<template>
	<view class="schedule-detail">
		<!-- 顶部信息 -->
		<view class="header">
			<view class="staff-info">
				<text class="name">{{staffInfo.name}}</text>
				<text class="department">{{staffInfo.department}}</text>
			</view>
		</view>
		
		<!-- 日历视图 -->
		<view class="calendar">
			<!-- 月份切换 -->
			<view class="month-switcher">
				<text class="prev" @click="changeMonth(-1)">上个月</text>
				<text class="current-month">{{currentYear}}年{{currentMonth + 1}}月</text>
				<text class="next" @click="changeMonth(1)">下个月</text>
			</view>
			
			<!-- 星期表头 -->
			<view class="week-header">
				<text v-for="day in weekDays" :key="day">{{day}}</text>
			</view>
			
			<!-- 日历格子 -->
			<view class="calendar-grid">
				<view class="calendar-cell" 
					v-for="(day, index) in calendarDays" 
					:key="index"
					:class="{'current-month': day.currentMonth, 'other-month': !day.currentMonth}">
					<text class="date">{{day.date}}</text>
					<text class="schedule-type" v-if="day.scheduleType">{{day.scheduleType}}</text>
				</view>
			</view>
		</view>
		
		<!-- 图例说明 -->
		<view class="legend">
			<view class="legend-item" v-for="(type, index) in scheduleTypes" :key="index">
				<view class="color-block" :style="{backgroundColor: type.color}"></view>
				<text>{{type.name}}</text>
			</view>
		</view>
		
		<!-- 操作按钮 -->
		<view class="action-buttons">
			<button class="btn primary" @click="handleEdit" v-if="canEdit">修改排班</button>
			<button class="btn secondary" @click="handleExchange">换班申请</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentYear: new Date().getFullYear(),
			currentMonth: new Date().getMonth(),
			weekDays: ['日', '一', '二', '三', '四', '五', '六'],
			staffInfo: {
				name: '',
				department: ''
			},
			scheduleTypes: [
				{ name: '早班', color: '#4CAF50' },
				{ name: '中班', color: '#2196F3' },
				{ name: '晚班', color: '#9C27B0' },
				{ name: '休息', color: '#9E9E9E' }
			],
			monthSchedule: [], // 存储当月排班数据
			canEdit: false
		}
	},
	computed: {
		calendarDays() {
			return this.generateCalendarDays();
		}
	},
	onLoad(options) {
		this.initData(options.id);
	},
	methods: {
		initData(scheduleId) {
			this.getMonthSchedule();
		},
		
		// 获取月度排班数据
		getMonthSchedule() {
			// TODO: 调用接口获取月度排班数据
			// 模拟数据
			this.staffInfo = {
				name: '张三',
				department: '内科'
			};
			// 这里应该从后端获取当月的排班数据
			this.monthSchedule = this.getMockScheduleData();
			this.canEdit = true;
		},
		
		// 生成日历数据
		generateCalendarDays() {
			const days = [];
			const firstDay = new Date(this.currentYear, this.currentMonth, 1);
			const lastDay = new Date(this.currentYear, this.currentMonth + 1, 0);
			
			// 填充上月剩余日期
			const firstDayWeekDay = firstDay.getDay();
			for (let i = firstDayWeekDay - 1; i >= 0; i--) {
				const date = new Date(firstDay);
				date.setDate(date.getDate() - i - 1);
				days.push({
					date: date.getDate(),
					currentMonth: false,
					scheduleType: this.getScheduleType(date)
				});
			}
			
			// 填充当月日期
			for (let i = 1; i <= lastDay.getDate(); i++) {
				const date = new Date(this.currentYear, this.currentMonth, i);
				days.push({
					date: i,
					currentMonth: true,
					scheduleType: this.getScheduleType(date)
				});
			}
			
			// 填充下月开始日期
			const lastDayWeekDay = lastDay.getDay();
			for (let i = 1; i < 7 - lastDayWeekDay; i++) {
				const date = new Date(lastDay);
				date.setDate(date.getDate() + i);
				days.push({
					date: date.getDate(),
					currentMonth: false,
					scheduleType: this.getScheduleType(date)
				});
			}
			
			return days;
		},
		
		// 获取指定日期的排班类型
		getScheduleType(date) {
			const dateStr = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
			const schedule = this.monthSchedule.find(s => s.date === dateStr);
			return schedule ? schedule.type : null;
		},
		
		// 切换月份
		changeMonth(offset) {
			const newDate = new Date(this.currentYear, this.currentMonth + offset, 1);
			this.currentYear = newDate.getFullYear();
			this.currentMonth = newDate.getMonth();
			this.getMonthSchedule();
		},
		
		// 模拟数据
		getMockScheduleData() {
			const data = [];
			const daysInMonth = new Date(this.currentYear, this.currentMonth + 1, 0).getDate();
			
			for (let i = 1; i <= daysInMonth; i++) {
				if (i % 4 === 0) {
					data.push({
						date: `${this.currentYear}-${this.currentMonth + 1}-${i}`,
						type: '早班'
					});
				} else if (i % 4 === 1) {
					data.push({
						date: `${this.currentYear}-${this.currentMonth + 1}-${i}`,
						type: '中班'
					});
				} else if (i % 4 === 2) {
					data.push({
						date: `${this.currentYear}-${this.currentMonth + 1}-${i}`,
						type: '晚班'
					});
				} else {
					data.push({
						date: `${this.currentYear}-${this.currentMonth + 1}-${i}`,
						type: '休息'
					});
				}
			}
			return data;
		},
		
		handleEdit() {
			uni.showToast({
				title: '修改排班',
				icon: 'none'
			});
		},
		
		handleExchange() {
			uni.showToast({
				title: '换班申请',
				icon: 'none'
			});
		}
	}
}
</script>

<style lang="scss">
.schedule-detail {
	padding: 20rpx;
	
	.header {
		margin-bottom: 30rpx;
		
		.staff-info {
			display: flex;
			align-items: center;
			gap: 20rpx;
			
			.name {
				font-size: 32rpx;
				font-weight: bold;
			}
			
			.department {
				font-size: 28rpx;
				color: #666;
			}
		}
	}
	
	.calendar {
		background: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
		
		.month-switcher {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;
			
			.current-month {
				font-size: 32rpx;
				font-weight: bold;
			}
			
			.prev, .next {
				color: #2196F3;
				padding: 10rpx 20rpx;
			}
		}
		
		.week-header {
			display: grid;
			grid-template-columns: repeat(7, 1fr);
			text-align: center;
			margin-bottom: 10rpx;
			
			text {
				padding: 10rpx;
				color: #666;
			}
		}
		
		.calendar-grid {
			display: grid;
			grid-template-columns: repeat(7, 1fr);
			gap: 2rpx;
			background: #f5f5f5;
			
			.calendar-cell {
				background: #fff;
				aspect-ratio: 1;
				padding: 10rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				
				&.other-month {
					opacity: 0.5;
				}
				
				.date {
					font-size: 24rpx;
					margin-bottom: 10rpx;
				}
				
				.schedule-type {
					font-size: 20rpx;
					padding: 4rpx 8rpx;
					border-radius: 4rpx;
					background: #e0e0e0;
				}
			}
		}
	}
	
	.legend {
		display: flex;
		justify-content: center;
		gap: 30rpx;
		margin-bottom: 30rpx;
		
		.legend-item {
			display: flex;
			align-items: center;
			gap: 10rpx;
			
			.color-block {
				width: 20rpx;
				height: 20rpx;
				border-radius: 4rpx;
			}
			
			text {
				font-size: 24rpx;
				color: #666;
			}
		}
	}
	
	.action-buttons {
		display: flex;
		gap: 20rpx;
		padding: 20rpx;
		
		.btn {
			flex: 1;
			padding: 20rpx;
			text-align: center;
			border-radius: 8rpx;
			font-size: 28rpx;
			
			&.primary {
				background: #2196F3;
				color: #fff;
			}
			
			&.secondary {
				background: #f5f5f5;
				color: #333;
			}
		}
	}
}
</style> 