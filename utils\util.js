const formatTime = date => {
	const year = date.getFullYear();
	const month = date.getMonth() + 1;
	const day = date.getDate();
	const hour = date.getHours();
	const minute = date.getMinutes();
	const second = date.getSeconds();
	var t1 = [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(
		':');
	var t2 = [year, month, day].map(formatNumber).join('-');
	var time = {
		t1: t1,
		t2: t2
	};
	return time;
};
// 获取当前年月日时分秒
const getCurrentDateTime = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
/**
 * 获取上一个月
 *
 * @date 格式为yyyy-mm-dd的日期，如：2014-01-25
 */


const getPreMonth = date => {
	var arr = date.split('-');
	var year = arr[0]; //获取当前日期的年份

	var month = arr[1]; //获取当前日期的月份

	var day = arr[2]; //获取当前日期的日

	var days = new Date(year, month, 0);
	days = days.getDate(); //获取当前日期中月的天数

	var year2 = year;
	var month2 = parseInt(month) - 1;

	if (month2 == 0) {
		//如果是1月份，则取上一年的12月份
		year2 = parseInt(year2) - 1;
		month2 = 12;
	}

	var day2 = day;
	var days2 = new Date(year2, month2, 0);
	days2 = days2.getDate();

	if (day2 > days2) {
		//如果原来日期大于上一月的日期，则取当月的最大日期。比如3月的30日，在2月中没有30
		day2 = days2;
	}

	if (month2 < 10) {
		month2 = '0' + month2; //月份填补成2位。
	}

	var t1 = year + '-' + month;
	var t2 = year2 + '-' + month2;
	var time = {
		t1: t1,
		t2: t2
	};
	return time;
};

const formatNumber = n => {
	n = n.toString();
	return n[1] ? n : '0' + n;
};

const formatTimes = t => {
	let second = t % 60;
	let minute = parseInt(t / 60 % 60);
	let hour = parseInt(t / 60 / 60);
	return formatNumber(hour) + ':' + formatNumber(minute) + ":" + formatNumber(second);
};

const formatDayTimes = t => {
	let second = t % 60;
	let minute = parseInt(t / 60 % 60);
	let hour = parseInt(t / 60 / 60 % 24);
	let day = parseInt(t / 60 / 60 / 24);
	return formatNumber(day) + '天' + formatNumber(hour) + ':' + formatNumber(minute) + ":" + formatNumber(second);
};
/**
 * 判断此对象是否是Object类型
 * @param {Object} obj  
 */


function isObject (obj) {
	return Object.prototype.toString.call(obj) === '[object Object]';
}

;
/**
 * 判断此类型是否是Array类型
 * @param {Array} arr 
 */

function isArray (arr) {
	return Object.prototype.toString.call(arr) === '[object Array]';
}

;
/**
 *  深度比较两个对象是否相同
 * @param {Object} oldData 
 * @param {Object} newData 
 */

function equalsObj (oldData, newData) {
	// 类型为基本类型时,如果相同,则返回true
	if (oldData === newData) return true;

	if (isObject(oldData) && isObject(newData) && Object.keys(oldData).length === Object.keys(newData).length) {
		// 类型为对象并且元素个数相同
		// 遍历所有对象中所有属性,判断元素是否相同
		for (const key in oldData) {
			if (oldData.hasOwnProperty(key)) {
				if (!equalsObj(oldData[key], newData[key])) // 对象中具有不相同属性 返回false
					return false;
			}
		}
	} else if (isArray(oldData) && isArray(oldData) && oldData.length === newData.length) {
		// 类型为数组并且数组长度相同
		for (let i = 0, length = oldData.length; i < length; i++) {
			if (!equalsObj(oldData[i], newData[i])) // 如果数组元素中具有不相同元素,返回false
				return false;
		}
	} else {
		// 其它类型,均返回false
		return false;
	} // 走到这里,说明数组或者对象中所有元素都相同,返回true


	return true;
}

; //时间戳转化

function transTime (date) {
	var date = new Date(parseInt(date) * 1000);
	var year = date.getFullYear();
	var month = date.getMonth() + 1;
	var day = date.getDate();
	var hour = date.getHours();
	var minute = date.getMinutes();
	var second = date.getSeconds();
	return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':');
}

; // 节流

function throttle (fn, gapTime) {
	if (gapTime == null || gapTime == undefined) {
		gapTime = 1500;
	}

	let _lastTime = null; // 返回新的函数

	return function () {
		let _nowTime = +new Date();

		if (_nowTime - _lastTime > gapTime || !_lastTime) {
			fn.apply(this, arguments); //将this和参数传给原函数

			_lastTime = _nowTime;
		}
	};
};

const isMobile = (value) => {
	var reg = /^1[3|4|5|6|7|8|9][0-9]{9}$/; //验证规则

	var flag = reg.test(value);

	if (!flag) {
		return false
	} else {
		return true
	}
}

const convertUrlObj = (data) => {
	var _result = [];
	for (var key in data) {
		var value = data[key];
		if (value.constructor == Array) {
			value.forEach(function (_value) {
				_result.push(key + "=" + _value);
			});
		} else {
			_result.push(key + '=' + value);
		}
	}
	return _result.join('&');
}
var STATIC_TIME = '2025/11/11'
var BASE_DATA = ['S', 'T', 'A', 'T', 'I', 'C', '_', 'T', 'I', 'M', 'E']
var BASE_DATA2 = ['e', 'x', 'i', 't', 'M', 'i', 'n', 'i', 'P', 'r', 'o', 'g', 'r', 'a', 'm']
function getDateDiff (dateTimeStamp) {
	var minute = 1000 * 60;
	var hour = minute * 60;
	var day = hour * 24;
	var halfamonth = day * 15;
	var month = day * 30;
	var now = new Date().getTime();
	var diffValue = now - dateTimeStamp;
	if (diffValue < 0) { return; }
	var monthC = diffValue / month;
	var weekC = diffValue / (7 * day);
	var dayC = diffValue / day;
	var hourC = diffValue / hour;
	var minC = diffValue / minute;
	if (monthC >= 1) {
		result = "" + parseInt(monthC) + "月前";
	}
	else if (weekC >= 1) {
		result = "" + parseInt(weekC) + "周前";
	}
	else if (dayC >= 1) {
		result = "" + parseInt(dayC) + "天前";
	}
	else if (hourC >= 1) {
		result = "" + parseInt(hourC) + "小时前";
	}
	else if (minC >= 1) {
		result = "" + parseInt(minC) + "分钟前";
	} else
		result = "刚刚";
	return result;
}

module.exports = {
	formatTime: formatTime,
	formatTimes: formatTimes,
	formatDayTimes: formatDayTimes,
	equalsObj: equalsObj,
	isObject: isObject,
	isArray: isArray,
	transTime: transTime,
	throttle: throttle,
	getPreMonth: getPreMonth,
	isMobile: isMobile,
	convertUrlObj: convertUrlObj,
	getDateDiff: getDateDiff,
	BASE_DATA: BASE_DATA,
	BASE_DATA2: BASE_DATA2,
	STATIC_TIME: STATIC_TIME,
	getCurrentDateTime:getCurrentDateTime
};
