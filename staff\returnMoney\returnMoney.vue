<template>
    <view class="card-replace">
        <!-- 顶部日期选择和筛选 -->
        <view class="header">
            <uni-datetime-picker v-model="date" type="daterange" @change="dateChange" />
        </view>
        <view class="application-list">
            <view class="application-item" v-for="(item, index) in applications" :key="index">
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe62b;</text>
                    <text class="label">会员id：</text>
                    <text class="value">{{ item.memId }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe64c;</text>
                    <text class="label">返现日期：</text>
                    <text class="value">{{ item.cashBackDate }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe656;</text>
                    <text class="label">返现金额：</text>
                    <text class="value">￥{{ item.cashBackMoney }}</text>
                </view>
                <!-- <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe77c;</text>
                    <text class="label">到期时间：</text>
                    <text class="value">{{ formData.oldPost }}</text>
                </view> -->
            </view>
            <view v-if="applications.length === 0" style="text-align: center;color: #999;">
                <image mode=""
                    src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png"
                    style="width: 372rpx;height: 312rpx;margin-top: 50rpx;">
                </image>
                <view style="margin-top: 10rpx;font-size: 28rpx;">暂无数据</view>
            </view>
        </view>
    </view>
</template>

<script>
const req = require("../../utils/request");

export default {
    data() {
        return {
            date: [],
            applications: [],
            pageNum: 1,
            total: 0,
        }
    },
    onLoad() {
        this.getData()
    },
    onReachBottom() {
        this.pageNum++
        this.getData()
    },
    methods: {
        dateChange(e) {
            this.date = e
            this.pageNum = 1
            this.$nextTick(() => {
                this.getData();
            })
        },
        getData() {
            req.showLoading();
            req.getRequest("/shopApi/goldCard/getInfo", {
                pageNum: this.pageNum,
                pageSize: 10,
                startTime: this.date[0],
                endTime: this.date[1],
                memId: req.getStorage('userInfo').id
            }, res => {
                uni.hideLoading();
                this.total = res.total
                if (this.pageNum === 1) {
                    this.applications = res.rows
                } else {
                    this.applications = [...this.applications, ...res.rows]
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped src="./returnMoney.scss"></style>
