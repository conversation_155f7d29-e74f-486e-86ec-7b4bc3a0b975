<template>
    <view class="inventory-check">



        <!-- 用户信息 -->
        <view class="user-info">
            <image class="avatar" :src="userInfo.avatar" mode="aspectFill"></image>
            <view class="info">
                <view class="name">{{ userInfo.memName }}</view>
                <view class="store">所属门店：{{ userInfo.storeName }}</view>
            </view>
        </view>

        <!-- 搜索框 -->
        <view class="search-container">
            <view class="search-box">
                <text class="iconfont" style="margin-right:10rpx;">&#xe621;</text>
                <input class="search-input" type="text" placeholder="请输入搜索信息" v-model="searchKeyword" />
                <text class="iconfont" @click="scanCode">&#xe697;</text>
            </view>
            <view class="search-btn" @click="getData">搜索</view>
        </view>

        <!-- 搜索结果下拉框 -->
        <view class="search-dropdown" v-if="showDropdown && goodsList.length > 0">
            <scroll-view scroll-y="true" class="dropdown-scroll" style="max-height:800rpx;">
                <view class="dropdown-item" v-for="(item, index) in goodsList" :key="index" @click="selectGoods(item)">
                    <view class="item-name">{{ item.goodsName || item.name }}</view>
                    <view class="item-spec">商品编码：{{ item.goodsNo }}</view>
                    <view class="item-spec">{{ item.doseSpec }} | {{ item.factory }}</view>
                </view>
            </scroll-view>
        </view>

        <!-- 商品基础信息 -->
        <view v-if="productInfo.goodsNo" class="section">
            <view class="section-header">
                <view class="section-title">商品基础信息</view>
            </view>
            <view class="product-info">
                <view class="info-row">
                    <text class="label">商品名称：</text>
                    <text class="value">{{ productInfo.goodsName }}</text>
                </view>
                <view class="info-row">
                    <text class="label">商品编码：</text>
                    <text class="value">{{ productInfo.goodsNo }}</text>
                </view>
                <view class="info-row">
                    <text class="label">商品通用名：</text>
                    <text class="value">{{ productInfo.commonName }}</text>
                </view>
                <view class="info-row">
                    <text class="label">商品单位：</text>
                    <text class="value">{{ productInfo.doseUnit }}</text>
                </view>
                <view class="info-row">
                    <text class="label">商品规格：</text>
                    <text class="value">{{ productInfo.doseSpec }}</text>
                </view>
                <view class="info-row">
                    <text class="label">生产厂家：</text>
                    <text class="value">{{ productInfo.factory }}</text>
                </view>
            </view>
        </view>

        <!-- 批号信息 -->
        <view style="padding-bottom:200rpx;">
            <view class="section" v-for="(item, index) in batchInfoList" :key="index">
                <view class="section-header">
                    <view class="section-title">批号信息</view>
                    <view v-if="item.isInv == 0" class="edit-btn"
                        @click="goUrl(`/other/addBatch/addBatch?obj=${encodeURIComponent(JSON.stringify(item))}&index=${index}`)">
                        编辑
                    </view>
                </view>
                <view class="batch-info">
                    <view class="info-row">
                        <text class="label">批　　号：</text>
                        <text class="value">{{ item.batchNumber }}</text>
                    </view>
                    <view class="info-row">
                        <text class="label">账面库存：</text>
                        <text class="value highlight">{{ item.quantity }}</text>
                    </view>
                    <view class="info-row">
                        <text class="label">盘点库存：</text>
                        <text class="value highlight">{{ item.invStock }}</text>
                    </view>
                    <view class="info-row">
                        <text class="label">生产日期：</text>
                        <text class="value">{{ item.productionDate }}</text>
                    </view>
                    <view class="info-row">
                        <text class="label">失效期：</text>
                        <text class="value">{{ item.until }}</text>
                    </view>
                </view>
            </view>
        </view>
        <!-- 底部按钮 -->
        <view class="bottom-actions" v-if="productInfo.goodsNo">
            <view class="action-btn submit" @click="submitInfo">提交信息</view>
            <view class="action-btn add" @click="goUrl('/other/addBatch/addBatch?goodsNo=' + productInfo.goodsNo)">添加批号
            </view>
        </view>
    </view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
    data() {
        return {
            id: "",
            searchKeyword: '',
            goodsList: [],
            batchInfoList: [],
            showDropdown: false,
            dropdownHeight: 400,
            userInfo: req.getStorage("userInfo") || {},
            productInfo: {},
            debounceTimer: null
        }
    },
    onLoad(options) {
        this.id = options.id
    },
    methods: {
        getData() {
            if (!this.searchKeyword) {
                this.showDropdown = false;
                return;
            }

            req.getRequest("/shopApi/inv/getGoodsDetail", {
                documentNum: this.id,
                search: this.searchKeyword
            }, res => {
                console.log(res);
                this.goodsList = res.data;
                this.showDropdown = this.goodsList.length > 0;
            })
        },
        selectGoods(item) {
            req.getRequest("/shopApi/inv/getBatchList", {
                documentNum: this.id,
                goodsNo: item.goodsNo
            }, res => {
                console.log(res);
                this.showDropdown = false;
                this.searchKeyword = '';
                this.batchInfoList = res.data
                this.productInfo = item
            })
        },
        goBack() {
            uni.navigateBack();
        },
        // 2129021
        scanCode() {
            uni.scanCode({
                success: (res) => {
                    console.log('扫码结果：', res);
                    this.searchKeyword = res.result;
                    this.getData();
                }
            });
        },
        editBatch() {
            uni.showToast({
                title: '编辑批号信息',
                icon: 'none'
            });
        },
        goUrl(url) {
            uni.navigateTo({
                url
            });
        },
        submitInfo() {
            req.showLoading()
            req.postRequest('/shopApi/inv/stocktaking', {
                documentNumber: this.id,
                goodsNo: this.productInfo.goodsNo,
                list: this.batchInfoList
            }, res => {
                uni.hideLoading()
                req.msg('提交成功')
                uni.navigateBack()
            })
        }
    }
}
</script>

<style lang="scss" scoped>
@import './inventoryCheck.scss';
</style>