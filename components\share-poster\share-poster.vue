<template>
    <view class="share-poster">
        <uni-popup ref="posterPopup" type="center">
            <view class="poster-container">
                <view class="poster-header">
                    <text class="poster-title">分享海报</text>
                    <text class="close-btn" @click="close">×</text>
                </view>
                <view class="poster-content">
                    <image v-if="posterUrl" :src="posterUrl" mode="widthFix" class="poster-image"></image>
                    <view v-else class="poster-loading">
                        <text>海报生成中...</text>
                    </view>
                </view>
                <view class="poster-footer">
                    <button class="poster-btn save-btn" @click="saveImage">保存到相册</button>
                    <!-- <button class="poster-btn share-btn" open-type="share">分享给朋友</button> -->
                </view>
            </view>
        </uni-popup>

        <!-- 用于生成海报的canvas，设置为不可见 -->
        <canvas canvas-id="posterCanvas" class="poster-canvas" style="width: 750px; height: 1200px;"></canvas>
    </view>
</template>

<script>
export default {
    name: 'SharePoster',
    props: {
        // 商品信息
        product: {
            type: Object,
            required: true
        },
        // 用户信息
        userInfo: {
            type: Object,
            default: () => ({})
        },
        // 小程序码
        qrcode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            posterUrl: '', // 生成的海报URL
            canvasWidth: 750, // canvas宽度
            canvasHeight: 1200 // canvas高度
        }
    },
    methods: {
        // 打开海报弹窗
        open() {
            this.$refs.posterPopup.open();
            // 如果没有生成过海报，则生成海报
            if (!this.posterUrl) {
                this.generatePoster();
            }
        },

        // 关闭海报弹窗
        close() {
            this.$refs.posterPopup.close();
        },

        // 生成海报
        async generatePoster() {
            try {
                uni.showLoading({
                    title: '生成中...',
                    mask: true
                });

                console.log('开始生成海报', this.product, this.qrcode);

                // 创建canvas上下文
                const ctx = uni.createCanvasContext('posterCanvas', this);

                // 设置背景色
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);

                // 绘制背景装饰
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, this.canvasWidth, 200);

                try {
                    // 等待加载商品图片
                    console.log('加载商品图片', this.product.img);
                    const productImage = await this.getImageInfo(this.product.img);
                    console.log('商品图片加载成功', productImage);

                    // 绘制商品图片
                    ctx.drawImage(productImage.path, 75, 100, 600, 600);
                } catch (error) {
                    console.error('绘制商品图片失败:', error);
                }

                // 绘制商品名称
                ctx.fillStyle = '#333333';
                ctx.font = 'bold 32px sans-serif';
                this.wrapText(ctx, this.product.goodsName || '商品名称', 75, 750, 600, 40);

                // 绘制价格
                ctx.fillStyle = '#EA1306';
                ctx.font = 'bold 48px sans-serif';
                ctx.fillText('¥' + (this.product.level1Price || '暂无信息'), 75, 860);

                // 如果有原价，绘制原价
                // if (this.product.salePrice > this.product.level1Price) {
                //     ctx.fillStyle = '#999999';
                //     ctx.font = '28px sans-serif';
                //     const originalPriceText = '¥' + this.product.salePrice;
                //     const metrics = ctx.measureText(originalPriceText);
                //     const originalPriceX = 75 + ctx.measureText('¥' + this.product.level1Price).width + 20;

                //     // 绘制删除线
                //     ctx.beginPath();
                //     ctx.moveTo(originalPriceX, 850);
                //     ctx.lineTo(originalPriceX + metrics.width, 850);
                //     ctx.strokeStyle = '#999999';
                //     ctx.lineWidth = 2;
                //     ctx.stroke();

                //     ctx.fillText(originalPriceText, originalPriceX, 860);
                // }

                // 绘制分割线
                ctx.beginPath();
                ctx.moveTo(75, 900);
                ctx.lineTo(675, 900);
                ctx.strokeStyle = '#EEEEEE';
                ctx.lineWidth = 2;
                ctx.stroke();

                try {
                    // 如果有用户信息，绘制用户头像和昵称
                    if (this.userInfo.avatar) {
                        const avatarUrl = this.userInfo.avatar;
                        console.log('加载用户头像', avatarUrl);
                        const avatarImage = await this.getImageInfo(avatarUrl);
                        console.log('用户头像加载成功', avatarImage);

                        // 绘制圆形头像
                        ctx.save();
                        ctx.beginPath();
                        ctx.arc(125, 970, 50, 0, Math.PI * 2, false);
                        ctx.clip();
                        ctx.drawImage(avatarImage.path, 75, 920, 100, 100);
                        ctx.restore();

                        // 绘制用户昵称
                        ctx.fillStyle = '#333333';
                        ctx.font = '28px sans-serif';
                        ctx.fillText(this.userInfo.nickName || '药店用户', 200, 970);
                    } else {
                        console.log('没有用户头像信息');
                        // 绘制默认用户信息
                        ctx.fillStyle = '#333333';
                        ctx.font = '28px sans-serif';
                        ctx.fillText('药店用户', 200, 970);
                    }
                } catch (error) {
                    console.error('绘制用户信息失败:', error);
                }

                // 绘制提示文本
                ctx.fillStyle = '#666666';
                ctx.font = '24px sans-serif';
                ctx.fillText('长按识别小程序码', 200, 1020);
                ctx.fillText('查看商品详情', 200, 1060);

                try {
                    // 如果有小程序码，绘制小程序码
                    if (this.qrcode) {
                        console.log('加载小程序码', this.qrcode);
                        const qrcodeImage = await this.getImageInfo(this.qrcode);
                        console.log('小程序码加载成功', qrcodeImage);
                        ctx.drawImage(qrcodeImage.path, 500, 900, 220, 220);
                    } else {
                        console.log('没有小程序码');
                    }
                } catch (error) {
                    console.error('绘制小程序码失败:', error);
                }

                // 绘制底部logo和文字
                ctx.fillStyle = '#999999';
                ctx.font = '24px sans-serif';
                ctx.fillText('药店小程序', 75, 1150);

                // 执行绘制
                ctx.draw(false, () => {
                    console.log('Canvas绘制完成，准备生成图片');
                    // 延迟生成图片，确保canvas已经绘制完成
                    setTimeout(() => {
                        // 将canvas转换为图片
                        uni.canvasToTempFilePath({
                            canvasId: 'posterCanvas',
                            success: (res) => {
                                console.log('海报生成成功', res);
                                this.posterUrl = res.tempFilePath;
                                uni.hideLoading();
                            },
                            fail: (err) => {
                                console.error('生成海报失败:', err);
                                uni.hideLoading();
                                uni.showToast({
                                    title: '生成海报失败',
                                    icon: 'none'
                                });
                            }
                        }, this);
                    }, 1000); // 延长等待时间，确保绘制完成
                });
            } catch (error) {
                console.error('生成海报出错:', error);
                uni.hideLoading();
                uni.showToast({
                    title: '生成海报失败',
                    icon: 'none'
                });
            }
        },

        // 获取图片信息
        getImageInfo(src) {
            return new Promise((resolve, reject) => {

                // 确保图片路径是完整的URL
                let imgSrc = src;
                if (imgSrc.indexOf('http') !== 0 && imgSrc.indexOf('https') !== 0 && imgSrc.indexOf('wxfile') !== 0) {
                    // 如果不是完整URL，可能是相对路径，添加域名前缀
                    if (imgSrc.startsWith('/')) {
                        imgSrc = imgSrc.substring(1);
                    }
                    imgSrc = 'https://jht-image.oss-cn-shenzhen.aliyuncs.com/' + imgSrc;
                }

                uni.getImageInfo({
                    src: imgSrc,
                    success: resolve,
                });
            });
        },

        // 文本换行处理
        wrapText(ctx, text, x, y, maxWidth, lineHeight) {
            if (!text) return;

            // 将文本按空格分割
            const words = text.split('');
            let line = '';
            let testLine = '';
            let lineCount = 1;

            for (let i = 0; i < words.length; i++) {
                testLine += words[i];
                const metrics = ctx.measureText(testLine);
                const testWidth = metrics.width;

                if (testWidth > maxWidth && i > 0) {
                    ctx.fillText(line, x, y);
                    line = words[i];
                    testLine = words[i];
                    y += lineHeight;
                    lineCount++;

                    // 最多显示两行，超出部分用省略号表示
                    if (lineCount > 2) {
                        line = line.substring(0, line.length - 1) + '...';
                        ctx.fillText(line, x, y);
                        break;
                    }
                } else {
                    line = testLine;
                }
            }

            // 绘制最后一行
            if (lineCount <= 2) {
                ctx.fillText(line, x, y);
            }
        },

        // 保存图片到相册
        saveImage() {
            if (!this.posterUrl) {
                uni.showToast({
                    title: '海报生成中，请稍候',
                    icon: 'none'
                });
                return;
            }

            uni.saveImageToPhotosAlbum({
                filePath: this.posterUrl,
                success: () => {
                    uni.showToast({
                        title: '保存成功',
                        icon: 'success'
                    });
                },
                fail: (err) => {
                    console.log(err);
                    if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                        uni.showModal({
                            title: '提示',
                            content: '请授权保存图片到相册',
                            success: (res) => {
                                if (res.confirm) {
                                    uni.openSetting();
                                }
                            }
                        });
                    } else {
                        uni.showToast({
                            title: '保存失败',
                            icon: 'none'
                        });
                    }
                }
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.share-poster {
    .poster-canvas {
        position: fixed;
        left: -9999rpx;
        width: 750rpx;
        height: 1200rpx;
    }

    .poster-container {
        width: 650rpx;
        background-color: #fff;
        border-radius: 24rpx;
        overflow: hidden;
    }

    .poster-header {
        position: relative;
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #f5f5f5;

        .poster-title {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
        }

        .close-btn {
            position: absolute;
            right: 30rpx;
            top: 50%;
            transform: translateY(-50%);
            font-size: 48rpx;
            color: #999;
            line-height: 1;
        }
    }

    .poster-content {
        padding: 30rpx;

        .poster-image {
            width: 100%;
            border-radius: 16rpx;
            box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
        }

        .poster-loading {
            height: 800rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 28rpx;
        }
    }

    .poster-footer {
        display: flex;
        padding: 20rpx 30rpx 40rpx;

        .poster-btn {
            flex: 1;
            height: 80rpx;
            line-height: 80rpx;
            text-align: center;
            border-radius: 40rpx;
            font-size: 28rpx;
            margin: 0 15rpx;
        }

        .save-btn {
            background-color: #f8f8f8;
            color: #333;
        }

        .share-btn {
            background: linear-gradient(90deg, #EA1306, #FF5F00);
            color: #fff;
        }

        .share-btn::after {
            border: none;
        }
    }
}
</style>