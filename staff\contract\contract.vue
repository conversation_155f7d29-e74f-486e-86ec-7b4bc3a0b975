<template>
	<view class="contract_content">
		<view class="contract_container" v-if="!contractInfo">
			<view class="container_info" @click="getContractInfo(1)">
				<img class="info_image"
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241016/925ccd876019453f991f71ff6a3367f7.png"
					alt="">
				<span class="info_title">试用期合同:</span>
				<span class="info_name">试用期合同.docx</span>
			</view>
			<view class="container_info" @click="getContractInfo(3)">
				<img class="info_image"
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241016/770aff2fcc8d4caabe942ed63496ae33.png"
					alt="">
				<span class="info_title">保密协议:</span>
				<span class="info_name">保密协议.docx</span>
			</view>
			<view class="container_info" v-if="isBecome !== 0" @click="getContractInfo(2)">
				<img class="info_image"
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241016/4e30b021272f43d4bb03e722d081a155.png"
					alt="">
				<span class="info_title">劳动合同:</span>
				<span class="info_name">劳动合同.docx</span>
			</view>
		</view>
	</view>
</template>

<script>
const req = require("../../utils/request");
export default {
	data() {
		return {
			isBecome: 0,
			contractInfo: false,
			content: '',
			uid: req.getStorage('uid'),
			isHavaSign: false,
			showCanvas: false,
			isDrawing: false,
			lastX: 0,
			lastY: 0,
			canvas: null,
			ctx: {}
		}
	},
	onLoad() {
		this.getIsTest()
	},
	methods: {


		getIsTest() {
			req.getRequest('/mall-system/staff/isTest', {
				name: req.getStorage('userInfo').staffName
			}, res => {
				this.isBecome = res.data.isTest
				if (res.data.isHavaSign) {
					this.isHavaSign = false
				}
			})
		},
		// 获取合同详细内容
		getContractInfo(type) {
			const userInfo = req.getStorage('userInfo');
			const obj = {
				url: `https://staffnew.gzjihetang.com/pages/contractDetail/contractDetail`,
				type: type,
				name: userInfo.staffName
			};
			uni.navigateTo({
				url: `/staff/contractDetail/contractDetail?obj=${JSON.stringify(obj)}`
			})
		},
	}
}
</script>

<style lang="scss">
.contract_content {
	min-height: 100vh;
	background-color: #f5f7fa;
	padding: 16px;
}

.contract_container {
	padding: 20px;
	border-radius: 8px;
	background: #FFFFFF;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.container_info {
	margin-bottom: 16px;
	padding: 16px;
	border-radius: 6px;
	background: #fff;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	border: 1px solid #ebeef5;
}

.container_info:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
	border-color: #e6e6e6;
}

.container_info:active {
	transform: translateY(0);
}

.container_info:last-child {
	margin-bottom: 0;
}

.info_image {
	width: 20px;
	height: 20px;
	flex-shrink: 0;
}

.info_title {
	margin-left: 12px;
	font-weight: 500;
	color: #303133;
	font-size: 15px;
}

.info_name {
	margin-left: 8px;
	color: #606266;
	font-size: 14px;
	border-bottom: 1px dashed #909399;
	padding-bottom: 2px;
	transition: all 0.3s ease;
}

.info_name:hover {
	color: #409EFF;
	border-bottom-color: #409EFF;
}

.submit {
	margin: 24px auto;
	width: 240px;
	height: 44px !important;
	line-height: 44px !important;
	background: linear-gradient(135deg, #409EFF, #28a4fe);
	font-size: 16px;
	color: #fff;
	text-align: center;
	border-radius: 22px !important;
	transition: all 0.3s ease;
}

.submit:active {
	transform: scale(0.98);
	opacity: 0.9;
}

.header {
	padding: 12px 16px;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	display: flex;
	background: rgba(248, 248, 248, 0.95);
	backdrop-filter: blur(10px);
	z-index: 999;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.page_head__title {
	display: inline-block;
	width: 80vw;
	font-weight: 600;
	line-height: 32px;
	text-align: center;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	color: #303133;
	font-size: 16px;
}
</style>
