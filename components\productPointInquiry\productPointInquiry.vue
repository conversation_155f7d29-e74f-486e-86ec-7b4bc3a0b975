<template>
  <view class="box">
    <view class="top">
      <view class="searchs">
        <image src="/static/image/ssico.png" class="ssico"></image>
        <input placeholder="请输入商品名称" v-model="searchInp" @input='confirmSearch' @confirm='confirmSearch' width="100%"
          clearable>
      </view>
    </view>

    <view class="middle">
      <view class="horizontal-scroll">
        <view v-for="(item, index) in titleList" :key="index" class="scroll-item">
          <img :src="item.icon" alt="" @click="titClick(item)">
          <view class="scroll-item-text" @click="titClick(item)">
            <text class="name">{{ item.name }}</text>
            <text class="size">({{ item.size }})</text>
          </view>
        </view>
      </view>
    </view>

    <view class="content">
      <scroll-view class="con_left" scroll-y lower-threshold="200" style="height:333px;">
        <view @click="itemClick(item, index)" :class="'li ' + (currents == index ? 'active' : '')"
          v-for="(item, index) in nameList" :key="index">
          <view @click.stop="addCart2(item)" class="addCart2" v-if="item.isMedical == 1">
            <image style="width:100%;height:100%"
              src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240530/92d09d2f4e6b4ee2a879604c4cb7d059.png">
            </image>
          </view>
          <view class="item2">
            <view class="item_name">{{ item.name }}</view>
            <view class="badge">{{ item.size }}</view>
          </view>
        </view>
      </scroll-view>

      <view class="con_right">
        <view class="right_top">
          <scroll-view scroll-x class="right_topTitle" style="white-space: nowrap; overflow: hidden;">
            <view v-for="(item, index) in level4List" :key="index" @click="level4Click(item, index)"
              :class="'li ' + (currents2 == index ? 'active' : '')" style="display: inline-block; margin-right: 10px;">
              <view>{{ item.name }}</view>
            </view>
          </scroll-view>
        </view>

        <view class="right_bottom">
          <scroll-view scroll-y lower-threshold="200" :style="'height:' + (systemInfo.windowHeight - 120) + 'px'"
            class="sort">
            <block v-for="(item, index) in pageList" :key="index">
              <view class="li dflex">
                <view class="proimgs" @tap="toDetail" :data-id="item.id">
                  <image :class="item.isOtc == 1 ? 'mohu' : ''" :src="item.img" mode="widthFix" class="proimg"
                    lazy-load="true"></image>
                  <view style="display: flex; position: absolute;top:0;left:0">
                    <image style="width:70rpx;margin-right:20rpx;" mode="widthFix"
                      src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240705/e08f09a6468d41218bd672d8009aef61.png"
                      v-if="item.isOverallPlanning == 1">
                    </image>
                    <image style="width:70rpx;" mode="widthFix"
                      src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240328/49c1d755e73f40179632a63dab288454.png"
                      v-if="item.isMedicalInsurance == 1">
                    </image>
                  </view>
                  <!-- <image style="position: absolute;top:0;left:0;width:160rpx;"
                         v-if="item.isOtc == 1"
                         src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240111/a338c782689a421b8e9ab0848c3c5c10.png"
                         mode="widthFix"></image> -->
                </view>
                <view class="flex">
                  <view class="proname" @tap="toDetail" :data-id="item.id">
                    <image v-if="item.isOtc == 1" style="width:36rpx;float: left;margin-right:5rpx;"
                      :class="systemInfo.osName == 'ios' ? 'ios' : 'android'"
                      src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240705/d3354d55f26542a69cad42a63db6850a.png"
                      mode="widthFix"></image>
                    <image v-else-if="item.isOtc == 6" style="width:46rpx;float: left;margin-right:5rpx;"
                      :class="systemInfo.osName == 'ios' ? 'ios' : 'android'"
                      src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240705/aff95e478eae4c8ea040434419fa775a.png"
                      mode="widthFix"></image>
                    <view>{{ item.goodsName }}</view>
                  </view>
                  <view class="operate dflex">
                    <view class="price">
                      ￥<text>{{ item.overplainMoney }}</text>
                    </view>

                    <view v-if="item.isMedical == 0" @click="addCart(item)" class="add-cart">
                      <image mode="widthFix"
                        src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240530/92d09d2f4e6b4ee2a879604c4cb7d059.png">
                      </image>
                    </view>
                  </view>
                  <view style="color:#0B78FF">预估积分：{{ item.distriDirectAmount2 }}</view>
                  <view style="color:#cccccc">门店库存：{{ item.saleStock }}</view>
                </view>
              </view>
            </block>
          </scroll-view>
        </view>
      </view>
    </view>

    <view class="bottom">
      <view style="margin-left:20px">已选{{ proNumber }}件</view>
      <view style="margin-right:20px">
        <button class="mini-btn" type="primary" plain="true" size="mini" @click="submitBtn">提交</button>
      </view>
    </view>
  </view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
  data() {
    return {
      doctorId: req.getStorage('uid'), //医生id
      timer: '',
      searchInp: '',
      systemInfo: {},
      currents: -1,
      currents2: -1,
      titleList: [],
      nameList: [],
      goodsList: [],
      level4List: [],
      level2: '',
      level3: '',
      level4: '',

      pageList: [],
      proNumber: 0,

    }
  },
  props: {
    UserID: String,
    regID: String,
    mode: Number,
    merchantId: String
  },
  mounted() {
    uni.getSystemInfo({
      success: res => {
        this.systemInfo = res
      }
    })
    this.getTitle()
  },
  methods: {
    confirmSearch(e) {
      this.name = e.detail.value
      this.level2 = ''
      this.level3 = ''
      this.level4 = ''
      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.nameList = []
        this.level4List = []
        this.getList()
      }, 1000);
    },

    // 获取头部
    getTitle() {
      this.level2 = ''
      req.getRequest('/shopApi/shopEmployee/secondList', { merchantId: this.merchantId }, res => {
        this.titleList = res.data
        if (res.data.length > 0) {
          this.level2 = res.data[0].name
        }
        let item = res.data[0]
        this.titClick(item)
      })
    },
    // 头部点击
    titClick(item) {
      console.log(item);
      this.currents = -1
      // this.currents2 = 0
      this.level2 = item
      this.level3 = ''
      this.level4 = ''
      this.searchInp = ''
      this.level4List = []
      req.getRequest('/shopApi/shopEmployee/thirdList', { level2: this.level2.name, id: this.level2.id, merchantId: this.merchantId }, res => {
        console.log('级别', res.data);
        this.nameList = res.data
        if (res.data.length > 0) {
          this.level3 = res.data[0]
        }
      })
      this.getList()
    },
    // 左侧点击
    itemClick(item, index) {
      console.log('item', item);
      this.level3 = item
      this.level4 = ''
      // this.currents = -1
      this.level4List = []
      this.searchInp = ''
      req.getRequest('/shopApi/shopEmployee/fourList', { level3Id: this.level3.level3 }, res => {
        this.currents = index
        this.level4List = res.data
        if (res.data.length > 0) {
          this.level4 = res.data[0].name
        }
        this.getList()
      })
    },

    level4Click(item, index) {
      console.log('4级', item);
      this.currents2 = index
      this.level4 = item
      this.searchInp = ''
      this.getList()
    },

    //获取商品
    getList() {
      this.pageList = []
      let from = {
        level2: this.level2.name,
        level3: this.level3.name,
        level2Id: this.level2.id,
        level3Id: this.level3.level3,
        level4Id: this.level4.id,
        name: this.searchInp,
        merchantId: this.merchantId
      }
      req.getRequest('/shopApi/shopEmployee/pointsListProduct', from, res => {
        this.pageList = res.data
      })
    },
    //加入购物车
    addCart(item) {
      console.log(111,item);
      if (!item.saleStock) {
        uni.showLoading({
          title: '此产品无库存'
        })
        setTimeout(() => {
          uni.hideLoading(); // 加载完成后隐藏加载提示框
        }, 1000);
        return
      }
      let params = {
        registrationId: this.regID, //订单id
        quantity: 1, //数量
        doctorId: this.doctorId, //医生id
        uid: this.UserID, //患者id
        productId: item.id, //商品id
        merchantId: this.merchantId,
        state: 3,
        purchaseNo: item.pushNo
      }
      req.postRequest('/shopApi/shopEmployee/addCartUser', params, res => {
        console.log('中药购物车', res);
        if (res.data != '') {
          req.msg('加入购物车成功')
        }
        this.getOutpatientInfo()
      })
    },

    //医嘱加购物车
    addCart2(item) {
      if (!item.stock) {
        uni.showLoading({
          title: '此产品无库存'
        })
        setTimeout(() => {
          uni.hideLoading(); // 加载完成后隐藏加载提示框
        }, 1000);
        return
      }
      let params = {
        registrationId: this.regID, //订单id
        doctorId: this.doctorId, //医生id
        name: item.name,
        uid: this.UserID,
        type: this.level2.name,
        merchantId: this.merchantId,
      }
      if (this.regID) {
        params.registrationId = this.regID
      } else {
        params.registrationId = ''
      }
      req.postRequest("/shopApi/shopEmployee/collocationCart", params, (res) => {

        if (res.data) {
          req.msg('加入购物车成功')
        }
        if (this.regID) {
          console.log('this.regID', res.data)
          if (res.data && res.data != 1) {
            let form2 = {
              id: this.regID,
              chiefComplaint: res.data.chiefComplaint,
              historyOfPresentIllness: res.data.historyPresentIllness,
              diagnosis: res.data.diagnosis,
              usages: res.data.internal,
              fesco: res.data.external,
              dose: res.data.dose
            }
            req.postRequest('/shopApi/shopEmployee/updateRegistration', form2, (res) => {

            })
          }
        }
      });
    },

    // 获取用药列表
    getOutpatientInfo() {
      let uid = this.UserID
      let merchantId = this.merchantId
      req.getRequest('/shopApi/shopEmployee/getCart', { registrationId: this.regID }, res => {
        console.log('用药列表', res);
        this.proNumber = res.data.length
      })
    },

    submitBtn() {
      this.$emit('child-event', { productPointOpen: false })
    },
  }
}
</script>

<style>
@import url("./productPointInquiry.css");
</style>