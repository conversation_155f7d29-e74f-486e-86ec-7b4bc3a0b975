## 1.2.3（2024-11-02）
- [修复] 组件重新渲染时未重置待渲染位置的问题
- [优化] 图片宽度支持 px 单位
## 1.2.2（2024-10-18）
- [修复] 解决了因为数据id重复导致的 小程序平台  waterfall-item 组件不会重新渲染的问题
## 1.2.1（2024-10-07）
全新升级为组件+插槽的方案实现，极大的简便了组件的使用和内容调整。
## 1.1.6（2022-10-24）
修复 初始化渲染变量取值错误问题
## 1.1.5（2022-10-24）
初始化渲染增加状态条件判断
## 1.1.4（2022-10-24）
修改空数据提示
## 1.1.3（2022-10-24）
1、增加内容插槽

2、删除状态文本属性

3、组件创建时可触发渲染条件
## 1.1.2（2022-09-26）
修改了开启布局的判断条件
## 1.1.1（2022-08-28）
1、加强组件化封装

2、 完善注释和优化使用逻辑
## 1.1.0（2022-08-22）
重写渲染列表逻辑
## 1.0.1（2021-06-08）
修改插入方向计算方式