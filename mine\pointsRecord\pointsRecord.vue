<template>
    <view class="points-record">
        <template v-if="recordList.length">
            <view class="record-item" v-for="item in recordList" :key="item.id">
                <view class="header">
                    <view class="left">
                        <view class="title">
                            <view>{{ item.goodsName }}</view>
                            <view>X{{ item.count }}</view>
                        </view>
                        <view class="time">{{ item.createDate }}</view>
                    </view>
                </view>
                <view class="content">
                    <image :src="item.img" mode="widthFix" style="width: 150rpx;"></image>
                </view>
            </view>
        </template>

        <view class="empty" v-else>
            <image src="/static/images/empty/empty-points.png" mode="aspectFit"></image>
            <view class="empty-text">暂无积分记录~</view>
        </view>

        <view class="load-more" v-if="recordList.length">
            <text v-if="loading">加载中...</text>
            <text v-else-if="isEnd">没有更多了</text>
        </view>
    </view>
</template>

<script>
const req = require('../../utils/request')
export default {
    data() {
        return {
            page: 1,
            recordList: [],
            isEnd: false,
            loading: false
        }
    },

    onLoad() {
        this.getData()
    },

    onReachBottom() {
        if (!this.isEnd && !this.loading) {
            this.page++
            this.getData()
        }
    },

    methods: {
        getData() {
            if (this.loading) return
            this.loading = true

            req.getRequest("/shopApi/exchangeRecords/pageList", {
                pageNum: this.page,
                pageSize: 10
            }, res => {
                const { items, total } = res.data

                if (this.page === 1) {
                    this.recordList = items
                } else {
                    this.recordList = [...this.recordList, ...items]
                }

                this.isEnd = this.recordList.length >= total
                this.loading = false
            })
        }
    }
}
</script>

<style lang="scss">
@import './pointsRecord.scss';
</style>