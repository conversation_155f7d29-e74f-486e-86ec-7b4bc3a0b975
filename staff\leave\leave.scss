/deep/ .header .uni-date__x-input,
/deep/ .header .uni-select__selector-empty,
/deep/ .header .uni-select__selector-item {
  font-size: 20rpx !important;
}

.card-replace {
  min-height: 100vh;
  background-color: #f5f7fa;

  .header {
    background: #fff;
    // margin: 0 20rpx;
    padding: 20rpx 15rpx;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 20rpx;
    position: relative;

    .add-btn {
      background: #fef0f0;
      color: #f23030;
      padding: 8px 16px;
      border-radius: 4px;
    }
  }

  .application-list {
    padding: 15rpx 30rpx;

    .application-item {
      background: #fff;
      border-radius: 8px;
      padding: 80rpx 15rpx 20rpx;
      margin-bottom: 15rpx;
      position: relative;

      .status-tag {
        position: absolute;
        top: 0;
        left: 0;
        padding: 4px 12px;
        color: #fff;
        font-size: 14px;
        border-radius: 8px 0 8px 0;
      }

      .info-row {
        margin-bottom: 8px;
        display: flex;
        align-items: center;

        .label {
          color: #909399;
          font-size: 14px;
          width: 80px;
        }

        .value {
          color: #303133;
          font-size: 14px;
          flex: 1;
        }
      }
    }
  }
}

.popup-content {
  background: #fff;
  border-radius: 10px 10px 0 0;

  .popup-header {
    position: relative;
    padding: 16rpx;
    text-align: center;
    border-bottom: 1px solid #eee;
    background: #ea1306;
    border-radius: 10px 10px 0 0;
    color: #fff;
    text {
      font-size: 16px;
      font-weight: 500;
    }

    .close-icon {
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 24px;
      color: #fff;
      padding: 8rpx;
    }
  }
  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }
  .popup-body {
    padding: 20px;
    max-height: 800rpx;
    overflow: scroll;
    .form-item {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      position: relative;

      .required {
        color: #f23030;
        margin-right: 5px;
      }

      .label {
        width: 80px;
        font-size: 14px;
        color: #333;
        margin-right: 10px;
      }

      .input,
      .textarea,
      .date-input,
      .select-input,
      .selectleader {
        flex: 1;
        border: 1px solid red;
        border-radius: 4px;
        font-size: 14px;
        color: #333;
        background: #fff;
      }

      .input {
        height: 40px;
        padding: 0 12px;
      }

      .textarea {
        height: 80px;
        padding: 10px 12px;
      }
      /deep/.uni-select {
        border: 0;
      }
      .date-input {
        height: 40px;
        padding-right: 12px;
        display: flex;
        align-items: center;
        // justify-content: space-between;

        .calendar-icon,
        .arrow-down {
          color: #999;
          font-size: 14px;
        }
      }

      .leader-list {
        position: absolute;
        top: 42px;
        left: 95px;
        right: 0;
        max-height: 300rpx;
        background: #fff;
        border: 1px solid #eee;
        border-radius: 4px;
        overflow-y: auto;
        z-index: 999;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

        .leader-item {
          padding: 20rpx;
          border-bottom: 1px solid #eee;
          cursor: pointer;

          &:hover {
            background: #f5f5f5;
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }

    .form-footer {
      display: flex;
      justify-content: space-between;
      gap: 20rpx;
      margin-top: 60rpx;
      padding: 0 30rpx 40rpx;

      .btn {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 8rpx;
        font-size: 32rpx;
        text-align: center;

        &.cancel {
          background: #f7f7f7;
          color: #333;
        }

        &.save {
          background: #f23030;
          color: #fff;
        }
      }
    }
  }
}

.popup-content1 {
  background: #fff;
  border-radius: 10px 10px 0 0;

  .popup-header {
    position: relative;
    padding: 20rpx 30px;
    text-align: center;
    border-bottom: 1px solid #eee;
    background-color: #ea1306;
    border-radius: 10px 10px 0 0;
    color: #fff;

    text {
      font-size: 16px;
      font-weight: 500;
    }

    .close-icon {
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 24px;
      color: #fff;
      padding: 5px;
    }
  }
  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }
  .popup-body {
    padding: 20px;
    max-height: 800rpx;
    overflow: scroll;
    /* Step Container */
    .step-container {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      // width: 200px;
    }

    /* Individual Step */
    .step {
      display: flex;
      align-items: center;
      margin: 10px 0;
      position: relative;
      padding-left: 15rpx;
    }

    /* Step Number */
    .step-number {
      width: 10px;
      height: 10px;
      border: 5px solid #ea1306;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
    }

    /* Step Title */
    .step-title {
      margin-left: 10px;
      font-size: 14px;
      color: #333;
    }

    /* Line Between Steps (Dashed) */
    .step::after {
      content: "";
      position: absolute;
      top: 68%;
      left: 32rpx;
      width: 2px;
      height: 90%;
      border-left: 2px dashed #ea1306;
      // /* Dashed Line */
      // z-index: -1;
    }

    /* Remove line for the last step */
    .step:last-child::after {
      display: none;
    }
  }
}
