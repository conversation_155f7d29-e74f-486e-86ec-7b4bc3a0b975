<template>
    <view @click.stop>
        <uni-popup ref="popup" type="bottom">
            <view class="popup-content"
                :style="{ 'padding-bottom': bottom ? 'calc(154rpx + env(safe-area-inset-bottom))' : '' }">
                <!-- 商品信息 -->
                <view class="popup-header">
                    <view style='position: relative;'>
                        <image :src="product.image" mode="aspectFill"
                            :class="{ 'mohu': product.type === 'prescription_drug' }"></image>
                        <image v-if="product.type == 'prescription_drug'"
                            style="position: absolute;top:0;left:0;width: 100%;" mode="widthFix"
                            src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240111/a338c782689a421b8e9ab0848c3c5c10.png">
                        </image>
                    </view>
                    <view class="popup-product-info">
                        <view class="popup-price">
                            ¥{{ product.price }}
                            <text class="original-price"
                                v-if="product.originalPrice && product.originalPrice > product.price">
                                ¥{{ product.originalPrice }}
                            </text>
                        </view>
                        <view class="popup-stock">
                            库存: {{ deliveryType === 2 ? cloudStock : storeStock }}件
                        </view>
                    </view>
                    <text class="close-icon iconfont" @click.stop="close">&#xeb6a;</text>
                </view>

                <!-- 配送方式 -->
                <view class="delivery-section">
                    <view class="section-title">配送方式</view>
                    <view class="delivery-options">
                        <view class="delivery-option" :class="{ active: localDeliveryType === 1 }"
                            @click.stop="selectDelivery(1)">
                            <text>同城闪送/自提</text>
                            <text class="delivery-badge">配送到家</text>
                        </view>
                        <view class="delivery-option" :class="{
                            active: localDeliveryType === 2,
                            disabled: currentStore.id != 60
                        }" @click.stop="currentStore.id == 60 ? selectDelivery(2) : null">
                            <text>快递配送</text>
                            <text class="delivery-badge">3天内发货</text>
                        </view>
                    </view>
                </view>

                <!-- 购买数量 -->
                <view class="quantity-section">
                    <view class="section-title">购买数量</view>
                    <view class="quantity-control">
                        <text class="minus" :class="{ disabled: localQuantity <= 1 }"
                            @click.stop="updateQuantity('minus')">-</text>
                        <input type="number" v-model="localQuantity" />
                        <text class="plus" :class="{ disabled: localQuantity >= maxStock }"
                            @click.stop="updateQuantity('plus')">+</text>
                    </view>
                </view>

                <!-- 确认按钮 -->
                <view class="confirm-btn" @click.stop="confirm">
                    {{ isCartAction ? '加入购物车' : '立即购买' }}
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const req = require('@/utils/request')
export default {
    name: 'BuyPopup',
    props: {
        // 商品信息
        product: {
            required: true,
            validator: function (obj) {
                // 修改验证逻辑：检查对象是否存在且包含必要属性
                return obj && (
                    // 如果没有 maxBuy 属性也是有效的
                    !obj.hasOwnProperty('maxBuy') ||
                    // 如果有 maxBuy 属性，确保它是数字且大于等于 0
                    (typeof obj.maxBuy === 'number' && obj.maxBuy >= 0)
                )
            }
        },
        // 是否为加入购物车操作
        isCartAction: {
            type: Boolean,
            default: true
        },
        // 云仓库存
        cloudStock: {
            default: 0
        },
        // 门店库存
        storeStock: {
            default: 0
        },
        // 距离底部距离
        bottom: {
            type: Boolean,
            default: false
        },
        deliveryType: {
            type: Number,
            default: 1
        },
        quantity: {
            type: Number,
            default: 1
        }
    },
    data() {
        return {
            currentStore: req.getStorage('currentStore'),
            // localDeliveryType: this.deliveryType,
            localDeliveryType: req.getStorage('currentStore').id == 60 ? this.deliveryType : 1,
            localQuantity: this.quantity
        }
    },
    computed: {
        maxStock() {
            // 首先获取当前配送方式的库存
            const currentStock = this.localDeliveryType === 2 ? this.cloudStock : this.storeStock
            // 如果设置了最大购买量，则取较小值
            return this.product.maxBuy ? Math.min(currentStock, this.product.maxBuy) : currentStock
        }
    },
    watch: {
        // 监听props变化更新本地数据
        deliveryType(newVal) {
            // 如果门店ID不是60，则强制使用同城配送
            if (this.currentStore.id != 60 && newVal === 2) {
                this.localDeliveryType = 1;
            } else {
                this.localDeliveryType = newVal;
            }
        },
        quantity(newVal) {
            this.localQuantity = newVal;
        }
    },
    created() {
        // 组件创建时确保配送方式符合门店限制
        if (this.currentStore.id != 60 && this.localDeliveryType === 2) {
            this.localDeliveryType = 1;
        }
    },
    methods: {
        // 打开弹窗
        open() {
            this.$refs.popup.open()
        },
        // 关闭弹窗
        close() {
            this.$refs.popup.close()
        },
        // 选择配送方式
        selectDelivery(type) {
            // 如果选择快递配送但门店ID不是60，则不允许选择
            if (type === 2 && this.currentStore.id != 60) {
                uni.showToast({
                    title: '当前门店不支持快递配送',
                    icon: 'none'
                })
                return
            }

            this.localDeliveryType = type
            this.localQuantity = 1
        },
        // 更新购买数量
        updateQuantity(action) {
            if (action === 'minus' && this.localQuantity > 1) {
                this.localQuantity--
            } else if (action === 'plus') {
                // 检查是否达到最大购买量
                if (this.localQuantity < this.maxStock) {
                    this.localQuantity++
                } else {
                    // 如果达到最大购买量，显示提示
                    const message = this.product.maxBuy && this.localQuantity >= this.product.maxBuy
                        ? `该商品最多可购买${this.product.maxBuy}件`
                        : '库存不足'
                    uni.showToast({
                        title: message,
                        icon: 'none'
                    })
                }
            }
        },
        // 确认购买
        confirm() {
            const currentStock = this.maxStock
            if (this.localQuantity > currentStock) {
                const message = this.product.maxBuy && this.localQuantity > this.product.maxBuy
                    ? `该商品最多可购买${this.product.maxBuy}件`
                    : '库存不足'
                uni.showToast({
                    title: message,
                    icon: 'none'
                })
                return
            }
            this.$emit('confirm', {
                quantity: this.localQuantity,
                deliveryType: this.localDeliveryType,
                isCartAction: this.isCartAction
            })
            this.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.popup-content {
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding: 40rpx 30rpx;
    width: 750rpx;
    box-sizing: border-box;

    .popup-header {
        display: flex;
        padding-bottom: 40rpx;
        margin-bottom: 40rpx;
        border-bottom: 1px solid #f5f5f5;
        position: relative;

        image {
            width: 200rpx;
            height: 200rpx;
            border-radius: 16rpx;
            box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
        }

        .popup-product-info {
            flex: 1;
            margin-left: 24rpx;
            padding-top: 10rpx;

            .popup-price {
                color: #EA1306;
                font-size: 44rpx;
                font-weight: bold;
                font-family: DIN;
                margin-bottom: 16rpx;
                display: flex;
                align-items: center;

                .original-price {
                    font-size: 26rpx;
                    color: #999;
                    text-decoration: line-through;
                    margin-left: 12rpx;
                    font-weight: normal;
                }
            }

            .popup-stock {
                font-size: 26rpx;
                color: #666;
                margin-bottom: 12rpx;
                display: flex;
                align-items: center;

                &::before {
                    content: '';
                    width: 8rpx;
                    height: 8rpx;
                    background: #67C23A;
                    border-radius: 50%;
                    margin-right: 8rpx;
                }
            }

            .selected-spec {
                font-size: 26rpx;
                color: #333;
                background: #f8f8f8;
                display: inline-block;
                padding: 8rpx 20rpx;
                border-radius: 24rpx;
            }
        }

        .close-icon {
            position: absolute;
            right: -10rpx;
            top: -10rpx;
            width: 56rpx;
            height: 56rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36rpx;
            color: #999;
            background: #f8f8f8;
            border-radius: 50%;

            &:active {
                background: #f0f0f0;
            }
        }
    }

    .delivery-section,
    .quantity-section {
        margin-bottom: 40rpx;

        .section-title {
            font-size: 30rpx;
            color: #333;
            font-weight: 500;
            margin-bottom: 24rpx;
            display: flex;
            align-items: center;

            &::before {
                content: '';
                width: 6rpx;
                height: 24rpx;
                background: #EA1306;
                border-radius: 6rpx;
                margin-right: 12rpx;
            }
        }
    }

    .delivery-options {
        display: flex;
        gap: 24rpx;

        .delivery-option {
            flex: 1;
            height: 88rpx;
            border: 2rpx solid #eee;
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12rpx;
            font-size: 28rpx;
            color: #666;
            transition: all 0.3s;
            position: relative;

            &.active {
                border-color: #EA1306;
                color: #EA1306;
                background: rgba(234, 19, 6, 0.05);
                font-weight: 500;

                .delivery-badge {
                    background: #EA1306;
                }
            }

            &.disabled {
                opacity: 0.6;
                cursor: not-allowed;
                pointer-events: none;
                background: #f5f5f5;
                border-color: #ddd;
            }

            .delivery-badge {
                position: absolute;
                right: -2rpx;
                top: -2rpx;
                background: #999;
                color: #fff;
                font-size: 20rpx;
                padding: 4rpx 12rpx;
                border-radius: 0 16rpx 0 16rpx;
                transform-origin: 100% 0;
                transition: all 0.3s;
            }

            &:active {
                transform: scale(0.98);
            }
        }
    }

    .quantity-control {
        display: flex;
        align-items: center;
        background: #f8f8f8;
        width: fit-content;
        border-radius: 12rpx;
        padding: 4rpx;

        input {
            width: 120rpx;
            height: 72rpx;
            text-align: center;
            background: #fff;
            margin: 0;
            font-size: 32rpx;
            color: #333;
            border: none;
        }

        .minus,
        .plus {
            width: 72rpx;
            height: 72rpx;
            background: #fff;
            border-radius: 8rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36rpx;
            color: #333;
            font-weight: 500;

            &:active {
                background: #f0f0f0;
            }

            &.disabled {
                color: #ccc;
                background: #f8f8f8;
            }
        }
    }

    .confirm-btn {
        margin-top: 40rpx;
        height: 88rpx;
        background: linear-gradient(135deg, #FF6B6B 0%, #EA1306 100%);
        color: #fff;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        font-weight: 500;
        box-shadow: 0 8rpx 16rpx rgba(234, 19, 6, 0.2);

        &:active {
            transform: scale(0.98);
        }
    }
}
</style>