<template>
  <view class="goods-card" @click="goToDetail">
    <view v-if="data.isMedicalInsurance || data.isOverallPlanning"
      style="position: absolute;top:0;left:0;border-radius: 10rpx 0 10rpx 0;color:#fff;padding:5rpx 10rpx;font-size:26rpx;"
      :style="{ 'background': data.isMedicalInsurance ? '#08BF8B' : '#0B78FF' }">
      {{ data.isMedicalInsurance ? '医保' : '统筹' }}
    </view>
    <view class="goods_title">
      <view v-if="data.type == 'otc' || data.type == 'not_prescription_drug' || data.type == 'prescription_drug'"
        :style="{
          fontSize: '18rpx',
          borderRadius: '7rpx',
          padding: '2rpx 8rpx',
          color: '#FFFFFF',
          background: data.type == 'prescription_drug' ? '#08BF8B' : '#3779F9'
        }">
        {{ data.type == 'otc' || data.type == 'not_prescription_drug' ? 'OTC' : data.type == 'prescription_drug' ? 'RX'
          : '' }}
      </view>
      {{ data.goodsName }}
    </view>
    <view style="display: flex;align-items: center;justify-content: space-between;padding:10rpx;height:70rpx;">
      <view style="display: flex;align-items: center;">
        <view style="color:#EA1306;margin-right:10rpx;font-size:32rpx;font-weight: 900;">¥{{ data.level1Price }}</view>
        <view v-if="data.salePrice > data.level1Price"
          style="font-size:28rpx;color:#999;text-decoration: line-through;">
          /¥{{ data.salePrice }}
        </view>
      </view>
      <image class="add"
        src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241211/5ed20995fb26499fa7d4f162ff5c77d5.png"
        mode='aspectFit' style="width:50rpx;height:50rpx;" @click.stop="showBuyPopup">
      </image>
    </view>
    <view v-if="!levels && data.level2Price < data.level1Price" class="goods-card-bottom">
      <i class="iconfont">&#xe6b0;</i>
      <view
        style="padding:3rpx 10rpx;background: linear-gradient(90deg, #FF5F00 0%, #EA3202 99%);color:#fff;border-radius: 20rpx;font-size:20rpx;margin:0 8rpx;">
        金卡价</view>
      <view style="color:#EA1306;font-weight: 900;font-size:32rpx;">¥{{ data.level2Price }}</view>
    </view>
    <view v-else-if="levels && data.level2Price < data.level1Price" class="goods-card-bottom">
      <image src="@/static/jinka.png" mode="heightFix"
        style="height:80rpx;position: absolute;left:0;top:-50%;transform: translateY(25%);"></image>
      <view style="color:#EA1306;font-weight: 900;font-size:32rpx;margin-left:90rpx;">¥{{ data.level2Price }}</view>
    </view>
    <buy-popup ref="buyPopup" :is-cart-action="true" :product="{
      image: data.img || '',
      price: levels == 2 || levels == 1 ? data.level2Price : data.level1Price || 0,
      originalPrice: data.salePrice || 0,
      name: data.goodsName || '',
      id: data.id || '',
      type: data.type || '',
      isMedicalInsurance: data.isMedicalInsurance,
      isOverallPlanning: data.isOverallPlanning
    }" @confirm="handleBuyConfirm" :cloud-stock="Number(data.warehouseStock) || 0"
      :store-stock="Number(data.saleStock) || 0" @click.stop :bottom="bottom" />
  </view>
</template>

<script>
import buyPopup from '@/components/buy-popup/buy-popup.vue'
const req = require('../../utils/request.js')
export default {
  name: 'goods-card',
  props: {
    data: {
      type: Object,
      required: true,
      default: () => ({})
    },
    // 距离底部距离
    bottom: {
      type: Boolean,
      default: false
    },
    // 是否需要刷新列表
    refresh: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      levels: req.getStorage("userInfo").levels
    }
  },
  methods: {
    handleBuyConfirm({ quantity, deliveryType, isCartAction }) {
      console.log(this.data);
      req.postRequest("/shopApi/purchase/cart", {
        skuId: null,
        merchantId: deliveryType == 1 ? req.getStorage("currentStore").id : 60,
        quantity,
        productId: this.data.id,
        mode: deliveryType,
        state: 0,
      }, res => {
        req.msg('加入购物车成功');
        getApp().globalData.updateCartBadge()
        if (this.refresh) {
          // 刷新列表数据
          // console.log("刷新刷新刷新");
          this.$emit('getList');
        };
      });
    },
    showBuyPopup() {
      console.log(this.data);
      if (!this.data) {
        uni.showToast({
          title: '商品信息不完整',
          icon: 'none'
        })
        return
      }
      this.$refs.buyPopup.open()
    },
    goToDetail() {
      if (!this.data || !this.data.id) {
        req.msg('商品信息不完整')
        return
      }
      uni.navigateTo({
        url: `/product/detail/detail?id=${this.data.id}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.goods-card {
  padding: 10rpx;
  background: #fff;
  border-radius: 20rpx;

  .goods_title {
    display: flow-root;
    margin: 10rpx 0;
    font-size: 26rpx;

    view {
      float: left;
      margin-right: 10rpx;
    }
  }

  .goods-card-bottom {
    display: flex;
    align-items: center;
    position: relative;
    padding: 8rpx;
    background: linear-gradient(90deg, #FDF6E1 0%, rgba(250, 235, 192, 0.2) 97%);
    border-radius: 15rpx;

    i {
      color: #EA8F06;
    }
  }
}
</style>