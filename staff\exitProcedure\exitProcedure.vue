<template>
	<view class="exitProcedure">
		<!-- 离职申请数据提交填写 -->
		<view v-if="!show">
			<view class="exitTime">
				<uni-section title="离职时间" type="line">
					<view style="padding: 20rpx 40rpx;">
						<!-- <view class="form-item">
							<text class="required">*</text>
							<text class="label">入职时间：</text>
							<uni-datetime-picker v-model="formData.entryDate" type="date">
								<view class="date-input">
									<view
										style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
										<text class="iconfont"
											style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
									</view>
									<text>{{ formData.entryDate || '请选择' }}</text>
								</view>
							</uni-datetime-picker>
						</view> -->
						<view class="form-item">
							<text class="required">*</text>
							<text class="label">离职时间：</text>
							<uni-datetime-picker v-model="formData.leaveDate" type="date">
								<view class="date-input">
									<view
										style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
										<text class="iconfont"
											style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
									</view>
									<text>{{ formData.leaveDate || '请选择' }}</text>
								</view>
							</uni-datetime-picker>
						</view>
					</view>
				</uni-section>
			</view>
			<view class="exitPost">
				<uni-section title="所属岗位" type="line">
					<view style="padding: 20rpx 40rpx;">
						<view class="form-item">
							<text class="required">*</text>
							<text class="label">所属岗位：</text>
							<input type="text" v-model="formData.post" class="input" disabled
								style="background: #f5f5f5;" />
						</view>
					</view>
				</uni-section>
			</view>
			<view class="exitPost">
				<uni-section title="交接人员" type="line">
					<view style="padding: 20rpx 40rpx;">
						<view class="form-item">
							<text class="required">*</text>
							<text class="label">是否交接：</text>
							<radio-group style="display: flex;align-items: center;" @change="changeResult">
								<label class="uni-list-cell uni-list-cell-pd" v-for="(item, index) in items"
									:key="item.value" style="display: flex;align-items: center;margin-right: 10rpx;">
									<view>
										<radio :value="item.value" :checked="item.value === formData.result"
											style="transform:scale(0.7);" color="#EA1306" />
									</view>
									<view>{{ item.name }}</view>
								</label>
							</radio-group>
						</view>
						<view class="form-item" v-if="formData.result">
							<text class="required">*</text>
							<text class="label">交接人员：</text>
							<input type="text" v-model="formData.heir" class="input" placeholder="请输入交接人员"
								@input="heirChange" />
							<view class="leader-list" v-if="heirList.length > 0">
								<view class="leader-item" v-for="(item, index) in heirList" :key="index"
									@click="selectHeir(item)">
									{{ item.name }}
								</view>
							</view>
						</view>
					</view>
				</uni-section>
			</view>
			<view class="exitPost">
				<uni-section title="审批" type="line">
					<view style="padding: 20rpx 40rpx;">
						<view class="form-item">
							<text class="required">*</text>
							<text class="label">部门领导：</text>
							<uni-data-select v-model="value" :localdata="range" @change="leaderChange"
								class="selectleader"></uni-data-select>
							<!-- <input type="text" v-model="leaderInputValue" placeholder="请输入审批人" class="input"
								@input="leaderChange" />
							<view class="leader-list" v-if="leaderList.length > 0">
								<view class="leader-item" v-for="(item, index) in leaderList" :key="index"
									@click="selectLeader(item)">
									{{ item.name }}
								</view>
							</view> -->
						</view>
					</view>
				</uni-section>
			</view>
			<view class="exitPost">
				<uni-section title="离职原因" type="line">
					<view style="padding: 20rpx 40rpx;">
						<view class="form-item" style="align-items: flex-start;">
							<text class="required">*</text>
							<text class="label">离职原因：</text>
							<textarea v-model="formData.reason" placeholder="请输入离职原因" class="textarea" />
						</view>
					</view>
				</uni-section>
			</view>
			<view>
				<view class="submitbtn" @click="addexitInfo">提交</view>
			</view>
		</view>
		<!-- 离职申请提交卡片 -->
		<block v-else>
			<view v-for="(item, index) in list" :key="index" class="application-item">
				<view v-if="item.state == 0" class="status-tag" style="background: #ff6b35;">审批中</view>
				<view v-if="item.state == 1" class="status-tag" style="background: #67c23a;">已通过</view>
				<view v-if="item.state == 2 || item.state == 3" class="status-tag" style="background: #f23030;">已拒绝
				</view>
				<view class="info-row">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe77c;</text>
					<text class="label">申请人：</text>
					<text class="value">{{ item.userId }}</text>
				</view>
				<view class="info-row">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe62b;</text>
					<text class="label">所属门店：</text>
					<text class="value">{{ item.storeId }}</text>
				</view>
				<view class="info-row">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe77c;</text>
					<text class="label">岗位：</text>
					<text class="value">{{ item.originalPostId }}</text>
				</view>
				<view style="display: flex;">
					<view class="info-row" style="width: 50%;">
						<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe656;</text>
						<text class="label">是否交接：</text>
						<text class="value">{{ item.isHandover == 1 ? '是' : '否' }}</text>
					</view>
					<view class="info-row" style="width: 50%;" v-if="item.isHandover == 1">
						<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe648;</text>
						<text class="label">交接人：</text>
						<text class="value" style="color:red;">{{ item.handoverId }}</text>
					</view>
				</view>
				<view class="info-row">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
					<text class="label">离职日期：</text>
					<text class="value">{{ item.applyTime }}</text>
				</view>
				<!-- <view class="info-row">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
					<text class="label">审批人：</text>
					<text class="value">{{ item.reason }}</text>
				</view> -->
				<view class="info-row">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe615;</text>
					<text class="label">离职原因：</text>
					<text class="value">{{ item.reason }}</text>
				</view>
				<view class="info-row" v-if="item.state == 0">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
					<text class="label">审批进度：</text>
					<view @click="open(item)"
						style="background-color: #ea1306;color: #fff;padding: 10rpx 20rpx;border-radius: 12rpx;">
						查看审批进度</view>
				</view>
			</view>
		</block>
		<uni-popup :is-mask-click="false" ref="approve" type="bottom" border-radius="10px 10px 0 0">
			<view class="popup-content1">
				<view class="popup-header">
					<text>审批进度</text>
					<text class="close-icon" @click="$refs.approve.close()">×</text>
				</view>
				<view class="popup-body">
					<view class="step-container">
						<view class="step" v-for="(item, index) in stepList" :key="index">
							<view class="step-number"></view>
							<view class="step-title">
								<view>开始时间:{{ item.startTime }}</view>
								<view>{{ item.assignee }}：{{ item.taskName }}</view>
								<view v-if="item.comment">审批意见：{{ item.comment }}</view>
								<view v-if="item.endTime">结束时间:{{ item.endTime }}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
const req = require("../../utils/request");
export default {
	data() {
		return {
			info: {},
			leaderInputValue: '',
			formData: {
				result: 1,
				post: req.getStorage('userInfo').postName,
				postId: req.getStorage('userInfo').postId
			},
			items: [{
				value: 1,
				name: '是',
				checked: 'true'
			},
			{
				value: 0,
				name: '否'
			}
			],
			pageNum: 1,
			total: 0,
			leaderList: [],
			heirList: [],
			list: [],
			timer1: null,
			timer2: null,
			show: false,
			is_headquarters: '',   // 是否总部人
			stepList: [],
			value: 0,
			range: [],
		};
	},
	onLoad() {
		this.getList();
		this.getBusiness();
	},
	methods: {
		open(item) {
			req.showLoading();
			req.getRequest('/shopApi/translate/getDetails', {
				id: item.id
			}, res => {
				let data = [...res.data];
				let list = data.filter(item => item.processDefinitionId.includes('resignation'));
				let taskId = list.length > 0 ? list[0].taskId : "";
				console.log(taskId);
				req.getRequest(`/shopApi/translate/history/${taskId}`, {}, res => {
					console.log(res);
					this.stepList = res;
					uni.hideLoading();
					this.$refs.approve.open();
				})
			})
		},
		getBusiness() {
			req.getRequest('/shopApi/goOut/business', {
				name: req.getStorage('userInfo').staffName
			}, res => {
				this.is_headquarters = res.data;
				this.getReviewer();
			});
		},
		getReviewer() {
			req.getRequest('/shopApi/translate/getReviewer', {
				uid: req.getStorage('userInfo').introductionId,
				node: 'addleave',
				isHeadquarters: this.is_headquarters
			}, res => {
				if (res.data.length != 0) {
					this.range = res.data.map(item => ({
						value: item.id,
						text: item.name
					}))
				}
			});
		},
		getList() {
			req.showLoading()
			req.getRequest("/shopApi/application/list", {
				pageNum: 1,
				pageSize: 10,
				userId: req.getStorage('userInfo').staffName,
			}, res => {
				uni.hideLoading()
				if (res.data.items.length > 0) {
					// 检查是否所有的items的state都是2或3
					const allRejectedOrCancelled = res.data.items.every(item => item.state === 2 || item.state === 3);
					this.show = !allRejectedOrCancelled;
					this.list = res.data.items;
				}
			})
		},
		leaderChange(e) {
			this.formData.leader = e ? this.range.find(item => item.value === e).text : "";
			// const value = e.detail.value.trim();
			// this.leaderInputValue = value;
			// this.formData.leader = ""
			// if (this.timer1) {
			// 	clearTimeout(this.timer);
			// 	this.timer1 = null;
			// }
			// if (!value) {
			// 	this.leaderList = [];
			// 	return;
			// }
			// this.timer1 = setTimeout(() => {
			// 	let params = {
			// 		name: value
			// 	};
			// 	let url = this.is_headquarters == 1 ? "/shopApi/replacement/getStaff" : "/shopApi/goOut/getManager";
			// 	if (this.is_headquarters != 1) {
			// 		params.uid = req.getStorage('userInfo').introductionId;
			// 	}
			// 	req.getRequest(url, params, res => {
			// 		this.leaderList = res.data.items;
			// 	});
			// }, 500);
		},
		// selectLeader(item) {
		// 	this.formData.leader = item.name;
		// 	this.leaderInputValue = item.name;
		// 	this.leaderList = [];
		// },
		heirChange(e) {
			const value = e.detail.value.trim();
			if (this.timer2) {
				clearTimeout(this.timer);
				this.timer2 = null;
			}
			if (!value) {
				this.heirList = [];
				return;
			}
			this.timer2 = setTimeout(() => {
				req.getRequest("/shopApi/replacement/getStaff", {
					name: value
				}, res => {
					this.heirList = res.data.items;
				});
			}, 500);
		},
		selectHeir(item) {
			this.formData.heirId = item.id;
			this.formData.heir = item.name;
			this.heirList = [];
		},
		changeResult(e) {
			this.formData.result = Number(e.detail.value);
		},
		getData() {
			req.getRequest('/shopApi/application/list', {
				pageNum: this.pageNum,
				pageSize: this.pageSize,
			}, res => {
				console.log(res);
			})
		},
		addexitInfo() {
			console.log(this.formData);
			if (!this.formData.leaveDate) {
				uni.showToast({
					title: '请选择离职时间',
					icon: 'none'
				});
				return;
			}
			if (!this.formData.heir && this.formData.result) {
				uni.showToast({
					title: '请输入交接人员',
					icon: 'none'
				});
				return;
			}
			if (!this.formData.leader) {
				uni.showToast({
					title: '请输入部门领导',
					icon: 'none'
				});
				return;
			}
			if (!this.formData.reason) {
				uni.showToast({
					title: '请输入离职原因',
					icon: 'none'
				});
				return;
			}
			req.showLoading()
			req.postRequest('/shopApi/application/add', {
				userId: req.getStorage('userInfo').staffName,
				storeId: req.getStorage('userInfo').divId,
				originalPostId: this.formData.postId,   //岗位
				isHandover: this.formData.result,     // 是否交接
				handoverId: this.formData.heirId, // 交接人
				applyTime: this.formData.leaveDate,   // 离职时间
				reason: this.formData.reason,
				deptleader: this.formData.leader
			}, res => {
				uni.hideLoading()
				console.log(res, '添加成功');
				req.msg('已提交申请');
				setTimeout(() => {
					this.getList()
				}, 1500);
			})
		}
	},
}
</script>

<style lang="scss" scoped>
@import './exitProcedure.scss';
</style>
