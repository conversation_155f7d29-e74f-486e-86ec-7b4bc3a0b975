<template>
	<view class="joinMember">
		<view class="tabType">
			<view class="text " style="border-right: 1px solid #fff;" @click="getQRCode(1)"
				:class="tabValue === 1 ? 'active_text' : ''">
				<text class="iconfont" style="font-size:28rpx;margin-right:10rpx;">&#xe84f;</text>
				免费金卡
			</view>
			<view class="text" @click="getQRCode(2)" :class="tabValue === 2 ? 'active_text' : ''">
				<text class="iconfont" style="font-size:28rpx;margin-right:10rpx;">&#xe6d6;</text>
				付费金卡
			</view>
		</view>
		<image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20250120/1ba071f388ce47b4b88ea1add7821879.jpg"
			style="width: 750rpx;" mode="widthFix">
		</image>
		<view class="code_" @click="previewImage">
			<image :src="micro" mode="scaleToFill" style="height:312rpx;width: 326rpx;"></image>
		</view>
	</view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
	data() {
		return {
			tabValue: 1,
			micro: '',
			imgUrls: []
		}
	},
	onLoad(options) {
		console.log(options, "user------------");
		this.getQRCode(1);
	},
	methods: {
		getQRCode(tabValue) {
			this.tabValue = tabValue;
			req.showLoading();
			req.getRequest('/shopApi/goldCard/getQRCode2', {
				page: 'mine/payMember/payMember',
				scen: JSON.stringify({
					back: 1,
					pid: req.getStorage("userInfo").id,
					tabValue: tabValue
				})
			}, res => {
				this.micro = res.data;
				uni.hideLoading();
			});
		},
		// 二维码预览
		previewImage() {
			uni.previewImage({
				urls: [this.micro] // 需要预览的图片http链接列表
			})
		},
	}
}
</script>

<style lang="scss" scoped>
.joinMember {
	position: relative;

	.tabType {
		position: absolute;
		right: 22rpx;
		top: 44rpx;
		display: flex;
		border: 1px solid #fff;
		border-radius: 8rpx;
		overflow: hidden;

		.text {
			font-family: PingFang SC;
			font-weight: normal;
			font-size: 30rpx;
			color: #333333;
			padding: 10rpx 15rpx;
			text-align: center;
			box-sizing: border-box;
			background-color: #fff;
		}

		.active_text {
			color: #FF1300;
			font-weight: 600;
			background-color: #FCB029;
		}
	}

	.code_ {
		width: 326rpx;
		height: 312rpx;
		background-color: #fff;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: 920rpx;
		border-radius: 12rpx;
		overflow: hidden;
	}
}
</style>
