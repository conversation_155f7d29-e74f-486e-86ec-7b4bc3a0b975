<template>
	<view class="container">
		<!-- 添加canvas用于图片处理 -->
		<canvas canvas-id="imageCanvas" style="position: fixed; left: -9999px;"></canvas>
		<view class="tips-box">
			<view class="tips-title">拍摄要求：</view>
			<view class="tips-item">1. 人脸完整清晰无遮挡，表情自然，正对摄像头</view>
			<view class="tips-item">2. 照片清晰、曝光正常，无过暗过亮</view>
			<view class="tips-item">3. 建议在光线充足的环境下拍摄</view>
		</view>
		<view class="preview-box">
			<camera v-if="!tempImagePath" device-position="front" flash="off" @error="error" class="camera"
				resolution="high"></camera>
			<view class="preview-img" v-else>
				<image :src="tempImagePath" mode="aspectFit"></image>
			</view>
			<view @click="takePhoto" class="camera-btn" v-if="!tempImagePath">拍摄</view>
			<view class="btn-group" v-else>
				<view @click="retake" class="btn-group-btn btn-group-btn-retake">重新拍摄</view>
				<view @click="uploadPhoto" class="btn-group-btn" v-if="isUploading">确认上传</view>
			</view>
			<view @click="viewFace" class="view-face" v-if='viewFaceImg'>查看录入照片</view>

		</view>
	</view>
</template>

<script>
import req from '@/utils/request.js';
export default {
	data() {
		return {
			tempImagePath: '', // 临时图片路径
			imageInfo: null, // 图片信息
			maxImageSize: 1 * 1024 * 1024, // 最大图片大小（1MB）
			minWidth: 480,
			minHeight: 640,
			maxWidth: 1080,
			maxHeight: 1920,
			cameraContext: null, // 相机上下文
			cameraError: false, // 相机错误状态
			isUploading: true, // 是否正在上传
			viewFaceImg: ''
		}
	},
	onLoad() {
		// 创建相机上下文
		this.cameraContext = uni.createCameraContext();
		if (req.getStorage('userInfo').face) {
			this.viewFaceImg = req.getStorage('userInfo').face;
		}
	},
	methods: {
		viewFace() {
			//预览
			uni.previewImage({
				current: 1,
				// 当前显示图片的http链接  
				urls: [req.getStorage('userInfo').face] // 假设 personalPic 是一个包含所有图片链接的数组
			});
		},
		// 相机错误处理
		error(e) {
			this.cameraError = true;
			console.error('相机错误：', e);
			uni.showToast({
				title: '相机启动失败，请检查相机权限',
				icon: 'none',
				duration: 3000
			});
		},

		// 拍照
		takePhoto() {
			if (this.cameraError) {
				uni.showToast({
					title: '相机不可用',
					icon: 'none'
				});
				return;
			}

			uni.showLoading({ title: '处理中...' });

			this.cameraContext.takePhoto({
				quality: 'high',
				success: async (res) => {
					console.log(res, 'resresres----');

					try {
						// 获取图片信息
						// const imageInfo = await this.getImageInfo(res.tempImagePath);
						this.tempImagePath = res.tempImagePath;
						// 	// 检查图片大小
						// 	const fileInfo = await this.getFileInfo(res.tempImagePath);
						// 	console.log('原始图片信息：', {
						// 		size: fileInfo.size,
						// 		width: imageInfo.width,
						// 		height: imageInfo.height
						// 	});

						// 	// 检查是否需要调整图片尺寸
						// 	if (imageInfo.width > this.maxWidth || imageInfo.height > this.maxHeight) {
						// 		finalImagePath = await this.resizeImage(finalImagePath, imageInfo);
						// 	}

						// 	// 如果图片大于最大限制，进行压缩
						// 	if (fileInfo.size >= this.maxImageSize) {
						// 		console.log('图片大于最大限制，进行压缩');
						// 		finalImagePath = await this.compressImage(finalImagePath);
						// 	}

						// 	// 获取最终图片信息
						// 	const finalImageInfo = await this.getImageInfo(finalImagePath);

						// 	this.tempImagePath = finalImagePath;
						// 	this.imageInfo = finalImageInfo;

						uni.hideLoading();
					} catch (error) {
						uni.hideLoading();
						uni.showToast({
							title: error.message || '图片处理失败',
							icon: 'none'
						});
					}
				},
				fail: (err) => {
					uni.hideLoading();
					uni.showToast({
						title: '拍照失败',
						icon: 'none'
					});
				}
			});
		},

		// 获取文件信息
		getFileInfo(filePath) {
			return new Promise((resolve, reject) => {
				uni.getFileInfo({
					filePath,
					success: (res) => resolve(res),
					fail: (err) => reject(new Error('获取文件信息失败'))
				});
			});
		},

		// 获取图片信息
		getImageInfo(imagePath) {
			return new Promise((resolve, reject) => {
				uni.getImageInfo({
					src: imagePath,
					success: (res) => {
						const { width, height } = res;
						console.log('图片尺寸:', width, '*', height);

						// 校验分辨率
						if (width < this.minWidth || height < this.minHeight) {
							uni.showToast({
								title: `图片分辨率过低，最小为${this.minWidth}*${this.minHeight}`,
								icon: 'none',
								duration: 3000
							});
							reject(new Error(`图片分辨率过低，最小为 ${this.minWidth}*${this.minHeight}`));
							return;
						}

						if (width > this.maxWidth || height > this.maxHeight) {
							uni.showToast({
								title: '图片将自动调整至合适尺寸',
								icon: 'none',
								duration: 2000
							});
						}

						// 检查图片方向
						if (res.orientation !== 'up') {
							console.log('图片需要调整方向');
						}

						resolve(res);
					},
					fail: (err) => reject(new Error('获取图片信息失败'))
				});
			});
		},

		// 压缩图片
		async compressImage(imagePath) {
			try {
				// 获取原始图片大小
				const originalInfo = await this.getFileInfo(imagePath);
				console.log('压缩前图片大小：', originalInfo.size / 1024, 'KB');

				// 根据原始图片大小动态设置压缩质量
				let quality = 90;
				if (originalInfo.size > 2 * 1024 * 1024) { // 大于2MB
					quality = 60;
				} else if (originalInfo.size > 1.5 * 1024 * 1024) { // 大于1.5MB
					quality = 70;
				} else if (originalInfo.size > this.maxImageSize) { // 大于1MB
					quality = 80;
				}

				const result = await new Promise((resolve, reject) => {
					uni.compressImage({
						src: imagePath,
						quality: quality,
						success: (res) => resolve(res),
						fail: (err) => reject(new Error('图片压缩失败'))
					});
				});

				// 检查压缩后的大小
				const compressedInfo = await this.getFileInfo(result.tempFilePath);
				console.log('压缩后图片大小：', compressedInfo.size / 1024, 'KB');

				// 如果压缩后仍然超过限制，继续压缩，但降低质量
				if (compressedInfo.size > this.maxImageSize) {
					quality = Math.max(quality - 10, 50); // 最低压缩质量为50
					return await new Promise((resolve, reject) => {
						uni.compressImage({
							src: result.tempFilePath,
							quality: quality,
							success: (res) => resolve(res.tempFilePath),
							fail: (err) => reject(new Error('图片二次压缩失败'))
						});
					});
				}

				return result.tempFilePath;
			} catch (error) {
				console.error('压缩过程出错：', error);
				throw new Error('图片压缩失败，请重试');
			}
		},

		// 重新拍摄
		retake() {
			this.tempImagePath = '';
			this.imageInfo = null;
			this.isUploading = true;
		},

		// 上传照片
		uploadPhoto() {
			if (!this.tempImagePath) {
				uni.showToast({
					title: '请先拍摄照片',
					icon: 'none'
				});
				return;
			}

			uni.showLoading({
				title: '上传中...'
			});
			req.uploadImg('/shopApi/faceVerify/upload', this.tempImagePath, res => {
				uni.showToast({
					title: '上传成功',
					icon: 'success'
				});
				this.isUploading = false;
				uni.hideLoading();
			})
		},

		// 调整图片尺寸
		resizeImage(imagePath, imageInfo) {
			return new Promise((resolve, reject) => {
				try {
					const ctx = uni.createCanvasContext('imageCanvas');

					// 计算缩放比例（确保不会小于最小尺寸）
					let newWidth = imageInfo.width;
					let newHeight = imageInfo.height;

					// 如果超过最大尺寸，进行等比例缩放
					if (newWidth > this.maxWidth || newHeight > this.maxHeight) {
						const ratioWidth = this.maxWidth / imageInfo.width;
						const ratioHeight = this.maxHeight / imageInfo.height;
						const ratio = Math.min(ratioWidth, ratioHeight);

						newWidth = Math.max(Math.floor(imageInfo.width * ratio), this.minWidth);
						newHeight = Math.max(Math.floor(imageInfo.height * ratio), this.minHeight);
					}

					console.log('调整后的尺寸:', newWidth, '*', newHeight);

					// 设置canvas尺寸
					const canvasWidth = newWidth;
					const canvasHeight = newHeight;

					// 绘制图片到canvas
					ctx.drawImage(imagePath, 0, 0, canvasWidth, canvasHeight);
					ctx.draw(false, () => {
						// 导出图片
						uni.canvasToTempFilePath({
							canvasId: 'imageCanvas',
							width: canvasWidth,
							height: canvasHeight,
							destWidth: newWidth,
							destHeight: newHeight,
							quality: 1,
							success: (res) => {
								// 验证调整后的图片尺寸
								uni.getImageInfo({
									src: res.tempFilePath,
									success: (info) => {
										console.log('最终图片尺寸:', info.width, '*', info.height);
										if (info.width < this.minWidth || info.height < this.minHeight) {
											reject(new Error(`调整后图片分辨率异常：${info.width}*${info.height}`));
											return;
										}
										resolve(res.tempFilePath);
									},
									fail: () => reject(new Error('获取调整后图片信息失败'))
								});
							},
							fail: (err) => {
								console.error('图片尺寸调整失败:', err);
								reject(new Error('图片尺寸调整失败'));
							}
						});
					});
				} catch (error) {
					console.error('图片处理错误:', error);
					reject(new Error('图片处理失败'));
				}
			});
		}
	}
}
</script>

<style>
.container {
	padding: 20rpx;
}

.tips-box {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background-color: #f8f8f8;
	border-radius: 10rpx;
}

.tips-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.tips-item {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 10rpx;
}

.preview-box {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 600rpx;
}

.preview-img {
	width: 710rpx;
	height: 700rpx;
	background-color: #f5f5f5;
	border-radius: 20rpx;
	overflow: hidden;
	display: flex;
	align-items: center;
	justify-content: center;
}

.preview-img-icon {
	font-size: 300rpx;
	color: #999;
	display: flex;
	justify-content: center;
	line-height: 600rpx;
}

.preview-img image {
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.btn-group {
	display: flex;
	justify-content: space-around;
	width: 100%;
	margin-top: 30rpx;
}

.btn-group-btn {
	margin: 20rpx auto;
	width: 40%;
	height: 80rpx;
	background-color: #EA1306;
	border-radius: 40rpx;
	color: #fff;
	font-size: 32rpx;
	text-align: center;
	line-height: 80rpx;
}

.btn-group-btn-retake {
	background-color: #ececec;
	color: #666;
}

.camera-btn {
	margin: 20rpx auto;
	width: 560rpx;
	height: 80rpx;
	border-radius: 40rpx;
	color: #fff;
	font-size: 32rpx;
	text-align: center;
	line-height: 80rpx;
	background-color: #EA1306;
}

.camera {
	width: 100%;
	height: 600rpx;
	border-radius: 20rpx;
}

.view-face {
	color: #EA1306;
	font-size: 28rpx;
	text-align: center;
	line-height: 80rpx;
}
</style>
