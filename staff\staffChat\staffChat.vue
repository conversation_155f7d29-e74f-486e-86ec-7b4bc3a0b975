<template>
	<view style="position: relative;">
		<!-- <view class="nav"
			:style="{ 'marginTop': statusBarHeight + 'px', 'height': 70 - statusBarHeight + 'px', 'background': '#fff' }">
			<uni-icons @click="goBack" type="left" size="25"></uni-icons>
			<view class="inquiryBtn">问诊中</view>
			<view class="toFinish" @click="toFinish">{{ registration.state == 2 || registration.state == 8 ||
				registration.state == 9 ? '详情' :
				'去完诊' }}</view>
		</view> -->
		<view class="toFinish" @click="toFinish">{{ registration.state == 2 || registration.state == 8 ||
			registration.state == 9 ? '详情' :
			'去完诊' }}</view>
		<scroll-view class="chat-container"
			:style="{ 'height': isShowButton ? 'calc(100vh - 190rpx)' : 'calc(100vh - 120rpx)' }" scroll-y
			:scroll-top="scrollTop">
			<view v-for="(msg, index) in messages" :key="index" class="message"
				:class="msg.from == staffId ? 'user-message' : 'server-message'">
				<view v-if="msg.from != staffId" class="container">
					<image mode="aspectFit" :src="msg.senderAvatar"></image>
				</view>
				<view
					:style="{ display: 'flex', flexDirection: 'column', alignItems: msg.from == staffId ? 'flex-end' : 'flex-start' }">
					<view style="font-size:22rpx;color:#999;padding:0 20rpx;">{{ msg.date }}</view>
					<view v-if="msg.type == 1" class="message-text">{{ msg.message }}</view>
					<view v-else-if="msg.type == 2 || msg.type == 4" class="message-text">
						<image @click="viewImg(msg.message)" :src="msg.message" style="width:300rpx;" mode="widthFix">
						</image>
					</view>
				</view>
				<view v-if="msg.from == staffId" class="container">
					<image mode="aspectFit" :src="msg.senderAvatar"></image>
				</view>
			</view>
		</scroll-view>
		<view class="input-container">
			<view
				style="display: flex;align-items: center;margin-top: auto;width: 100%;height: 108rpx;background: #fff;padding:0 30rpx;box-sizing: border-box;">
				<image @click="showButton"
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240806/73a9eb74c6134766bdbf18a973949670.png"
					mode="widthFix" style="width:55rpx;margin:0 10rpx;"></image>
				<input v-model="inputMessage" placeholder="请输入文字" class="input-field" />
				<view @click="sendMessage(inputMessage)" class="send-button">发送</view>
			</view>
			<view v-if="isShowButton" style="display: flex;background: #fff;padding:0 0 10rpx 90rpx">
				<view style="display: flex;flex-direction: column;align-items: center;justify-content:flex-end">
					<image @click="sendImg"
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240806/c23bd9800cc64ab8b78dfc590acc501a.png"
						mode="widthFix" style="width:40rpx;"></image>
					<view style="font-size:24rpx;">照片</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
const req = require("../../utils/request.js");

export default {
	data() {
		return {
			inputMessage: '',
			staffId: "",
			id: "",
			patientId: "",
			ws: null,
			back: null,
			scrollTop: 0,
			statusBarHeight: null,
			isShowButton: false,
			messages: [],
			registration: {},
			heartBeatInterval: null,
			heartBeatTimeout: 30000, // 30 seconds,
			isOnline: 0,
		};
	},
	onLoad(options) {
		this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight
		this.staffId = options.staffId
		this.id = options.id
		this.patientId = options.patientId != 'null' ? options.patientId : options.uid
		if (options.back) this.back = options.back
		this.isOnline = options.isOnline
		this.initWebSocket();
		// this.getInfo()
	},
	onShow() {
		this.getInfo()
		this.getRegistration()
	},
	onUnload() {
		this.closeWebSocket();
	},
	methods: {
		createDate() {
			const currentDate = new Date();
			const year = currentDate.getFullYear();
			const month = String(currentDate.getMonth() + 1).padStart(2, '0');
			const day = String(currentDate.getDate()).padStart(2, '0');
			const hours = String(currentDate.getHours()).padStart(2, '0');
			const minutes = String(currentDate.getMinutes()).padStart(2, '0');
			const seconds = String(currentDate.getSeconds()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		},
		getRegistration() {
			req.getRequest('/shopApi/shopEmployee/bookingRegister', { userId: req.getStorage("loginInfo").id, id: this.id }, res => {
				this.registration = res.data[0]
			})
		},
		goGoods() {
			uni.navigateTo({
				url: `/pages/reserveGoods/reserveGoods?registrationId=${this.id}&patientId=${this.patientId}&staffId=${this.staffId}`
			});
		},
		goBack() {
			// if (this.back) {
			uni.navigateBack()
			// } else {
			// uni.navigateBack()
			// }
		},
		showButton() {
			this.isShowButton = !this.isShowButton
			this.scrollToBottom();
		},
		viewImg(url) {
			uni.previewImage({
				urls: [url]
			})
		},
		sendImg() {
			uni.chooseImage({
				count: 1,
				success: res => {
					uni.uploadFile({
						url: req.env[req.env.NODE_ENV].apiUrl + `/hisApi/inquiry/upload`,
						filePath: res.tempFilePaths[0],
						name: 'file',
						// header: {
						// 	'Content-Type': 'multipart/form-data'
						// },
						formData: {
							'registrationId': this.id,
							'receiver': this.patientId,
							'sender': this.staffId
						},
						success: res => {
							if (res.data.code == 500) {
								req.msg("出错了，请稍后再试")
							}
						}
					});
				}
			})
		},
		removeProtocol(url) {
			return url.replace(/^https?:\/\//, '');
		},
		getInfo() {
			wx.showLoading()
			req.getRequest("/shopApi/shopEmployee/getInfo", {
				registrationId: this.id,
				staffId: this.staffId,
			}, res => {
				this.messages = []
				uni.hideLoading();
				res.data.forEach(item => {
					if (item.type == 3) {
						this.messages.push({
							from: item.sender,
							message: JSON.parse(item.content),
							senderAvatar: item.senderAvatar,
							date: item.createDate,
							type: item.type
						})
					}
					else {
						this.messages.push({
							from: item.sender,
							message: item.content,
							senderAvatar: item.senderAvatar,
							date: item.createDate,
							type: item.type
						})
					}
				})
				this.scrollToBottom();
			})
		},
		initWebSocket() {
			console.log(process.env.VUE_APP_API_URL);
			this.ws = uni.connectSocket({
				url: `${req.env.NODE_ENV == 'product' ? 'wss://' : 'ws://'}${this.removeProtocol(req.env[req.env.NODE_ENV].apiUrl)}/hisApi/inquiry/${this.staffId}/${this.patientId}/${this.id}`,

				success: () => {
					console.log('WebSocket connection opened.');
				}
			});

			this.ws.onOpen(() => {
				console.log('WebSocket connection opened.');
				this.startHeartBeat();
			});

			this.ws.onMessage((res) => {
				const data = JSON.parse(res.data);
				console.log('接收到消息', data);
				if (data.type == 3) {
					this.messages.push({
						from: data.sender,
						message: JSON.parse(data.content),
						senderAvatar: data.senderAvatar,
						date: this.createDate(),
						type: data.type
					});
				} else {
					this.messages.push({
						from: data.sender,
						message: data.content,
						senderAvatar: data.senderAvatar,
						date: this.createDate(),
						type: data.type
					});
				}
				this.scrollToBottom();
			});

			this.ws.onClose(() => {
				console.log('断开连接');
				this.stopHeartBeat();
			});

			this.ws.onError((err) => {
				uni.showModal({
					content: '出错了，请稍后再试',
					showCancel: false,
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack();
						}
					}
				});
				console.error('错误：' + err);
				this.stopHeartBeat();
			});
		},
		sendMessage(message) {
			if (!message) return;
			if (this.ws && this.ws.readyState) {
				// const data = {
				// 	type: 'message',
				// 	userId: this.userId,
				// 	message: message
				// };
				this.ws.send({
					data: message
				});
				this.messages.push({
					from: this.staffId,
					message: message,
					senderAvatar: req.getStorage("userInfo").avatar,
					date: this.createDate(),
					type: 1
				});
				this.inputMessage = ''; // Clear input field after sending message

				this.scrollToBottom();
			} else {
				console.error('WebSocket is not open.');
			}
		},
		startHeartBeat() {
			if (this.heartBeatInterval) {
				clearInterval(this.heartBeatInterval);
			}
			this.heartBeatInterval = setInterval(() => {
				if (this.ws && this.ws.readyState) {
					this.ws.send({
						data: 'heartBeat',
						success: () => {
							console.log('HeartBeat sent.');
						}
					});
				} else {
					clearInterval(this.heartBeatInterval);
				}
			}, this.heartBeatTimeout);
		},
		stopHeartBeat() {
			if (this.heartBeatInterval) {
				clearInterval(this.heartBeatInterval);
				this.heartBeatInterval = null;
			}
		},
		closeWebSocket() {
			if (this.ws) {
				this.ws.close();
			}
		},
		scrollToBottom() {
			this.$nextTick(() => {
				this.scrollTop = this.messages.length * 10000; // 根据消息数量设置滚动高度
			});
		},
		toFinish() {
			let staffId = this.staffId
			let patientId = this.patientId   //患者id
			let id = this.id  //挂号id
			wx.navigateTo({
				url: '/staff/patientInfo/patientInfo?staffId=' + staffId + '&patientId=' + patientId + '&id=' + id + '&isOnline=' + this.isOnline + '&state=' + this.registration.state + "&userId=" + this.registration.uid
			})
		},

		// useConfirm(e) {
		// 	console.log('adada111111', e);
		// 	let _ts = this
		// 	let list = _ts.messages
		// 	let filteredList = list.filter((it, index) => index == e);
		// 	let ids = filteredList[0].message.map(item => item.id);

		// 	let staffId = _ts.staffId
		// 	let patientId = _ts.patientId   //患者id
		// 	let id = _ts.id  //挂号id

		// 	req.postRequest('/doctor/submitDoctor', { staffId, id, ids }, res => {
		// 		console.log('提交', res);
		// 		if (res == '提交成功') {
		// 			wx.navigateTo({
		// 				url: '/pages/patientInfo/patientInfo?staffId=' + staffId + '&patientId=' + patientId + '&id=' + id,
		// 			})
		// 		}
		// 	})
		// },
	}
};
</script>

<style src="./staffChat.css"></style>