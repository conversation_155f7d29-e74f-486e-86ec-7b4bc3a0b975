<template>
	<view class="container">
		<!-- 搜索框 -->
		<view class="search-box">
			<uni-search-bar v-model="searchValue" clearButton="auto" placeholder="搜索单据编号" :cancelButton="false" />
		</view>

		<!-- 标签页 -->
		<view class="tab-container">
			<text :class="['tab-item', current === 0 ? 'active' : '']" @click="handleTabChange(0)">单据审核</text>
			<text :class="['tab-item', current === 1 ? 'active' : '']" @click="handleTabChange(1)">取消已审核单据</text>
		</view>

		<!-- 日期选择 -->
		<view class="date-picker">
			<!-- <uni-datetime-picker type="daterange" v-model="dateRange" @change="handleDateChange" rangeSeparator="至" /> -->
			<uni-datetime-picker v-model="dateRange" type="daterange">
				<view class="date-input">
					<view
						style="background: red;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
						<text class="iconfont" style="color:#fff;font-size:34rpx;font-weight: 900;">&#xe685;</text>
					</view>
					<text>{{ dateRange[0] || '开始日期' }} 至 {{ dateRange[1] || '结束日期' }}</text>
				</view>
			</uni-datetime-picker>
		</view>

		<!-- 订单列表 -->
		<scroll-view scroll-y class="order-list" @scrolltolower="loadMore">
			<view v-if="orderList.length > 0" class="list-content">
				<view class="item" @click="showDetailPopup(item)" v-for="(item, index) in orderList" :key="index">
					<view class="status">审核通过</view>
					<view class="info-grid">
						<view class="info-row">
							<view class="info-item">
								<text class="label">单据编号：</text>
								<text class="value">{{ item.orderNo }}</text>
							</view>
						</view>
						<view class="info-row">
							<view class="info-item full">
								<text class="label">单位名称：</text>
								<text class="value">{{ item.companyName }}</text>
							</view>
						</view>
						<view class="info-row">
							<view class="info-item">
								<text class="label">总 金 额：</text>
								<text class="value red">{{ item.amount }}</text>
							</view>
							<view class="info-item">
								<text class="label">预到货期：</text>
								<text class="value">{{ item.expectedDate }}</text>
							</view>
						</view>
						<view class="info-row">
							<view class="info-item">
								<text class="label">成本金额：</text>
								<text class="value red">{{ item.costAmount || '￥0.00' }}</text>
							</view>
							<view class="info-item">
								<text class="label">毛　　利：</text>
								<text class="value profit">{{ item.profit || '￥0.00' }}</text>
							</view>
						</view>
						<view class="info-row">
							<view class="info-item">
								<text class="label">结款方式：</text>
								<text class="value payment">{{ item.paymentMethod }}</text>
							</view>
							<view class="info-item">
								<text class="label">操 作 员：</text>
								<text class="value operator">{{ item.operator }}</text>
							</view>
						</view>
						<view class="info-row" v-if="item.rejectReason">
							<view class="info-item full">
								<text class="label">原　　因：</text>
								<text class="value">{{ item.rejectReason }}</text>
							</view>
						</view>
					</view>
					<view v-if="current === 0" class="footer">
						<view class="btns">
							<view class="btn primary" @click.stop="handleApprove(item)">审核通过</view>
							<view class="btn default" @click.stop="handleReject(item)">审核不通过</view>
						</view>
					</view>
					<view v-else class="footer">
						<view class="btns">
							<view class="btn cancel" @click.stop="handleCancel(item)">取消审核</view>
						</view>
					</view>
				</view>
				<!-- 加载更多 -->
				<view class="loading-more" v-if="orderList.length > 0">
					<text v-if="loading">加载中...</text>
					<text v-else-if="!hasMore">没有更多数据了</text>
				</view>
			</view>
			<view v-else style="text-align: center;color: #999;">
				<image mode=""
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png"
					style="width: 372rpx;height: 312rpx;margin-top: 50rpx;">
				</image>
				<view style="margin-top: 10rpx;font-size: 28rpx;">暂无数据</view>
			</view>
		</scroll-view>

		<!-- 审核不通过原因弹窗 -->
		<uni-popup ref="rejectPopup" type="center">
			<view class="popup-content">
				<view class="popup-header">
					<text>商品编号：{{ currentItem.orderNo }}</text>
					<text class="close-icon" @click="closeRejectPopup">×</text>
				</view>
				<view class="popup-body">
					<textarea v-model="rejectReason" class="reason-input" placeholder="请输入审核不通过原因"></textarea>
				</view>
				<view class="popup-footer">
					<view class="btn default" @click="closeRejectPopup">取消</view>
					<view class="btn primary" @click="confirmReject">保存</view>
				</view>
			</view>
		</uni-popup>

		<!-- 详情弹窗 -->
		<uni-popup ref="detailPopup" type="bottom">
			<view class="popup-content detail-popup">
				<view class="popup-header">
					<text>单据审核(4张单)</text>
					<text class="close-icon" @click="closeDetailPopup">×</text>
				</view>
				<scroll-view scroll-y class="detail-order-list">
					<view class="list-content">
						<view class="item" v-for="(item, index) in orderList" :key="index">
							<view class="status">审核通过</view>
							<view class="info-grid">
								<view class="info-row">
									<view class="info-item">
										<text class="label">单据编号：</text>
										<text class="value">{{ item.orderNo }}</text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item full">
										<text class="label">产品名称：</text>
										<text class="value">{{ item.companyName }}</text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item">
										<text class="label">数　　量：</text>
										<text class="value payment">3000</text>
									</view>
									<view class="info-item">
										<text class="label">包装数量：</text>
										<text class="value payment">30</text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item">
										<text class="label">商品规格：</text>
										<text class="value">30只</text>
									</view>
									<view class="info-item">
										<text class="label">单　　价：</text>
										<text class="value red">{{ item.amount }}</text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item">
										<text class="label">成本金额：</text>
										<text class="value red">{{ item.costAmount || '￥0.00' }}</text>
									</view>
									<view class="info-item">
										<text class="label">毛　　利：</text>
										<text class="value profit">{{ item.profit || '￥0.00' }}</text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item full">
										<text class="label">生产厂家：</text>
										<text class="value">{{ item.companyName }}</text>
									</view>
								</view>
							</view>
						</view>
						<!-- 加载更多 -->
					</view>
				</scroll-view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
const req = require('../../utils/request')
export default {
	data() {
		return {
			searchValue: '',
			current: 0,
			dateRange: [],
			rejectReason: '', // 审核不通过原因
			currentItem: null, // 当前操作的订单
			orderList: [],
			page: 1,
			pageSize: 10,
			loading: false,
			hasMore: true,
			showDetail: false,
		}
	},
	onLoad() {
		this.getOrderList()
	},
	methods: {
		// 获取订单列表
		async getOrderList(reset = false) {
			if (reset) {
				this.page = 1
				this.orderList = []
				this.hasMore = true
			}

			if (!this.hasMore || this.loading) return

			this.loading = true
			try {
				// 模拟接口调用
				const params = {
					page: this.page,
					pageSize: this.pageSize,
					status: this.current, // 0-待审核 1-已审核
					startDate: this.dateRange[0],
					endDate: this.dateRange[1],
					keyword: this.searchValue
				}

				// TODO: 替换为实际的接口调用
				const res = await new Promise(resolve => {
					setTimeout(() => {
						resolve({
							code: 200,
							data: {
								list: Array(10).fill({}).map((_, index) => ({
									orderNo: `THF10000${this.page}${index}`,
									companyName: '广州某某医药有限公司',
									date: '2024-02-15',
									amount: '￥1234.56',
									expectedDate: '2024-02-20',
									paymentMethod: '现金',
									operator: '张三',
									costAmount: '￥800.00',
									profit: '￥434.56',
									rejectReason: index === 0 ? '商品信息有误' : ''
								})),
								total: 35
							}
						})
					}, 1000)
				})

				if (res.code === 200) {
					const { list, total } = res.data
					this.orderList = [...this.orderList, ...list]
					this.hasMore = this.orderList.length < total
				}
			} catch (error) {
				console.error('获取订单列表失败：', error)
				uni.showToast({
					title: '获取订单列表失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		// 加载更多
		loadMore() {
			this.page++
			this.getOrderList()
		},
		// 搜索
		handleSearch(e) {
			this.searchValue = e
			this.getOrderList(true)
		},
		// 切换标签
		handleTabChange(index) {
			this.current = index
			this.getOrderList(true)
		},
		// 日期变化
		handleDateChange(e) {
			console.log('日期变化：', e)
			this.getOrderList(true)
		},
		handleApprove(item) {
			// 处理审核通过
			uni.showToast({
				title: '审核通过',
				icon: 'success'
			})
		},
		handleReject(item) {
			this.currentItem = item
			this.rejectReason = ''
			this.$refs.rejectPopup.open()
		},
		handleCancel(item) {
			this.currentItem = item
			this.rejectReason = ''
			this.$refs.rejectPopup.open()
		},
		// 关闭审核不通过弹窗
		closeRejectPopup() {
			this.$refs.rejectPopup.close()
			this.rejectReason = ''
			setTimeout(() => {
				this.currentItem = null
			}, 100);
		},
		// 确认审核不通过
		confirmReject() {
			if (!this.rejectReason.trim()) {
				req.msg('请输入原因')
				return
			}
			console.log('审核不通过原因：', this.rejectReason)
			this.$refs.rejectPopup.close()
			this.rejectReason = ''
			this.currentItem = null
		},
		// 显示详情弹窗
		showDetailPopup(item) {
			this.currentItem = item
			this.$refs.detailPopup.open()
		},
		// 关闭详情弹窗
		closeDetailPopup() {
			this.$refs.detailPopup.close()
			setTimeout(() => {
				this.currentItem = null
			}, 100)
		}
	}
}
</script>

<style lang="scss" scoped>
@import './sellApproval.scss';
</style>
