.approvalProgress {
  background-color: #f8f8f8;
  // height: 100vh;
  padding: 20rpx;
  box-sizing: border-box;

  .application-item {
    background-color: #fff;
    border-radius: 8rpx;
    margin-bottom: 20rpx;
    padding: 70rpx 32rpx 28rpx;

    .info-row {
      margin-bottom: 8px;
      display: flex;
      align-items: center;

      .label {
        color: #333;
        font-size: 14px;
        width: 80px;
      }

      .value {
        color: #303133;
        font-size: 14px;
        flex: 1;
      }
    }
  }

  .exitTime {
    width: 100%;
    background-color: #fff;
    border-radius: 8rpx;
    margin-bottom: 20rpx;
  }

  .exitPost {
    margin-bottom: 20rpx;
  }

  .form-item {
    position: relative;
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    .required {
      color: #f23030;
      margin-right: 5px;
    }

    .label {
      width: 150rpx;
      // font-size: 14px;
      color: #333;
      // margin-right: 10px;
      text-align: justify;
      text-align-last: justify;
    }
    .label1 {
      width: 200rpx;
    }
    .label_1 {
      width: 200rpx;
    }

    .input,
    .textarea,
    .date-input,
    .select-input,
    .selectleader {
      flex: 1;
      border: 1px solid red;
      border-radius: 4px;
      font-size: 14px;
      color: #333;
      background: #fff;
    }
    /deep/.uni-select {
      border: 0;
    }

    .input {
      height: 68rpx;
      padding: 0 24rpx;
    }

    .textarea {
      height: 160rpx;
      padding: 20rpx 24rpx;
    }

    .date-input {
      height: 68rpx;
      padding-right: 24rpx;
      display: flex;
      align-items: center;
      // justify-content: space-between;

      .calendar-icon,
      .arrow-down {
        color: #999;
        font-size: 28rpx;
      }
    }

    .leader-list {
      position: absolute;
      top: 42px;
      left: 95px;
      right: 0;
      max-height: 300rpx;
      background: #fff;
      border: 1px solid #eee;
      border-radius: 4px;
      overflow-y: auto;
      z-index: 999;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

      .leader-item {
        padding: 20rpx;
        border-bottom: 1px solid #eee;
        cursor: pointer;

        &:hover {
          background: #f5f5f5;
        }

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
  .form-item1 {
    width: 50%;
    margin-bottom: 0;
    line-height: 60rpx;
  }
  .submitbtn {
    padding: 20rpx;
    border-radius: 12rpx;
    text-align: center;
    margin: 10rpx;
    width: 120rpx;
  }

  .popup-content {
    background: #fff;
    border-radius: 10px 10px 0 0;

    .popup-header {
      position: relative;
      padding: 20rpx 30px;
      text-align: center;
      border-bottom: 1px solid #eee;
      background-color: #ea1306;
      border-radius: 10px 10px 0 0;
      color: #fff;

      text {
        font-size: 16px;
        font-weight: 500;
      }

      .close-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 24px;
        color: #fff;
        padding: 5px;
      }
    }
    /* 隐藏滚动条 */
    ::-webkit-scrollbar {
      display: none;
    }
    .popup-body {
      padding: 20px;
      max-height: 800rpx;
      overflow: scroll;
      /* Step Container */
      .step-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        // width: 200px;
      }

      /* Individual Step */
      .step {
        display: flex;
        align-items: center;
        margin: 10px 0;
        position: relative;
        padding-left: 15rpx;
      }

      /* Step Number */
      .step-number {
        width: 10px;
        height: 10px;
        border: 5px solid #ea1306;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
      }

      /* Step Title */
      .step-title {
        margin-left: 10px;
        font-size: 14px;
        color: #333;
      }

      /* Line Between Steps (Dashed) */
      .step::after {
        content: "";
        position: absolute;
        top: 68%;
        left: 32rpx;
        width: 2px;
        height: 90%;
        border-left: 2px dashed #ea1306;
        // /* Dashed Line */
        // z-index: -1;
      }

      /* Remove line for the last step */
      .step:last-child::after {
        display: none;
      }
    }
  }
}
.detail-popup {
  width: 100% !important;
  height: 800rpx;
  border-radius: 24rpx 24rpx 0 0 !important;
  padding-bottom: 80rpx;

  .detail-order-list {
    height: 800rpx;
    background: #f5f5f5;

    .list-content {
      padding: 20rpx;
    }

    .item {
      background-color: #fff;
      padding: 30rpx;
      margin-bottom: 20rpx;
      border-radius: 15rpx;
      position: relative;

      .status {
        position: absolute;
        top: 0;
        left: 0;
        background: red;
        color: #fff;
        padding: 5rpx 10rpx;
        font-size: 24rpx;
        border-radius: 15rpx 0 15rpx 0;
        display: inline-block;
      }

      .info-grid {
        margin-top: 20rpx;

        .info-row {
          display: flex;
          margin-bottom: 16rpx;

          &:first-child {
            padding-bottom: 16rpx;
            border-bottom: 1px dashed #e3e4e5;
          }

          .info-item {
            flex: 1;
            display: flex;
            align-items: center;
            font-size: 28rpx;

            &.full {
              flex: 2;
            }

            .label {
              width: 160rpx;
              color: #333;
              letter-spacing: 2rpx;
              text-align: justify;
              text-align-last: justify;
            }

            .value {
              flex: 1;
              color: #666;

              &.red {
                color: #ff0000;
                font-weight: bold;
              }

              &.profit {
                color: #52c41a;
                font-weight: 900;
              }

              &.payment {
                color: #0b78ff;
              }

              &.operator {
                color: #722ed1;
              }
            }
          }
        }
      }
    }
  }
}
// .underline-color {
//   text-decoration: underline;
//   text-decoration-color: #ea1306; /* 你可以使用任何有效的颜色值 */
//   text-decoration-thickness: 2px;
// }
/deep/.uni-section {
  border-radius: 8rpx;
}

/deep/.uni-section .uni-section-header__decoration {
  background-color: #ea1306;
}

/deep/.uni-section__content-title {
  font-size: 30rpx !important;
  font-weight: 600 !important;
}

/deep/.uni-select {
  border: 1px solid #ea1306;
}
