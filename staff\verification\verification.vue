<template>
	<view class="verification-container">
		<!-- 核销输入区域 -->
		<view class="input-section">
			<view class="input-group">
				<text class="label">手机号码</text>
				<input type="number" v-model="phoneNumber" placeholder="请输入会员手机号码" class="input" maxlength="11" />
				<view class="check-btn" @click.stop="checkMember">验证</view>
			</view>
			<view class="input-group">
				<text class="label">抖音券码</text>
				<input type="text" v-model="couponCode" placeholder="请扫描优惠券码" class="input" />
				<uni-icons color="#BFBFBF" type="scan" size="20" class="scan-btn" @click.stop="scanCode"></uni-icons>
			</view>
			<view class="verify-btn" @click.stop="verifyCoupon" :style="{ background: couponCode && isMember ? '#EA1306' : '#999' }">核销</view>
		</view>

		<!-- 核销记录列表 -->
		<!-- <view class="record-list">
			<uni-section title="核销记录" type="line"></uni-section>
			<view class="record-item" v-for="(item, index) in recordList" :key="index">
				<view class="record-header">
					<text class="record-date">{{ item.createTime }}</text>
					<text class="record-status" :style="{ color: item.status === 1 ? '#67C23A' : '#EA1306' }">
						{{ item.status === 1 ? '核销成功' : '核销失败' }}
					</text>
				</view>
				<view class="record-info">
					<view class="info-item">
						<text class="info-label">券码：</text>
						<text class="info-value">{{ item.couponCode }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">手机号：</text>
						<text class="info-value">{{ item.phoneNumber }}</text>
					</view>
					<view class="info-item" v-if="item.remark">
						<text class="info-label">备注：</text>
						<text class="info-value">{{ item.remark }}</text>
					</view>
				</view>
			</view>
			<view v-if="recordList.length === 0" class="empty-record">
				<image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png" mode="aspectFit"></image>
				<text>暂无核销记录</text>
			</view>
		</view> -->
	</view>
</template>

<script>
const req = require("../../utils/request");

export default {
	data() {
		return {
			couponCode: '',
			phoneNumber: '',
			recordList: [],
			pageNum: 1,
			pageSize: 10,
			total: 0,
			isMember: false,
		}
	},
	onLoad() {
		// this.getRecordList();
	},
	onReachBottom() {
		if (this.recordList.length < this.total) {
			this.pageNum++;
			// this.getRecordList();
		}
	},
	methods: {
		// 扫码
		scanCode() {
			if (!this.phoneNumber) {
				req.msg('请先输入手机号码');
				return;
			};
			if (!/^1[3-9]\d{9}$/.test(this.phoneNumber)) {
				req.msg('请输入正确的手机号码');
				return;
			}
			if (!this.isMember) {
				// 不是会员，提示加入会员
				uni.showModal({
					title: '提示',
					content: '请先验证该用户手机号码',
					confirmText: '确定',
					showCancel: false
				});
				return;
			};
			uni.scanCode({
				scanType: ['qrCode'], // 只开启二维码扫描
				success: async (res) => {
					console.log('扫码结果：', res);
					try {
						this.couponCode= res.result;
						// 如果是链接形式，先解析短链接
						// if (couponData.includes('http')) {
						// 	// 获取问号后面的参数部分
						// 	const paramsPart = couponData.split('?')[1];
						// 	if (paramsPart) {
						// 		// 解析参数
						// 		const params = {};
						// 		paramsPart.split('&').forEach(param => {
						// 			const [key, value] = param.split('=');
						// 			params[key] = decodeURIComponent(value);
						// 		});
						// 		// 获取券码参数，假设参数名为 code
						// 		this.couponCode = params.code || couponData;
						// 	} else {
						// 		this.couponCode = couponData;
						// 	}
						// } else {
						// 	this.couponCode = couponData;
						// }
					} catch (error) {
						console.error('解析券码失败：', error);
						req.msg('无效的券码格式');
					}
				},
				fail: (err) => {
					console.error('扫码失败：', err);
					req.msg('扫码失败，请重试');
				}
			});
		},
		// 检查是否是会员
		checkMember() {
			if (!this.phoneNumber) {
				req.msg('请输入手机号码');
				return;
			}
			if (!/^1[3-9]\d{9}$/.test(this.phoneNumber)) {
				req.msg('请输入正确的手机号码');
				return;
			}

			req.showLoading('查询中...');
			req.getRequest('/shopApi/dy/coupon/memberValidity', {
				phone: this.phoneNumber
			}, res => {
				uni.hideLoading();
				if (res.data) {
					// 是会员，直接调用扫码;
					this.isMember = true;
					this.scanCode();
				} else {
					this.isMember = false;
					// 不是会员，提示加入会员
					uni.showModal({
						title: '提示',
						content: '该用户还不是会员，请先加入会员',
						confirmText: '确定',
						showCancel: false
					});
				}
			});
		},
		// 验券准备
		verifyCoupon() {
			if (!this.phoneNumber||!/^1[3-9]\d{9}$/.test(this.phoneNumber)) {
				req.msg('请输入手机号码');
				return;
			};
			if (!this.isMember) {
				// 不是会员，提示加入会员
				uni.showModal({
					title: '提示',
					content: '请先验证该用户手机号码',
					confirmText: '确定',
					showCancel: false
				});
				return;
			};
			if (!this.couponCode) {
				req.msg('请扫描二维码或输入券码');
				return;
			};
			req.showLoading('核销中...');
			req.getRequest('/shopApi/dy/coupon/checkPrepare', {
				code: this.couponCode.includes('http') ? '': this.couponCode ,
				encrypted_data: this.couponCode.includes('http') ?  this.couponCode:'',
			}, res => {
				console.log(res, "res");
				if (res.code == 200) {
					let data = res.data;
					// const { codes, ...newData } = data;
					this.comfirmCoupon(data);
				}
			});
		},
		comfirmCoupon(data) {
			// req.showLoading('核销中...');
			data.phone = this.phoneNumber;
			req.postRequest('/shopApi/dy/coupon/couponCheck', data, res => {
				// console.log(res, "res");
				uni.hideLoading();
				req.msg('核销成功');
				this.couponCode = '';
				this.phoneNumber = '';
				this.isMember = false;
				// this.pageNum = 1;
			});
		},
		getRecordList() {
			req.showLoading();
			req.getRequest('/shopApi/verification/list', {
				pageNum: this.pageNum,
				pageSize: this.pageSize,
				operatorId: req.getStorage('userInfo').introductionId
			}, res => {
				uni.hideLoading();
				this.total = res.total;
				if (this.pageNum === 1) {
					this.recordList = res.data.items;
				} else {
					this.recordList = [...this.recordList, ...res.data.items];
				}
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.verification-container {
	min-height: 100vh;
	background-color: #f5f7fa;
	padding: 30rpx;

	.input-section {
		background: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;

		.input-group {
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;
			position: relative;

			.label {
				width: 120rpx;
				font-size: 28rpx;
				color: #333;
			}

			.input {
				flex: 1;
				height: 80rpx;
				border: 1px solid #EA1306;
				border-radius: 8rpx;
				padding: 0 120rpx 0 20rpx;
				font-size: 28rpx;

				&:disabled {
					background: #f5f5f5;
					border-color: #ddd;
				}
			}

			.scan-btn {
				position: absolute;
				right: 20rpx;
				width: 80rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 99;
				background-color: #fff;
			}

			.check-btn {
				position: absolute;
				right: 0rpx;
				background: #EA1306;
				color: #fff;
				font-size: 24rpx;
				z-index: 99;
				width: 120rpx;
				height: 80rpx;
				border-radius: 0rpx 8rpx 8rpx 0rpx;
				line-height: 80rpx;
				text-align: center;
				&:active {
					opacity: 0.8;
				}
			}
		}
	}

	.record-list {
		background: #fff;
		border-radius: 16rpx;
		padding: 20rpx;

		.record-item {
			background: #fff;
			border-radius: 8rpx;
			padding: 20rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

			.record-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;

				.record-date {
					font-size: 28rpx;
					color: #333;
				}

				.record-status {
					font-size: 24rpx;
				}
			}

			.record-info {
				.info-item {
					display: flex;
					margin-bottom: 10rpx;

					.info-label {
						width: 120rpx;
						font-size: 26rpx;
						color: #666;
					}

					.info-value {
						flex: 1;
						font-size: 26rpx;
						color: #333;
					}
				}
			}
		}

		.empty-record {
			text-align: center;
			padding: 40rpx 0;

			image {
				width: 240rpx;
				height: 240rpx;
				margin-bottom: 20rpx;
			}

			text {
				font-size: 28rpx;
				color: #999;
			}
		}
	}

	.verify-btn {
		width: 100%;
		height: 80rpx;
		background: #EA1306;
		color: #fff;
		border-radius: 40rpx;
		font-size: 28rpx;
		text-align: center;
		line-height: 80rpx;
		margin-top: 30rpx;
	}
}

/deep/ .uni-section {
	margin-bottom: 20rpx;
}

/deep/ .uni-section .uni-section-header__decoration {
	background-color: #EA1306;
}

/deep/ .uni-section__content-title {
	font-size: 32rpx !important;
}
</style>
