<template>
	<view class="withdrawal">
		<!-- 收款账户信息 -->
		<view class="account-box">
			<view class="account-header" @click="toggleContent">
				<view class="toggle-circle" :class="{ 'active': isContentExpanded }"></view>
				<text style="color: #333333;font-size: 28rpx;margin-right: 12rpx;">{{ isContentExpanded ? '收起' : '展开'
				}}</text>
				<view class="infotitle" @click.stop="goUrl('/staff/editAccount/editAccount')">收款账户</view>
			</view>
			<view class="account-content" :class="{ 'collapsed': !isContentExpanded }"
				@click.stop="goUrl('/staff/editAccount/editAccount')">
				<view class="account-info">
					<text class="iconfont">&#xe62b;</text>
					<text class="label">姓名</text>：
					<text class="value">{{ memName || '' }}</text>
				</view>
				<view class="account-info">
					<text class="iconfont">&#xe62b;</text>
					<text class="label">支付宝账号</text>：
					<text class="value">{{ alipayAccount || '' }}</text>
				</view>
			</view>
		</view>

		<!-- Tab切换 -->
		<view class="tab-container">
			<view class="tab-item" :class="{ 'active': activeTab === 0 }" @click="switchTab(0)">
				待提现
				<view class="tab-line" v-if="activeTab === 0"></view>
			</view>
			<view class="tab-item" :class="{ 'active': activeTab === 1 }" @click="switchTab(1)">
				已提现
				<view class="tab-line" v-if="activeTab === 1"></view>
			</view>
		</view>

		<!-- 日期筛选 -->
		<view class="filter-container">
			<view class="date-picker" style="display: flex;align-items: center;background-color: #fff;">
				<uni-datetime-picker v-model="dateRange" type="daterange" :clear-icon="false" :border="false"
					class="date-range-picker" rangeSeparator="至" @change='selectDate' />
				<text class="iconfont" style="font-size: 38rpx;" @click="deleteDate">&#xe6d7;</text>
			</view>
			<view class="total-amount">
				总数：<text class="amount">{{ page.total }}</text>
			</view>
		</view>

		<!-- 表格列表 -->
		<view style="padding: 20rpx 10rpx;box-sizing: border-box;border-radius: 10rpx;background: #FFFFFF;">
			<view class="table-container">
				<view class="table-header">
					<view class="header-item" v-if="activeTab === 0">
						<checkbox-group @change="onSelectAll">
							<checkbox :checked="isAllSelected" />
						</checkbox-group>
					</view>
					<!-- <view class="header-item header-item1">流水</view> -->
					<view class="header-item header-item2">日期</view>
					<view class="header-item header-item3">金额</view>
					<view class="header-item header-item1" v-if="activeTab === 1">状态</view>
				</view>
				<view class="table-body">
					<view class="table-row" v-for="(item, index) in tableData" :key="index"
						:class="{ 'selected': item.selected }">
						<view class="row-item" v-if="activeTab === 0">
							<checkbox-group @change="onSelectItem" :data-index="index">
								<checkbox :checked="item.selected" />
							</checkbox-group>
						</view>
						<!-- <view class="row-item header-item1">{{ item.serial }}</view> -->
						<view class="row-item header-item2">{{ item.redDate }}</view>
						<view class="row-item amount header-item3">{{ item.amount }}</view>
						<view class="header-item header-item1" v-if="activeTab === 1 && item.state === 0"
							style="color: #0B78FF;">待审核</view>
						<view class="header-item header-item1" v-else-if="activeTab === 1 && item.state === 1"
							style="color: #1AAC0D;">已到账
						</view>
						<view class="header-item header-item1" v-else-if="activeTab === 1 && item.state === 2"
							style="color: #EA1306;">已拒绝
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="submit-btn" v-if="activeTab === 0">
			<view @click="submitWithdrawal" class="btn">提交</view>
		</view>
	</view>
</template>

<script>
const req = require("../../utils/request");
export default {
	data() {
		return {
			isContentExpanded: true,
			activeTab: 0,
			dateRange: [],
			isAllSelected: false,
			tableData: [],
			page: {
				pageNum: 1,
				pageSize: 10,
				total: 0
			},
			memName: '',
			alipayAccount: ''
		}
	},
	onShow() {
		this.memName = req.getStorage('userInfo').memName || '';
		this.alipayAccount = req.getStorage('userInfo').alipayAccount || ''
		if (!req.getStorage('userInfo').idNum || !this.memName || !this.alipayAccount) {
			this.goUrl('/staff/editAccount/editAccount');
		};
		this.page.pageNum = 1;
		this.tableData = [];
		this.getPending()
	},
	onReachBottom() {
		if (this.activeTab === 0) {
			this.page.pageNum++;
			this.getPending();
		} else {
			this.page.pageNum++;
			this.getWithdrawal();
		}
	},
	methods: {
		deleteDate() {
			this.dateRange = [];
			this.page.pageNum = 1;
			this.tableData = [];
			if (this.activeTab == 0) {
				this.getPending();
			} else {
				this.getWithdrawal();
			}
		},
		goUrl(url) {
			uni.navigateTo({
				url: url
			})
		},
		toggleContent() {
			this.isContentExpanded = !this.isContentExpanded
		},
		switchTab(index) {
			this.activeTab = index;
			this.page.pageNum = 1;
			this.tableData = [];
			if (this.activeTab == 0) {
				this.getPending();
			} else {
				this.getWithdrawal();
			}
		},
		selectDate() {
			this.page.pageNum = 1;
			this.tableData = [];
			if (this.activeTab == 0) {
				this.getPending();
			} else {
				this.getWithdrawal();
			}
		},
		onSelectAll(e) {
			this.isAllSelected = e.detail.value.length > 0;
			this.tableData.forEach(item => {
				item.selected = this.isAllSelected;
			});
		},
		onSelectItem(e) {
			const index = e.currentTarget.dataset.index;
			this.tableData[index].selected = e.detail.value.length > 0;
			// 更新全选状态
			this.isAllSelected = this.tableData.every(item => item.selected);
		},
		submitWithdrawal() {
			// 处理提现提交
			const selectedItems = this.tableData.filter(item => item.selected);
			console.log(selectedItems);
			if (selectedItems.length === 0) {
				uni.showToast({
					title: '请先勾选要提现的金额',
					icon: 'none'
				});
				return
			};
			// 发送提现请求
			req.postRequest('/shopApi/user/save', selectedItems, res => {
				if (res.code === 200) {
					uni.showToast({
						title: '提现申请提交成功',
						icon: 'success'
					});
					// 刷新列表
					this.page.pageNum = 1;
					this.isAllSelected = false;
					setTimeout(() => {
						this.getPending();
					}, 1500);
				} else {
					uni.showToast({
						title: res.msg || '提现申请提交失败',
						icon: 'none'
					});
				}
			}, false, false, true);
		},
		// 待提现列表
		getPending() {
			req.getRequest('/shopApi/user/pendingWithdrawal', {
				pageNum: this.page.pageNum,
				pageSize: this.page.pageSize,
				startTime: this.dateRange[0],
				endTime: this.dateRange[1]
			}, res => {
				if (this.page.pageNum === 1) {
					this.tableData = res.data.list.length != 0 ? res.data.list.map(item => ({
						...item, // 保留原有的属性
						selected: false // 新增的选中属性，默认值为 false
					})) : [];
				} else {
					const updatedArray = res.data.list.length != 0 ? res.data.list.map(item => ({
						...item,
						selected: false
					})) : [];
					this.tableData = [...this.tableData, ...updatedArray];
				};
				console.log(this.tableData);
				this.page.total = res.data.total;
			})
		},
		// 提现列表
		getWithdrawal() {
			req.getRequest('/shopApi/user/withdrawal', {
				pageNum: this.page.pageNum,
				pageSize: this.page.pageSize,
				startTime: this.dateRange[0],
				endTime: this.dateRange[1]
			}, res => {
				if (this.page.pageNum === 1) {
					this.tableData = [...res.data.list];
				} else {
					this.tableData = [...this.tableData, ...res.data.list];
				};
				this.page.total = res.data.total;
			})
		}
	}
}
</script>

<style>
.withdrawal {
	padding: 8rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
	box-sizing: border-box;
}

.account-box {
	width: 734rpx;
	background: #FFFFFF;
	padding: 20rpx 10rpx;
	margin-bottom: 8rpx;
	box-sizing: border-box;
	border-radius: 10rpx;
}

.account-header {
	display: flex;
	align-items: center;
	height: 88rpx;
	border-radius: 20rpx;
	padding: 0 20rpx;
	background: rgba(0, 78, 214, 0.08);
}

.toggle-circle {
	width: 24rpx;
	height: 24rpx;
	border-radius: 50%;
	margin-right: 10rpx;
	border: 4rpx solid #999;

}

.infotitle {
	width: 288rpx;
	height: 58rpx;
	border-radius: 10rpx;
	background: #FFFFFF;
	color: #EA1306;
	font-weight: 500;
	font-size: 28rpx;
	font-family: Source Han Sans;
	text-align: center;
	line-height: 58rpx;
}

.toggle-circle.active {
	border-color: #EA1306;
}

.account-content {
	padding: 20rpx 20rpx 0;
}

.account-content.collapsed {
	display: none;
}

.account-info {
	margin-bottom: 10rpx;
}

.iconfont {
	color: #EA1306;
	margin-right: 6rpx;
	font-size: 24rpx;
}

.label {
	display: inline-block;
	width: 130rpx;
	font-family: PingFang SC;
	font-size: 24rpx;
	color: #3D3D3D;
	text-align: justify;
	text-align-last: justify;
}

.value {
	color: #606972;
	font-size: 24rpx;
	font-family: Source Han Sans;
}

.tab-container {
	width: 734rpx;
	height: 88rpx;
	border-radius: 10rpx;
	background: #FFFFFF;
	padding: 20rpx 10rpx 0;
	display: flex;
	margin-bottom: 8rpx;
	box-sizing: border-box;
}

.tab-item {
	flex: 1;
	text-align: center;
	position: relative;
}

.tab-item.active {
	color: #EA1306;
}

.tab-line {
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 356rpx;
	height: 4rpx;
	background: #EA1306;
}

.filter-container {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	background: #FFFFFF;
	margin-bottom: 8rpx;
}

.date-picker {
	border: 1px solid #EA1306;
	background: #F5F7FB;
	border-radius: 4rpx;
	width: 530rpx;
}

.date-range-picker {}

.date-range-picker :deep(.uni-date__x-input) {
	height: 60rpx;
	line-height: 60rpx;
	padding: 0 20rpx;
	background: #F5F5F5;
	border-radius: 6rpx;
	font-size: 24rpx;
}

.date-range-picker :deep(.uni-date__x-input-placeholder) {
	color: #999;
}

.date-range-picker :deep(.uni-date__x-input-input) {
	color: #333;
}

.total-amount {
	color: #333;
	font-size: 24rpx;
}

.table-container {
	border-radius: 8rpx;
	overflow: hidden;
	border: 1rpx solid #BFBFBF;
}

.table-header {
	display: flex;
	background: #FEF3F2;
	height: 58rpx;
	border-bottom: 1rpx solid #BFBFBF;
}

.header-item {
	width: 92rpx;
	text-align: center;
	font-size: 24rpx;
	font-family: Source Han Sans;
	font-weight: 500;
	color: #3D3D3D;
	line-height: 58rpx;
	border-right: 1rpx solid #BFBFBF;
}

.header-item:last-child {
	border-right: none;
}

.table-body {
	padding: 0;
}

.table-row {
	display: flex;
	/* padding: 20rpx 0; */
	line-height: 58rpx;
	height: 58rpx;
	border-bottom: 1rpx solid #BFBFBF;
	transition: all 0.3s;
}

.table-row:last-child {
	border-bottom: none;
}

.table-row.selected {}

.table-row.selected .row-item {
	color: #EA1306;
}

.row-item {
	width: 92rpx;
	text-align: center;
	font-size: 24rpx;
	color: #606972;
	transition: all 0.3s;
	border-right: 1rpx solid #BFBFBF;
}

.row-item:last-child {
	border-right: none;
}

.header-item1 {
	width: 192rpx;
}

.header-item2 {
	width: 294rpx;
}

.header-item3 {
	/* width: 136rpx; */
	flex: 1;
}

.amount {
	color: #EA1306;
}

.submit-btn {
	padding: 40rpx;
	background-color: #fff;
	position: fixed;
	bottom: 0;
	left: 0;
	width: 750rpx;
	box-sizing: border-box;
}

.submit-btn .btn {
	background: #EA1306;
	color: #FFFFFF;
	border-radius: 20rpx;
	height: 82rpx;
	font-family: Source Han Sans;
	font-size: 32rpx;
	font-weight: 500;
	text-align: center;
	line-height: 82rpx;
}

/* 修改复选框样式 */
checkbox {
	transform: scale(0.7);
}

checkbox .wx-checkbox-input {
	border-color: #606972 !important;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked {
	background: #EA1306 !important;
	border-color: #EA1306 !important;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
	color: #fff !important;
}
</style>
