page {
  background-color: #f9f9f9;
}
view{
  font-size:28rpx;
}
.content {
  width: 96%;
  margin: 10px auto;
}

.df {
  display: flex;
}

.title {
  padding: 20px 10px 10px 10px;
  position: relative;
  margin: 10px 0;
  border-radius: 5px;
  background-color: #fff;
  box-shadow: 0px 2px 2px 0px #f1f1f1;
}

.shu {
  width: 3px;
  height: 16px;
  margin-right: 5px;
  padding: 2px 0 0 0;
}

.Copay {
  font-weight: 600;
}

.sickPeople {
  display: flex;
  margin: 10px;
}

.rico {
  display: block;
  width: 14rpx;
  height: 21rpx;
  margin: 8px 0 0 auto;
}

.submit {
  position: fixed;
  left: 10%;
  margin-top: 10px;
  bottom: 100rpx;
  width: 80%;
  height: 46px;
  line-height: 46px;
  background: #EA1306;
  font-size: 36rpx;
  color: #fff;
  text-align: center;
  border-radius: 50rpx;
}
.modify_box {
  width: 100%;
  position: fixed;
  margin-top: 10px;
  bottom: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.modify_item {
  height: 46px;
  padding: 0 50rpx;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 50rpx;
  font-size: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.infoDet {
  margin-top: 10px;
}

.leftInfo {
  width: 20%;
  margin-right: 10%;
  text-align: right;
}

.fzc {
  color: #cccccc;
}
.region {
  position: absolute;
  top: 80rpx;
  z-index: 2;
}
.region view {
  background: #fff;
  padding: 20rpx;
}
.uni-select {
  padding: 0 !important;
  border: 0 !important;
}

.uni-select__input-placeholder {
  color: #999 !important;
  font-size: 15px !important;
}

.uni-icons {
  opacity: 0 !important;
}

.uni-select__input-box {
  align-items: normal !important;
}
