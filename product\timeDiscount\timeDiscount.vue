<template>
    <view class="seckill-page">
        <!-- <image style="width:100%;"
            src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241121/78a2a0eddbf84af987f08200c51b7045.png"
            mode="widthFix"></image> -->
        <!-- 商品列表 -->
        <view class="product-list">
            <view class="product-item" v-for="(item, index) in productList" :key="index" @click="goToDetail(item)">
                <view class="product-card">
                    <view class="image-wrapper">
                        <image class="product-image" :src="item.img" mode="heightFix"></image>
                        <view class="date-wrapper">
                            <view style="font-size:35rpx;font-weight: 600;">{{ item.discount * 10 }}折</view>
                            <!-- <image mode="widthFix" style="width:130rpx;"
                                src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241119/5cd2785fdfed412d9d0af37502fe7bbe.png">
                            </image> -->
                        </view>
                    </view>
                    <view class="product-info">
                        <text class="title">{{ item.goodsName }}{{ item.doseSpec ? item.doseSpec : "" }}</text>
                        <view class="stock-info">
                            <view class="stock-item">
                                <text class="stock">门店库存: {{ item.saleStock }}件</text>
                                <text class="stock">云仓库存: {{ item.warehouseStock }}件</text>
                            </view>
                            <text class="limit">限购{{ item.limitNum }}件</text>
                        </view>
                        <view class="price-row" @click.stop="openBuyPopup(item)">
                            <view class="price">
                                <text class="symbol">抢购价¥</text>
                                <text class="number">{{ item.discountPrice }}</text>
                                <text class="original">¥{{ item.salePrice }}</text>
                            </view>
                            <view class="price_button">
                                立即抢购
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 无数据提示 -->
        <view class="empty-tip" v-if="productList.length === 0">
            <text>暂无折扣商品</text>
        </view>

        <!-- 添加购买弹窗组件 -->
        <buy-popup ref="buyPopup" :product="{
            image: selectedProduct.img ? selectedProduct.img : '',
            price: selectedProduct.discountPrice || 0,
            originalPrice: selectedProduct.salePrice || 0,
            maxBuy: selectedProduct.limitNum
        }" :cloud-stock="selectedProduct.warehouseStock" :store-stock="selectedProduct.saleStock"
            :is-cart-action="true" @confirm="confirmBuy" />
    </view>
</template>

<script>
import buyPopup from '@/components/buy-popup/buy-popup.vue'
const req = require('../../utils/request.js')
export default {
    components: {
        buyPopup
    },
    data() {
        return {
            currentDate: '',
            currentSession: 0,
            productList: [],
            selectedProduct: {}, // 添加选中的商品数据
            currentCategory: 0
        }
    },
    onLoad() {
        this.getProductList()
    },
    onPullDownRefresh() {
        this.getProductList()
    },
    onUnload() {

    },
    methods: {
        // 切换场次
        switchSession(index) {
            this.currentSession = index
            // 获取scroll-view组件
            const query = uni.createSelectorQuery().in(this)
            query.select('.scroll-view').boundingClientRect(data => {
                // 计算需要滚动的距离
                const itemWidth = 180 // 每个item的宽度（包含margin）
                const scrollLeft = index * itemWidth - (data.width - itemWidth) / 2

                // 执行滚动
                uni.pageScrollTo({
                    scrollLeft: Math.max(0, scrollLeft),
                    duration: 300
                })
            }).exec()

            this.getProductList()
        },

        // 获取商品列表
        getProductList() {
            // TODO: 调用接口获取商品列表
            req.getRequest("/shopApi/discount/getDiscountListPage", {
                isMore: 0,
                storeId: req.getStorage("currentStore").id,
                pageNum: this.pageNum,
                pageSize: this.pageSize,
            }, res => {
                this.productList = res.data
            })
            setTimeout(() => {
                uni.stopPullDownRefresh()
            }, 500)
        },

        // 跳转商品详情
        goToDetail(item) {
            if (item.saleStock < 1 || item.discountPrice < 1) {
                req.msg('商品已抢完')
                return
            }
            uni.navigateTo({
                url: `/product/detail/detail?id=${item.id}&type=5`
            })
        },

        // 打开购买弹窗
        openBuyPopup(item) {
            this.selectedProduct = item
            this.$nextTick(() => {
                this.$refs.buyPopup.open()
            })
        },

        // 确认购买
        confirmBuy(data) {
            const { quantity, deliveryType } = data
            req.postRequest("/shopApi/purchase/cart", {
                skuId: null,
                merchantId: req.getStorage("currentStore").id,
                quantity,
                productId: this.selectedProduct.id,
                mode: deliveryType,
                state: 0,
                actId: this.selectedProduct.actId || "",
                activityType: 5  // 限时折扣活动
            }, res => {
                if (res.code === 200) {
                    req.msg('加入购物车成功')
                    this.$refs.buyPopup.close()
                }
            })
        },


    }
}
</script>

<style lang="scss" scoped>
@import './timeDiscount.scss';
</style>