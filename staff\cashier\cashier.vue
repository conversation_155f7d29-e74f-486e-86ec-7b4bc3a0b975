<template>
	<view style="background-color: #f8f8f8;min-height: 100vh;padding: 20rpx;box-sizing: border-box;">
		<view class="info_box">
			<uni-section title="员工信息" type="line">
				<view style="padding:0 40rpx 30rpx;">
					<view class="staff_info" style="margin-bottom: 20rpx;">
						<view class="title">姓名</view>：<view class="info">{{ memberName }}</view>
					</view>
					<view class="staff_info">
						<view class="title">所属门店</view>：<view class="info">{{ staffStore ? staffStore : "" }}</view>
					</view>
				</view>
			</uni-section>
		</view>
		<view class="info_box">
			<uni-section title="会员信息" type="line">
				<view style="padding:0 40rpx 30rpx;position: relative;">
					<input placeholder="请输入搜索的会员姓名信息" class="member_input" v-model="menmber" @input="menmberChange"
						confirm-type="search" />
					<!-- 搜索结果列表 -->
					<view class="leader-list" v-if="menmberList.length > 0">
						<view class="leader-item" v-for="(item, index) in menmberList" :key="index"
							@click="selectMenmber(item)">
							{{ item.nickName }}-{{ item.phone }}
						</view>
					</view>
					<view style="display: flex;margin-bottom: 10rpx;">
						<view style="display: flex;margin-right: 80rpx;width: 280rpx;">
							<view class="member_name">姓名</view>：<view class="info_name">{{ data.nickName }}</view>
						</view>
						<view style="display: flex;">
							<view class="member_name">会员余额</view>：<view class="info_name"
								style="color: #E41206;font-weight: 600;">¥{{ data.balance ? data.balance : 0 }}
							</view>
						</view>
					</view>
					<view style="display: flex;margin-bottom: 10rpx;">
						<view style="display: flex;margin-right: 30rpx;width: 330rpx;">
							<view class="member_name">联系方式</view>：<view class="info_name">{{ data.phone }}</view>
						</view>
						<!-- <view style="display: flex;">
							<view class="member_name">可用积分</view>：<view class="info_name"
								style="color: #F89617;font-weight: 600;">200
							</view>
						</view> -->
					</view>
				</view>
			</uni-section>
		</view>
		<view class="info_box">
			<uni-section title="商品信息" type="line">
				<template v-slot:right>
					<view class="chooseGoods" @click="goUrl(`/staff/chooseGoods/chooseGoods?memberId=${data.id}`)">选择商品
					</view>
				</template>
				<view class="goods_info">
					<uni-swipe-action ref="cartItem">
						<view v-for="(item, index) in cartList" :key="index">
							<uni-swipe-action-item :right-options="options" @click="deleteGoods(index)"
								:disabled="manage">
								<view class="goods_">
									<view class="goods_img">
										<image :src="item.pic" style="width: 158rpx;height: 136rpx;"
											:style="{ filter: item.prescriptionDrug == 1 ? 'blur(5px)' : '' }"
											mode="aspectFit">
										</image>
										<image style="position: absolute;top:0;left:0;width: 158rpx;height: 136rpx;"
											v-if="item.prescriptionDrug == 1"
											src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240111/a338c782689a421b8e9ab0848c3c5c10.png">
										</image>
									</view>
									<view style="width: 430rpx;">
										<view style="margin-bottom: 30rpx;">
											<view style="display: flex;">
												<i class="iconfont icon" v-if="item.isOtc === 1">&#xe606;</i>{{
													item.title
												}}
											</view>
											<view style="color: #4E5969;font-size: 24rpx;" v-if="item.doseSpec">规格:{{
												item.doseSpec }}</view>
										</view>
										<view
											style="display: flex;justify-content: space-between;width: 430rpx;align-items: center;">
											<view>
												<text style="color: #EA1306;font-weight: 600;font-size: 32rpx;">¥{{
													item.price1
												}}</text>
												<text
													style="color: #999999;font-size: 24rpx;text-decoration: line-through;margin-left: 10rpx;">¥{{
														item.salePrice }}</text>
											</view>
											<!-- <view style="color: #333333;font-size: 26rpx;">x{{ item.quantity }}</view>
											  -->
											<view
												style="display: flex;justify-content: space-between;align-items: center;">
												<view
													style="background-color: #F5F5F5;border-radius: 50%;width: 50rpx;height: 50rpx;line-height: 50rpx;text-align: center;"
													@click.stop="jianQuantity(item)">
													<i class="iconfont"
														style="color: #4E5969;font-size: 40rpx;">&#xe60a;</i>
												</view>
												<view
													style="color: #EA1306;font-size: 36rpx;font-weight: 700;width: 60rpx;text-align: center;">
													{{ item.quantity }}
												</view>
												<view
													style="background-color: #E91306;border-radius: 50%;width: 50rpx;height: 50rpx;line-height: 50rpx;text-align: center;"
													@click.stop="jiaQuantity(item)">
													<i class="iconfont"
														style="color: #FFFFFF;font-size: 24rpx;">&#xe634;</i>
												</view>
											</view>
										</view>
									</view>
								</view>
							</uni-swipe-action-item>
						</view>
					</uni-swipe-action>
					<view class="details" style="margin-top: 30rpx;">
						<view>商品金额</view>
						<view style="font-weight: 600;">¥{{ detailInfo.sumPrice ? detailInfo.sumPrice.toFixed(2) :
							'0.00' }}</view>
					</view>
					<view class="details" v-if="false">
						<view style="display: flex;"><i class="iconfont "
								style="color: #F99B07;margin-right: 8rpx;">&#xe612;</i>积分
						</view>
						<view style="color: #999999;">暂无可用<uni-icons type="right" size="14"></uni-icons></view>
					</view>
					<view class="details">
						<view style="display: flex;"><i class="iconfont "
								style="color: #9B1FFA;margin-right: 8rpx;">&#xe628;</i>立减
						</view>
						<view style="color: #999999;">¥{{ detailInfo.discountPrice ? detailInfo.discountPrice.toFixed(2)
							:
							'0.00' }}
							<!-- <uni-icons type="right" size="14"></uni-icons> -->
						</view>
					</view>
					<view class="details" @click="goCoupon" v-if="false">
						<view style="display: flex;"><i class="iconfont "
								style="color: #FD1B26;margin-right: 8rpx;">&#xe613;</i>优惠劵
						</view>
						<view style="color: #FD1B26;">-￥{{ detailInfo.couponPrice ?
							detailInfo.couponPrice.toFixed(2)
							: 0.00
							}}<uni-icons type="right" size="14"></uni-icons>
						</view>
					</view>
					<view class="details"
						style="justify-content: flex-end;border-top: 1px solid #F2F3F5;padding-top: 30rpx;">
						小计:<text style="font-weight: 600;">¥{{ detailInfo.amountPrice ?
							detailInfo.amountPrice.toFixed(2) : '0.00' }}</text></view>
				</view>
			</uni-section>
		</view>
		<view class="info_box">
			<uni-section title="支付方式" type="line">
				<view style="padding:0 20rpx 20rpx;">
					<radio-group style="display: flex;" @change="changePayway">
						<label v-for="(item, index) in payWay" :key="item.value" class="radio_label">
							<view>
								<radio :value="item.value" :checked="index === current" style="transform:scale(0.7)"
									color="#EA1306" />
							</view>
							<view>{{ item.name }}</view>
						</label>
					</radio-group>
				</view>
			</uni-section>
		</view>
		<view style="display: flex;align-items: center;justify-content: center;padding: 40rpx 0;">
			<view class="confirm" @click="confirm">确认</view>
			<!-- <view class="cancel">取消</view> -->
		</view>
	</view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
	data() {
		return {
			options: [{
				text: '删除',
				style: {
					backgroundColor: '#ea1306'
				}
			}],
			payWay: [{
				value: 0,
				name: '收银台',
			}, {
				value: 1,
				name: '微信支付',
			}, {
				value: 2,
				name: '现金支付',
			}],
			current: 0,
			memberName: req.getStorage("userInfo").staffName,
			staffStore: req.getStorage("userInfo").storeName,
			menmber: "",
			data: {},
			cartList: [],
			checked: [],
			detailInfo: {},
			couponUserId: "",
			menmberList: [],
			timer: null
		};
	},
	onShow() {
		if (this.data.id) {
			this.getcartList();
		}
	},
	methods: {
		menmberChange(e) {
			const value = e.detail.value.trim();
			if (this.timer) {
				clearTimeout(this.timer);
				this.timer = null;
			}
			if (!value) {
				this.menmberList = [];
				return;
			}
			this.timer = setTimeout(() => {
				req.getRequest('/shopApi/post/getMem', {
					nickName: value
				}, res => {
					this.menmberList = [...res.data];
				});
			}, 500);
		},
		selectMenmber(item) {
			this.menmber = "";
			this.data = item;
			this.menmberList = [];
			this.getcartList();
		},
		changePayway(e) {
			console.log(e);
			this.current = Number(e.detail.value);
		},
		// 删除购物车商品
		deleteGoods(index) {
			let ids_ = this.cartList[index].id;
			req.msgConfirm('确定删除该商品', () => {
				req.postRequest1(`/shopApi/purchase/deletePost/${ids_}`, {}, res => {
					console.log(res, "删除");
					this.$refs.cartItem.closeAll();
					// this.checked = this.checked.filter(item => item !== ids_);
					// getApp().globalData.updateCartBadge()
					this.getcartList();
					this.couponUserId = '';
					// this.getDiscount();
				});
			});
		},
		// 商品减少
		jianQuantity(item) {
			let num = item.quantity;
			num--;
			if (num <= 0) {
				req.msg("商品数量不能再减少了");
				item.quantity = 1;
			} else {
				item.quantity = num;
				this.updateDuantity(item);
			}
		},
		// 商品增加
		jiaQuantity(item) {
			let num = item.quantity;
			let originalNum = item.quantity;
			num++;
			item.quantity = num;
			this.updateDuantity(item, originalNum);
		},
		// 修改购物车商品数量 - 方法
		updateDuantity(item, originalNum) {
			req.postRequest('/shopApi/purchase/quantity', {
				id: item.id,
				quantity: item.quantity,
				mode: 1,
				merchantId: req.getStorage("currentStore").id,
			}, res => {
				console.log(res, "修改商品1111");
				if (res.code == 500) {
					console.log('2222');
					item.quantity = originalNum;
					return req.msg(res.msg);
				};
				if (this.checked.length != 0) {
					this.couponUserId = '';
					this.getDiscount();
				};
			}, true)
		},

		// 获取购物车列表
		getcartList() {
			req.showLoading();
			req.getRequest('/shopApi/purchase/list', {
				mode: 1,
				merchantId: req.getStorage("currentStore").id,
				isDirect: 0,
				uid: this.data.id,
			}, res => {
				console.log(res, "购物车列表");
				this.cartList = [...res.data];
				this.checked = this.cartList.map(item => item.id);
				uni.hideLoading();
				this.getDiscount();
			})
		},
		// 查看结算优惠明细
		getDiscount() {
			let ids_ = this.checked.join(",");
			if (!ids_) {
				this.detailInfo = {};
				this.couponUserId = '';
				return;
			}
			req.getRequest('/shopApi/purchase/discountDetail1', {
				ids: ids_,
				// discountType: 1,
				merchantId: req.getStorage("currentStore").id,
				mode: 1,
				couponUserId: this.couponUserId,
				uid: this.data.id
			}, res => {
				console.log(res, "优惠明细");
				this.detailInfo = res.data;
				// if (this.showAllGoods) {
				// 	this.showList = [...this.detailInfo.goodsList];
				// } else {
				// 	this.showList = this.detailInfo.goodsList.slice(0, 4);
				// }
				// this.chargePrice = (this.detailInfo.deliveryPrice - this.detailInfo.deliverySubtractPrice).toFixed(2);
				this.detailInfo.deliveryPrice = this.detailInfo.deliveryPrice ? this.detailInfo.deliveryPrice.toFixed(2) : '0.00';
				this.couponUserId = res.data.couponUserId ? res.data.couponUserId : "";
			})
		},
		confirm() {
			req.showLoading();
			if (this.current == 0) {
				req.getRequest('/shopApi/post/updateState', {
					uid: this.data.id,
					ids: this.checked.join(',')
				}, res => {
					console.log(res);
					uni.hideLoading();
					uni.navigateBack();
				})
			} else {
				let parme = {
					ids: this.checked, // 商品ID数组
					mode: 1, // 配送方式 1-自提/配送 2-邮寄
					orderType: 1, // 订单类型 1-普通订单
					phone: this.data.phone, // 联系电话
					userName: this.data.nickName, // 用户姓名
					merchantId: req.getStorage("currentStore").id, // 门店ID,快递传60
					addressId: "", // 收货地址ID
					latitude: req.getStorage("location").lat,
					longitude: req.getStorage("location").lng,
					locationAddress: req.getStorage('location').address,
					distance: "",
					remarks: "", // 订单备注
					remark: "",  // 订单备注
					node: "",    // 买家留言
					prescriptionDrug: 0,
					useBalance: 0, // 是否使用余额
					couponId: this.couponUserId,
					balancePayMoney: "",  // 抵扣金额
					payState: this.current == 1 ? 2 : 7, // 支付方式
					uid: this.data.id,
					orderSource: 4,
					salesperson: req.getStorage("userInfo").staffName
				};
				req.postRequest("/shopApi/mp/order/save", parme, res => {
					uni.hideLoading();
					uni.navigateBack();
				})
			}
		},
		goUrl(url) {
			if (!this.data.id) {
				return req.msg("请先输入会员信息");
			}
			uni.navigateTo({
				url: url,
				events: {
					// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
					acceptDataFromOpenedPage: (data) => {
						this.getcartList();
					},
				}
			});
		}
	},
}
</script>

<style lang="scss" src="./cashier.scss"></style>
