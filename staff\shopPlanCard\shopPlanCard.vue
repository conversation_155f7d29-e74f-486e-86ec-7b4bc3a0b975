<template>
	<view class="punch-card-container">
		<view v-if="formIndex != 1">
			<!-- 顶部时间显示 -->
			<view class="time-display">
				<view class="current-time">{{ currentTime }}</view>
				<view class="current-date">{{ currentDate }}</view>
			</view>

			<!-- 定位信息显示 -->
			<view v-if="!type || selectedStore.address" class="location-info">
				<text class="iconfont location-icon">&#xe632;</text>
				<text class="location-text">{{ type ? selectedStore.address : (location.address || '正在获取位置...') }}</text>
				<text v-if="!type" class="refresh-btn" @click="getLocation">刷新</text>
			</view>
			<!-- 门店选择下拉框 -->
			<view class="store-select">
				<uni-data-select :localdata="storeList" :clear="false" label="门店选择" placeholder="请选择门店"
					@change="handleStoreChange">
				</uni-data-select>
			</view>
			<!-- 照片上传 -->
			<block v-if="selectedStore.text">
				<photo-upload-module v-for="(item, key) in uploadModules" :key="key" :title="item.title"
					:reference-image="selectedStore.plaStore && selectedStore.plaStore[key]" :value="selectedStore[key]"
					:image-styles="imageStyles" :remark-field="item.remarkField"
					:remark-value="selectedStore[item.remarkField]" @select="function (e) { selectFile(e, key) }"
					@delete="function (e) { deleteFile(e, key) }" @remark-change="handleRemarkChange" />
			</block>
			<!-- 打卡按钮 -->
			<!-- <view class="punch-btn" @click="submitPunchCard"
				:style="{ backgroundColor: photoUrl.length > 0 ? '#EA1306' : '#f8f8f8', color: photoUrl.length > 0 ? '#fff' : '#999' }">
				打卡
			</view> -->
			<view v-if="!type" class="punch-btn" @click="submitPunchCard" style="background: #EA1306;">
				打卡
			</view>
		</view>
		<!-- 打卡记录 -->
		<view class="record-list" v-if="false">
			<uni-section title="打卡记录" type="line"></uni-section>
			<view class="record-item" v-for="(item, index) in recordList" :key="index">
				<view class="record-header">
					<text class="record-date">{{ item.createTime }}</text>
				</view>
				<view class="record-address">
					<text class="iconfont" style="color: #EA1306;margin-right: 10rpx;">&#xe632;</text>
					<text>{{ item.address }}</text>
				</view>
				<view class="record-photos" v-if="item.pic">
					<image v-for="(img, imgIndex) in JSON.parse(item.pic)" :key="imgIndex" :src="img" mode="aspectFill"
						@click="previewImage(JSON.parse(item.pic), imgIndex)">
					</image>
				</view>
			</view>
			<view v-if="recordList.length === 0" class="empty-record">
				<image
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png"
					mode="aspectFit"></image>
				<text style="display: block;">暂无打卡记录</text>
			</view>
		</view>
	</view>
</template>

<script>
const req = require("../../utils/request");
import PhotoUploadModule from '../../components/PhotoUploadModule.vue';

export default {
	components: {
		PhotoUploadModule
	},
	data() {
		return {
			type: null,
			currentTime: '',
			currentDate: '',
			location: {
				latitude: '',
				longitude: '',
				address: ''
			},
			photoUrl: [],
			imageStyles: {
				width: 100,
				height: 100,
				border: {
					radius: '8rpx'
				}
			},
			timer: null,
			id: 0,
			recordList: [],
			formIndex: "",
			storeList: [],
			selectedStore: {},
			// 上传模块配置
			uploadModules: {
				endFrame: { title: '框架', remarkField: 'frameRemark' },
				pileHead: { title: '堆头', remarkField: 'headRemark' },
				cashRegister: { title: '收银台', remarkField: 'cashRemark' },
				goodsShelves: { title: '货架首层', remarkField: 'shelvesRemark' },
				premium: { title: '精品背柜', remarkField: 'premiumRemark' },
				// promotion: { title: '卖场促销', remarkField: 'promotionRemark' },
				employee: { title: '员工仪表', remarkField: 'employeeRemark' },
				// drugDisplay: { title: '药品陈列', remarkField: 'drugDisplayRemark' },
				// prescriptionSales: { title: '处方销售记录', remarkField: 'prescriptionSalesRemark' },
				// serviceProcess: { title: '服务流程', remarkField: 'serviceProcessRemark' },
				competitor: { title: '竞争对手门店', remarkField: 'competitorRemark' },
				basicVerification: { title: '基础检核', remarkField: 'basicVerificationRemark' }
			}
		}
	},
	onLoad(options) {
		console.log(options);

		this.id = Number(options.id);
		this.type = options.type
		this.formIndex = options.formIndex ? Number(options.formIndex) : "";
		this.getClockIn();
		this.startTimer();
		this.getLocation();
		// this.getRecordList();
	},
	onUnload() {
		if (this.timer) {
			clearInterval(this.timer);
		}
	},
	methods: {
		getClockIn() {
			req.getRequest("/shopApi/goOut/getClockIn", {
				id: this.id
			}, res => {
				this.storeList = res.data;
				this.storeList.forEach((item, index) => {
					item.value = index;
					item.text = item.storeName;
					// 初始化所有照片上传数组
					Object.keys(this.uploadModules).forEach(key => {
						// 如果字段存在且有值，将字符串数组转换为对象数组
						if (item[key]) {
							try {
								const urls = JSON.parse(item[key]);
								item[key] = urls.map(url => ({ url }));
							} catch (e) {
								item[key] = [];
							}
						} else {
							item[key] = [];
						}
					});
				});
			})
		},
		// 开始计时器更新时间
		startTimer() {
			this.updateTime();
			this.timer = setInterval(() => {
				this.updateTime();
			}, 1000);
		},
		// 更新时间显示
		updateTime() {
			const now = new Date();
			// 格式化时间，避免显示 GMT+0800(CST)
			const hours = now.getHours().toString().padStart(2, '0');
			const minutes = now.getMinutes().toString().padStart(2, '0');
			const seconds = now.getSeconds().toString().padStart(2, '0');
			this.currentTime = `${hours}:${minutes}:${seconds}`;

			// 格式化日期
			const year = now.getFullYear();
			const month = (now.getMonth() + 1).toString().padStart(2, '0');
			const date = now.getDate().toString().padStart(2, '0');
			const weekDay = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][now.getDay()];
			this.currentDate = `${year}年${month}月${date}日 ${weekDay}`;
		},
		// 获取定位
		getLocation() {
			req.showLoading('获取位置中...');
			req.getLocationInfo(res => {
				this.location.address = res.address;
				console.log(res, 'res');
				uni.hideLoading();
			});
		},
		// 拍照上传
		selectFile(e, type) {
			e.tempFilePaths.forEach(item => {
				req.uploadImg('/shopApi/ask/uploads', item, res => {
					this.selectedStore[type].push({ url: res[0] });
				});
			});
		},

		// 删除照片
		deleteFile(e, type) {
			this.selectedStore[type].splice(e.index, 1);
		},
		// 提交打卡
		submitPunchCard() {
			if (!this.location.address) {
				req.msg('请等待位置信息获取完成');
				return;
			}

			if (!this.selectedStore.text) {
				req.msg('请先选择门店');
				return;
			}

			// 检查所有照片是否都已上传
			const emptyModules = [];
			Object.keys(this.uploadModules).forEach(key => {
				if (!this.selectedStore[key]?.length) {
					emptyModules.push(this.uploadModules[key].title);
				}
			});

			if (emptyModules.length > 0) {
				req.msg(`请上传${emptyModules.join('、')}照片`);
				return;
			}

			// 处理每个模块的图片数据
			const submitData = { ...this.selectedStore };
			Object.keys(this.uploadModules).forEach(key => {
				if (submitData[key] && submitData[key].length) {
					submitData[key] = JSON.stringify(submitData[key].map(item => item.url));
				}
			});

			console.log(submitData, 'submitData');
			req.showLoading('提交中...');
			req.postRequest('/shopApi/goOut/patrolClockIn', {
				...submitData,
				address: this.location.address,
			}, res => {
				uni.hideLoading();
				req.msg('打卡成功');
				// 清空所有已上传的照片
				this.selectedStore = {};
				this.getClockIn();
			});
		},
		// 获取打卡记录
		getRecordList() {
			req.showLoading('加载中');
			req.getRequest('/shopApi/goOut/outClockInDetail', {
				id: this.id,
			}, res => {
				this.recordList = res.data;
				uni.hideLoading();
			});
		},
		// 预览图片
		previewImage(urls, current) {
			uni.previewImage({
				urls: urls,
				current: current
			});
		},
		// 门店选择改变事件处理
		handleStoreChange(value) {
			this.selectedStore = this.storeList[value];
			console.log('选中的门店完整信息：', this.selectedStore);
		},
		// 建议内容变化处理
		handleRemarkChange(e) {
			this.selectedStore[e.field] = e.value;
		}
	}
}
</script>

<style lang="scss" scoped>
@import "./shopPlanCard.scss";
</style>
