<template>
    <view style="background:#f9f9f9;min-height: 100vh;">
        <view class="search">
            <uni-easyinput :styles="{ borderColor: '#f00' }" prefixIcon="search" v-model="searchValue"
                placeholder="输入商品名称搜索订单" @input="handleSearch"></uni-easyinput>
        </view>

        <!-- 标签栏 -->
        <view class="tabs-box">
            <scroll-view scroll-x="true" class="scroll-view" :scroll-into-view="scrollInto" scroll-with-animation>
                <view class="tabs">
                    <view v-for="(item, index) in tabList" :key="index" :id="'tab' + index" class="tab-item"
                        :class="{ active: currentTab === index }" @tap="changeTab(index)">
                        {{ item.name }}
                    </view>
                </view>
            </scroll-view>
        </view>

        <!-- 内容区域 -->
        <view v-if="orderList.length > 0" class="order">
            <view @click="currentTab != 6 && goUrl('/shoppingCart/orderDetails/orderDetails?id=' + item.id)"
                class="order_item" v-for="(item, index) in orderList" :key="index">
                <view class="order_item_top">
                    <view class="order_id">
                        <view class="order_id_text">订单编号:{{ item.id }}</view>
                        <view @click.stop="copyOrderId(item.id)"
                            style="border:1px solid red;padding:0 20rpx;border-radius: 30rpx;color:red;font-size:22rpx;margin-left:20rpx;">
                            复制</view>
                    </view>
                    <view v-if="currentTab != 6" style="color:red;">{{ item.stateName }}</view>
                </view>
                <view class="order_item_content">
                    <view @click="goUrl(`/mine/logistics/logistics?orderId=${item.id}&mode=${item.mode}`)"
                        v-if="item.deliverInfo && item.mode == 2" class="logistics">
                        <text class="iconfont" style="color:red;">&#xe693;</text>
                        <view class="logistics_info" style="width:450rpx;">
                            <view class="logistics_text"
                                style=" overflow: hidden;text-overflow: ellipsis;white-space: nowrap;width: 100%;">{{
                                    item.deliverInfo.value }}</view>
                            <view style="color:#999;font-size:23rpx;margin-top:10rpx;">{{ item.deliverInfo.time }}
                            </view>
                        </view>
                        <text class="iconfont">&#xe667;</text>
                    </view>
                    <view v-if="item.mtInfo && item.mode == 3" class="logistics">
                        <text class="iconfont" style="color:red;font-size:40rpx;">&#xe63d;</text>
                        <view class="logistics_info" style="width:450rpx;">
                            <view class="logistics_text"
                                style=" overflow: hidden;text-overflow: ellipsis;white-space: nowrap;width: 100%;">{{
                                    item.mtInfo.status }}</view>
                            <view style="color:#999;font-size:23rpx;margin-top:10rpx;">{{ item.mtInfo.courierName }}-{{
                                item.mtInfo.courierPhone }}
                            </view>
                        </view>
                        <text class="iconfont">&#xe667;</text>
                    </view>
                    <view class="goods" v-for="(it, idx) in item.products" :key="idx">
                        <view style="display: flex;align-items: center;position: relative;">
                            <image :src="it.pic" mode="widthFix"
                                style="width:180rpx;background: #EDF3FA;border-radius: 15rpx;"
                                :style="{ filter: it.prescriptionDrug == 1 ? 'blur(5px)' : '' }"></image>
                            <image style="position: absolute;top:0;left:0;width: 158rpx;height: 136rpx;"
                                v-if="it.prescriptionDrug == 1"
                                src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240111/a338c782689a421b8e9ab0848c3c5c10.png">
                            </image>
                            <view class="goods_info">
                                <view>{{ it.title }}</view>
                                <view v-if="it.doseSpec" style="font-size:22rpx;color:#666;">规格:{{ it.doseSpec }}</view>
                                <view style="font-size:22rpx;color:#666;">数量:{{ it.quantity }}{{
                                    it.doseUnit ? it.doseUnit : '' }}</view>
                            </view>
                        </view>
                        <view v-if="currentTab != 6" class="goods_price">¥{{ it.salePrice }}</view>
                    </view>
                    <view class="total_price">
                        <view class="price_item">
                            <view v-if="item.hisPersonalMedicalCardFee" class="medical_pay">医保支付：<text class="price">¥{{
                                item.hisPersonalMedicalCardFee }}</text>
                            </view>
                            <view v-if="item.hisWechatFee" class="medical_pay">微信支付：<text class="price">¥{{
                                item.hisWechatFee }}</text>
                            </view>
                            <view v-if="item.hisInsuranceFee" class="overall_pay">统筹支付：<text class="price">¥{{
                                item.hisInsuranceFee }}</text>
                            </view>
                        </view>
                        <view class="total" v-if="item.isTisane">代煎费用：<text class="price">¥{{ item.tisaneFee }}</text>
                        </view>
                        <view v-if="currentTab != 6">
                            <view class="total" v-if="item.isOverallPlanning != 1">总金额：<text class="price">¥{{
                                item.balancePayMoney + item.payMoney }}</text></view>
                            <view class="total" v-else>总金额：<text class="price">¥{{ item.payMoney }}</text></view>
                        </view>
                    </view>
                </view>
                <view class="order_item_bottom">
                    <view class="time">时间：{{ item.createDate }}</view>
                    <view class="btn" v-if="currentTab != 6">
                        <block v-if="item.payState != 7">
                            <view v-if="item.state == 1" @click.stop="cancelOrder(item.id)" class="btn_item btn_item2">
                                取消订单
                            </view>
                            <view v-if="item.state == 1" @click.stop="payOrder(item)" class="btn_item">立即付款</view>
                            <!-- <view v-if="item.state == 5" @click.stop="" class="btn_item btn_item2">查看配送信息</view> -->
                            <view v-if="item.state == 5 || item.state == 6" @click.stop="confirmOrder(item.id)"
                                class="btn_item">确认收货</view>
                            <view class="btn_item btn_item2" @click.stop="oneMoreOrder(item)" v-if="item.state == 7">
                                再来一单
                            </view>
                        </block>
                        <view v-if="item.state == 20"
                            @click.stop="goUrl(`/shoppingCart/tcConsultation/tcConsultation?orderId=${item.id}&payState=${item.payState}`)"
                            class="btn_item">去开方</view>
                        <!-- <view v-if="item.state == 3" @click.stop="confirmOrder(item.id)" class="btn_item">提醒发货</view> -->

                        <!-- <view class="btn_item" @click.stop="oneMoreOrder(item.id)" v-if="item.state == 7">去评价</view> -->
                    </view>
                    <view class="btn" v-else>
                        <view v-if="item.tisanePay == 0" @click.stop="payTisane(item)" class="btn_item">
                            支付代煎费用
                        </view>
                        <view v-else @click.stop="goTisaneDetails(item.registerId)" class="btn_item">
                            代煎详情
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <!-- <view v-else-if="currentTab == 6" class="order">
            
        </view> -->
        <view
            style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top:200rpx;"
            v-else>
            <image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241116/a1a17cdb255740e7a69beb4e5b1b40da.png"
                mode="widthFix" style="width:50%;"></image>
            <view style="color:#4E5969;margin-top:20rpx;">暂无订单~</view>
        </view>

        <uni-popup ref="showMerchantDialog" :mask-click="false">
            <view class="popup-content">
                <view class="popup-title">配送方式</view>
                <view class="delivery-options">
                    <view class="delivery-item" :class="{ active: mode === 1 }" @tap="selectmode(1)">
                        <view class="radio-wrap">
                            <view class="radio-inner"></view>
                        </view>
                        <text>到店自提</text>
                    </view>
                    <view class="delivery-item" :class="{ active: mode === 2 }" @tap="selectmode(2)">
                        <view class="radio-wrap">
                            <view class="radio-inner"></view>
                        </view>
                        <text>快递配送</text>
                    </view>
                </view>
                <!-- 自提信息 -->
                <view class="delivery-info" v-if="mode === 1">
                    <view class="info-item">
                        <view class="label" @click="$refs.merchantpopup.open()">自提点：{{ selectmerchant.storeName ?
                            selectmerchant.storeName : '请选择门店' }}</view>
                    </view>
                    <view class="area">
                        <!-- <image src="/static/dzico.png" class="dzico"></image> -->
                        <text class="address">{{ selectmerchant.address }}</text>
                    </view>
                </view>
                <!-- 快递信息 -->
                <view class="delivery-info" v-else>
                    <view class="area" @click="goUrl('/mine/addressList/addressList?choice=true')"
                        style="margin-bottom: 30rpx;">
                        <!-- <image src="/static/dzico.png" class="dzico"></image> -->
                        <view class="flex" v-if="address.address || address.house">
                            <view class="name">{{ address.address }}{{ address.house }}</view>
                            <view class="text" v-if="address.name">{{ address.name }}<text>{{ address.phone
                                    }}</text></view>
                        </view>
                        <view class="flex" v-else>请添加收货地址</view>
                        <!-- <i class="iconfont">&#xe642;</i> -->
                    </view>
                    <view class="area">
                        <!-- <image src="/static/dzico.png" class="dzico"></image> -->
                        <view class="flex">
                            <view class="name">
                                {{ mode == 3 ? '配送点：' : '服务点：' }}{{ selectmerchant.storeName || '' }}
                            </view>
                            <view class="text">{{ selectmerchant.address }}</view>
                        </view>
                    </view>
                </view>
                <view class="popup-buttons">
                    <view class="confirm-btn" @tap="confirmDelivery">确定</view>
                </view>
            </view>
        </uni-popup>
        <uni-popup ref="merchantpopup" class="merchant-dialog" type="bottom" background-color="#fff"
            border-radius="20rpx 20rpx 0 0">
            <view class="dialog-content">
                <view class="dialog-title">请选择门店</view>
                <scroll-view scroll-y class="scroll-box">
                    <view v-for="merchant in merchantList" :key="merchant.id" class="merchant-item"
                        @click="selectMerchantAndPay(merchant)">
                        {{ merchant.storeName }}
                    </view>
                </scroll-view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
// 1 未付款 2 待确认 3 待发货 4 待提货 5 配送中 6 已签收 7 订单完成 8 订单取消 9 退货中 10 完成退货 11 支付失败 15 进行中 16预约下单 20 未开方 30 售后待审核 32 售后审查失败 35 商品待退回 36商品退回中\r\n40 退款待审 41 退款中 42 退款失败 43 退款成功 49取消售后\r\n处方订单 21 开方中 22 开方失败\r\n25 待审方 26 审方中 27 审方失败
const req = require('../../utils/request')
export default {
    data() {
        return {
            searchValue: "",
            currentTab: 0,
            scrollInto: '',
            orderList: [],
            tabList: [
                { name: '全部', state: 0 },
                { name: '待付款', state: 1 },
                // { name: '待问诊', state: 99 },
                { name: '待发货', state: 3 },
                { name: '待收货', state: 5 },
                { name: '已完成', state: 7 },
                { name: '售后/退货', state: 9 },
                { name: '代煎', state: 10 }
            ],
            page: 1,
            hasMore: true,
            isLoading: false,
            timer: null,
            merchantList: [],
            payOrderItem: {},
            mode: 1,
            selectmerchant: '',
            address: {},
        }
    },
    onLoad(options) {
        console.log(options, '------');
        if (options.from === 'his') {
            this.hisPayOrder(Number(options.orderId));
        };
        if (options.currentTab) {
            this.currentTab = Number(options.currentTab)
            this.scrollInto = 'tab' + options.currentTab;
        }
    },
    onShow() {
        this.getData()
    },
    onReachBottom() {
        if (this.hasMore && !this.isLoading) {
            this.page++
            this.getData()
        }
    },
    methods: {
        // 选择配送方式
        selectmode(type) {
            this.mode = type;
            this.selectmerchant = '';
            this.getstore();
            if (type == 2) {
                // 获取收获地址
                req.getRequest("/shopApi/mp/address/list", {
                    page: 1,
                    limit: 20
                }, res => {
                    console.log(res, "获取收获列表");
                    if (res.data.list.length) {
                        this.address = res.data.list[0];
                    }
                    // console.log(this.address);
                })
            };
        },
        hisPayOrder(id) {
            // 弹窗选择
            this.payOrderItem.id = id;
            this.getstore();

        },
        goUrl(url) {
            uni.navigateTo({
                url: url
            })
        },
        handleSearch(e) {
            if (this.timer) {
                clearTimeout(this.timer)
            }

            this.timer = setTimeout(() => {
                this.searchValue = e
                this.page = 1
                this.hasMore = true
                this.getData()
            }, 500)
        },
        getData() {
            if (!this.hasMore || this.isLoading) return
            this.isLoading = true
            if (this.tabList[this.currentTab].state == 10) {
                req.getRequest("/shopApi/mp/user/tisaneList", {
                    uid: req.getStorage('userInfo').id,
                    page: this.page,
                    limit: 99,
                }, res => {
                    this.isLoading = false
                    this.hasMore = false
                    uni.hideLoading()
                    this.orderList = res.data
                })
            } else if (this.tabList[this.currentTab].state === 9) {
                // 售后接口
                req.postRequest("/shopApi/mp/user/refundList", {
                    uid: req.getStorage('userInfo').id,
                    noFinish: 1,
                    page: this.page,
                    noFinish: 1,
                    limit: 10,
                    search: this.searchValue
                }, res => {
                    uni.hideLoading()
                    this.isLoading = false
                    if (res.code == 500) return req.msg(res.msg)
                    if (res.data && res.data.length > 0) {
                        if (this.page === 1) {
                            this.orderList = res.data
                        } else {
                            this.orderList = [...this.orderList, ...res.data]
                        }
                    } else {
                        this.hasMore = false
                        if (this.page === 1) {
                            this.orderList = []
                        }
                    }
                }, true)
            } else {
                // 原有的订单列表接口
                req.getRequest("/shopApi/mp/user/list", {
                    page: this.page,
                    limit: 10,
                    uid: req.getStorage('userInfo').id,
                    state: this.tabList[this.currentTab].state,
                    search: this.searchValue
                }, res => {
                    uni.hideLoading()
                    this.isLoading = false
                    if (res.data && res.data.length > 0) {
                        if (this.page === 1) {
                            this.orderList = res.data
                        } else {
                            this.orderList = [...this.orderList, ...res.data]
                        }
                    } else {
                        this.hasMore = false
                        if (this.page === 1) {
                            this.orderList = []
                        }
                    }

                })
            }
        },
        cancelOrder(id) {
            uni.showModal({
                title: '提示',
                content: '确定要取消该订单吗?',
                success: (res) => {
                    if (res.confirm) {
                        req.postRequest("/shopApi/mp/order/cancel/" + id, {}, res => {
                            if (res.code == 200) {
                                req.msg('取消成功')
                                this.page = 1
                                this.hasMore = true
                                this.getData()
                            }
                        })
                    }
                }
            })
        },
        changeTab(index) {
            this.currentTab = index
            this.scrollInto = 'tab' + index
            this.page = 1
            this.hasMore = true
            this.orderList = []
            req.showLoading()
            this.getData()
        },
        payOrder(item) {
            if (!item.merchantId) {
                // 没有选择门店，弹窗选择
                this.payOrderItem = item;
                // this.$refs.showMerchantDialog.open();
                this.getstore();
                return;
            }
            // 有门店，直接付款
            req.payOrder(item.id, res => {
                this.payOrderItem = {};
                if (item.mode == 3) {
                    req.getRequest("/shopApi/create/order/createByShop", {
                        id: item.id
                    }, res => {
                        if (res.code == 200) {
                            this.page = 1
                            this.hasMore = true
                            this.getData()
                        }
                    })
                } else {
                    this.page = 1
                    this.hasMore = true
                    this.getData()
                }
            })
        },
        // 获取门店
        getstore() {
            req.getRequest('/shopApi/mp/order/getStoreList', {
                orderId: this.payOrderItem.id,
                mode: this.mode,
            }, res => {
                console.log(res, '门店列表');
                this.$refs.showMerchantDialog.open();
                this.merchantList = res.data;
                if (this.mode == 2) {
                    this.selectmerchant = this.merchantList[0];
                };
            });
        },
        // 确认收货
        confirmOrder(id) {
            req.msgConfirm('是否确认收货？', () => {
                req.postRequest1(`/shopApi/mp/order/receiving/${id}`, {}, res => {
                    console.log(res, "确认收货");
                    req.msg("确认收货成功");
                    this.page = 1
                    this.hasMore = true
                    this.getData();
                })
            });
        },
        // 再来一单
        oneMoreOrder(item) {
            console.log(item);
            req.postRequest1(`/shopApi/mp/order/tryAgain/${item.id}`, {}, res => {
                console.log(res, "再来一单");
                if (res.data) {
                    let mode_ = item.mode == 3 || item.mode == 1 ? 1 : 2
                    uni.navigateTo({
                        url: `/shoppingCart/confirmOrder/confirmOrder?prescriptionDrug=${true}&mode=${mode_}&tabMode=${item.mode}&goodslist=${res.data}&merchantId=${item.merchantId}&discountType=1&toform=1`
                    });
                }
            });
        },
        // swiperChange(e) {
        //     this.currentTab = e.detail.current;
        //     this.scrollInto = 'tab' + e.detail.current;
        // },
        // 添加复制订单号方法
        copyOrderId(id) {
            uni.setClipboardData({
                data: id.toString(),
                success: () => {
                    req.msg('复制成功')
                }
            })
        },
        payTisane(item) {
            req.showLoading()
            req.postRequest("/shopApi/mp/order/tisane/pay", {
                id: item.id
            }, res => {
                uni.hideLoading()
                res.data = JSON.parse(res.data)
                uni.requestPayment({
                    package: res.data.miniPayRequest.package,
                    appId: res.data.miniPayRequest.appId,
                    paySign: res.data.miniPayRequest.paySign,
                    nonceStr: res.data.miniPayRequest.nonceStr,
                    timeStamp: res.data.miniPayRequest.timeStamp,
                    signType: res.data.miniPayRequest.signType,
                    success: res => {
                        req.showLoading()
                        this.timer = setInterval(() => {
                            req.getRequest("/shopApi/mp/order/tisaneCheck", {
                                id: item.id
                            }, res => {
                                if (res.data.isSuccess) {
                                    uni.hideLoading()
                                    clearInterval(this.timer)
                                    this.timer = null
                                    req.msg('支付成功')
                                    this.page = 1
                                    this.hasMore = true
                                    this.getData()
                                    this.goTisaneDetails(item.registerId)
                                }
                            })
                        }, 3000);
                    },
                    fail: err => {
                        console.log(err);
                    }
                })
            })
        },
        //详情代煎详情
        goTisaneDetails(registerId) {
            wx.navigateToMiniProgram({
                appId: 'wx017bcf0b1797fef4',
                path: `reserve/offlineOpen/offlineOpen?id=${registerId}`,
            })
        },
        selectMerchantAndPay(merchantId) {
            this.$refs.merchantpopup.close();
            this.selectmerchant = merchantId;
        },
        confirmDelivery() {
            console.log(this.mode, this.address.id, this.payOrderItem.id, 'hhhh');
            req.postRequest('/shopApi/mp/order/updateOrderStore', {
                id: this.payOrderItem.id,
                merchantId: this.selectmerchant.id,
                mode: this.mode,
                addressId: this.mode === 2 ? this.address.id : ''
            }, res => {
                this.payOrderItem.merchantId = this.selectmerchant.id;
                this.$refs.showMerchantDialog.close();
                this.payOrder(this.payOrderItem);
            });
        }
    }
}
</script>

<style lang="scss" src="./order.scss"></style>