<template>
    <view class="card-replace">
        <!-- 顶部日期选择和筛选 -->
        <view class="header">
            <uni-datetime-picker v-model="date" type="daterange" @change="dateChange" />
            <uni-data-select v-model="state" :clear="false" :localdata="stateList"
                @change="stateChange"></uni-data-select>
            <view @click=" $refs.popup.open()" class="add-btn">
                <text style="font-size: 28rpx;">新增</text>
            </view>
        </view>

        <!-- 申请列表 -->
        <view class="application-list">
            <view class="application-item" v-for="(item, index) in applications" :key="index"
                :style="item.state != 0 ? 'padding: 80rpx 15rpx 80rpx;' : 'padding: 80rpx 15rpx 20rpx;'">
                <view v-if="item.state == 0" class="status-tag" style="background: #ff6b35;">审批中</view>
                <view v-if="item.state == 1" class="status-tag" style="background: #67c23a;">已通过</view>
                <view v-if="item.state == 2 || item.state == 3" class="status-tag" style="background: #f23030;">已拒绝
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe62b;</text>
                    <text class="label">所在部门：</text>
                    <text class="value">{{ item.divName }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe62b;</text>
                    <text class="label">申请人：</text>
                    <text class="value">{{ item.applyUser }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe62d;</text>
                    <text class="label">开始时间：</text>
                    <text class="value">{{ item.startTime }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe62d;</text>
                    <text class="label">结束时间：</text>
                    <text class="value">{{ item.endTime }}</text>
                </view>
                <!-- v-if="item.state != 2"  -->
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                    <text class="label">外出类型：</text>
                    <text class="value">{{ item.outType == 0 ? '外出' : '巡店' }}</text>
                </view>
                <view class="info-row" v-if="item.outType == 1" style="align-items: flex-start;">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                    <text class="label">巡店地址：</text>
                    <text class="value">{{ item.outStore }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                    <text class="label" style="width:110px;">外出时长(小时)：</text>
                    <text class="value">{{ item.howTime }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                    <text class="label">外出事由：</text>
                    <text class="value">{{ item.reason }}</text>
                </view>
                <view class="info-row" style="align-items: flex-start;">
                    <text class="iconfont"
                        style="font-size:24rpx;color:red;margin-right:5rpx;margin-top: 6rpx;">&#xe671;</text>
                    <text class="label">上传图片：</text>
                    <view style="flex: 1;display: flex;flex-wrap: wrap;align-items: center;">
                        <view
                            style="border-radius: 12rpx;margin-right: 20rpx;margin-bottom: 20rpx;font-size: 28rpx;line-height: 46rpx;position: relative;border: 1px solid #f5f5f5;width: 130rpx;height: 130rpx;"
                            v-for="(it, ind) in JSON.parse(item.photo)" :key="index" @click="chooseImg(it)">
                            <image :src="it" style="width: 130rpx;height: 130rpx;border-radius: 12rpx;"></image>
                        </view>
                    </view>
                </view>
                <view class="info-row" v-if="item.state == 0">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                    <text class="label">审批进度：</text>
                    <view @click="open(item)"
                        style="background-color: #ea1306;color: #fff;padding: 10rpx 20rpx;border-radius: 12rpx;">
                        查看审批进度</view>
                </view>
                <view class="punchCard" @click="goUrl(`/staff/punchCard/punchCard?id=${item.id}`)">打卡</view>
            </view>
            <view v-if="applications.length === 0" style="text-align: center;color: #999;">
                <image mode=""
                    src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png"
                    style="width: 372rpx;height: 312rpx;margin-top: 50rpx;">
                </image>
                <view style="margin-top: 10rpx;font-size: 28rpx;">暂无数据</view>
            </view>
        </view>
        <uni-popup :is-mask-click="false" ref="popup" type="bottom" border-radius="10px 10px 0 0">
            <view class="popup-content">
                <view class="popup-header">
                    <text>外出申请</text>
                    <text class="close-icon" @click="$refs.popup.close()">×</text>
                </view>
                <view class="popup-body">
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">申请人：</text>
                        <input type="text" disabled v-model="formData.name" class="input"
                            style="background: #f5f5f5;" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">所在部门：</text>
                        <input type="text" disabled v-model="formData.divName" class="input"
                            style="background: #f5f5f5;" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">外出类型：</text>
                        <uni-data-select v-model="outType" :localdata="outTypeList" @change="outTypeChange"
                            class="selectleader"></uni-data-select>
                    </view>
                    <view class="form-item" v-if="outType === 1">
                        <text class="required">*</text>
                        <text class="label">选择门店：</text>
                        <view class="store-select">
                            <view class="selected-stores" v-if="selectedStores.length > 0">
                                <view v-for="(store, index) in selectedStores" :key="store.value" class="store-tag">
                                    <text>{{ store.text }}</text>
                                    <text class="delete-icon" @click.stop="removeStore(index)">×</text>
                                </view>
                            </view>
                            <view class="store-dropdown">
                                <uni-data-select v-model="tempStoreId" :localdata="availableStores"
                                    @change="storeChange" class="selectleader"></uni-data-select>
                            </view>
                        </view>
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">外出时间：</text>
                        <uni-datetime-picker v-model="formData.date" type="datetimerange" @change="calculateHours">
                            <view class="date-input">
                                <view
                                    style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
                                    <text class="iconfont"
                                        style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
                                </view>
                                <text>{{ formData.date[0] || '请选择' }}-{{ formData.date[1] || '请选择' }}</text>
                            </view>
                        </uni-datetime-picker>
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">时长(小时)：</text>
                        <input type="text" disabled v-model="formData.hours" class="input"
                            style="background: #f5f5f5;" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">部门领导：</text>
                        <uni-data-select v-model="value" :localdata="range" @change="leaderChange"
                            class="selectleader"></uni-data-select>
                        <!-- <input type="text" v-model="leaderInputValue" placeholder="请输入部门领导" class="input"
                            @input="leaderChange" />
                        <view class="leader-list" v-if="leaderList.length > 0">
                            <view class="leader-item" v-for="(item, index) in leaderList" :key="index"
                                @click="selectLeader(item)">
                                {{ item.name }}
                            </view>
                        </view> -->
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">外出事由：</text>
                        <textarea v-model="formData.reason" placeholder="请输入申请原因" class="textarea" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">上传图片：</text>
                        <view style="flex: 1;display: flex;flex-wrap: wrap;align-items: center;">
                            <view
                                style="border-radius: 12rpx;margin-right: 20rpx;margin-bottom: 20rpx;font-size: 28rpx;line-height: 46rpx;position: relative;border: 1px solid #f5f5f5;width: 130rpx;height: 130rpx;"
                                v-for="(item, index) in attachmentList" :key="index" @click="chooseImg(item)">
                                <image :src="item" style="width: 130rpx;height: 130rpx;border-radius: 12rpx;"></image>
                                <i style="border: 1px solid #333; color: #333;width: 30rpx;height: 30rpx;text-align: center;line-height: 26rpx;position: absolute;top: -16rpx;right: -16rpx;border-radius: 50%;background-color: #fff;"
                                    @click.stop="del_img(index)">x</i>
                            </view>
                            <view
                                style="border: 1px solid #f5f5f5;border-radius: 12rpx;color: #f5f5f5;line-height: 120rpx;width: 130rpx;height: 130rpx;font-size: 120rpx;text-align: center;"
                                @click="uploadFile">
                                +
                            </view>
                        </view>
                    </view>
                    <view class="form-footer">
                        <view class="btn save" @click="saveForm">提交</view>
                        <view class="btn cancel" @click="$refs.popup.close()">取消</view>
                    </view>
                </view>
            </view>
        </uni-popup>
        <uni-popup :is-mask-click="false" ref="approve" type="bottom" border-radius="10px 10px 0 0">
            <view class="popup-content1">
                <view class="popup-header">
                    <text>审批进度</text>
                    <text class="close-icon" @click="$refs.approve.close()">×</text>
                </view>
                <view class="popup-body">
                    <view class="step-container">
                        <view class="step" v-for="(item, index) in stepList" :key="index">
                            <view class="step-number"></view>
                            <view class="step-title">
                                <view>开始时间:{{ item.startTime }}</view>
                                <view>{{ item.assignee }}：{{ item.taskName }}</view>
                                <view v-if="item.comment">审批意见：{{ item.comment }}</view>
                                <view v-if="item.endTime">结束时间:{{ item.endTime }}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const req = require("../../utils/request");

export default {
    data() {
        return {
            date: [],
            leaderList: [],
            state: 4,
            leaderInputValue: '',
            stateList: [
                { value: 4, text: "全部" },
                { value: 0, text: "审核中" },
                { value: 1, text: "已通过" },
                { value: 2, text: "已拒绝" },
                { value: 3, text: "已取消" },
            ],
            leaveType: 1,
            leaveTypeList: [
                { value: 1, text: '事假' },
                // { value: 2, text: '病假' },
                { value: 3, text: '孕假' },
                { value: 4, text: '年假' },
                { value: 5, text: '丧假' },
                { value: 6, text: '陪产假' },
                { value: 7, text: '调休' },
            ],
            selectedStatus: '',
            applications: [],
            formData: {
                name: req.getStorage('userInfo').staffName,
                divName: req.getStorage('userInfo').divName,
                date: [],
                reason: '',
                leader: '',
                attachmentList: []
            },
            pageNum: 1,
            total: 0,
            timer: null,
            attachmentList: [],
            is_headquarters: '',   // 是否总部人
            stepList: [],
            value: 0,
            range: [],
            outType: 0,  // 外出类型：0-外出，1-巡店
            outTypeList: [
                { value: 0, text: '外出' },
                // { value: 1, text: '巡店' }
            ],
            tempStoreId: '',  // 临时存储选择的门店ID
            selectedStores: [], // 已选择的门店列表
            storeList: [], // 所有门店列表
            outStore: ""
        }
    },
    computed: {
        availableStores() {
            return this.storeList.filter(store =>
                !this.selectedStores.some(selected => selected.value === store.value)
            );
        }
    },
    onLoad() {
        this.getData();
        this.getBusiness();
    },
    onReachBottom() {
        this.pageNum++
        this.getData()
    },
    methods: {
        open(item) {
            req.showLoading();
            req.getRequest('/shopApi/translate/getDetails', {
                id: item.id
            }, res => {
                let data = [...res.data];
                let list = data.filter(item => item.processDefinitionId.includes('employeeGoOut'));
                let taskId = list.length > 0 ? list[0].taskId : ""
                req.getRequest(`/shopApi/translate/history/${taskId}`, {}, res => {
                    console.log(res);
                    this.stepList = res;
                    uni.hideLoading();
                    this.$refs.approve.open();
                })
            })
        },
        getBusiness() {
            req.getRequest('/shopApi/goOut/business', {
                name: req.getStorage('userInfo').staffName
            }, res => {
                this.is_headquarters = res.data;
                this.getReviewer();
            });
        },
        getReviewer() {
            req.getRequest('/shopApi/translate/getReviewer', {
                uid: req.getStorage('userInfo').introductionId,
                node: 'addleave',
                isHeadquarters: this.is_headquarters
            }, res => {
                if (res.data.length != 0) {
                    this.range = res.data.map(item => ({
                        value: item.id,
                        text: item.name
                    }))
                }
            });
        },
        // 上传图片
        uploadFile() {
            uni.chooseImage({
                success: (res) => {
                    req.showLoading('上传中');
                    const tempFilePaths = res.tempFilePaths;
                    console.log(tempFilePaths);
                    this.attachmentList = [...this.attachmentList, ...tempFilePaths];
                    this.attachmentList.forEach((item, index) => {
                        req.uploadImg('/shopApi/ask/uploads', item, res => {
                            // console.log(res);
                            this.attachmentList[index] = res[0];
                            if (this.attachmentList.length === index + 1) {
                                uni.hideLoading();
                                console.log(this.attachmentList);
                            };
                        });
                    });
                },
                fail(err) {
                    console.log('选择文件失败', err);
                }
            });
        },
        //预览
        chooseImg(item) {
            console.log('ItmImg', item);
            let src = []
            src.push(item)
            uni.previewImage({
                current: 1,
                // 当前显示图片的http链接  
                urls: src
            });
        },
        // 图片右上角删除角标事件
        del_img(index) {
            this.attachmentList.splice(index, 1);
        },
        leaderChange(e) {
            this.formData.leader = e ? this.range.find(item => item.value === e).text : "";
            // const value = e.detail.value.trim();
            // this.leaderInputValue = value;
            // this.formData.leader = ""
            // if (this.timer) {
            //     clearTimeout(this.timer);
            //     this.timer = null;
            // }

            // if (!value) {
            //     this.leaderList = [];
            //     return;
            // }
            // this.timer = setTimeout(() => {
            //     let params = {
            //         name: value
            //     };
            //     let url = this.is_headquarters == 1 ? "/shopApi/replacement/getStaff" : "/shopApi/goOut/getManager";
            //     if (this.is_headquarters != 1) {
            //         params.uid = req.getStorage('userInfo').introductionId;
            //     }
            //     req.getRequest(url, params, res => {
            //         this.leaderList = res.data.items;
            //     });
            // }, 500);
        },
        // selectLeader(item) {
        //     this.formData.leader = item.name;
        //     this.leaderInputValue = item.name;
        //     this.leaderList = [];
        // },
        getData() {
            req.showLoading();
            req.getRequest("/shopApi/goOut/list", {
                applyUser: req.getStorage('userInfo').staffName,
                pageNum: this.pageNum,
                pageSize: 10,
                state: this.state == 4 ? null : this.state,
                startTime: this.date[0],
                endTime: this.date[1],
                staffId: req.getStorage('userInfo').introductionId,
                outType: 0
            }, res => {
                uni.hideLoading();
                this.total = res.total
                if (this.pageNum === 1) {
                    this.applications = res.data.items
                } else {
                    this.applications = [...this.applications, ...res.data.items]
                }
            })
        },
        stateChange(e) {
            this.state = e
            this.pageNum = 1
            this.getData()
        },
        dateChange(e) {
            this.date = e
            this.pageNum = 1
            this.getData()
        },
        onDateChange(e) {
            // 处理日期选择
        },
        calculateHours() {
            if (this.formData.date && this.formData.date.length === 2) {
                const start = new Date(this.formData.date[0]);
                const end = new Date(this.formData.date[1]);
                this.formData.hours = ((end - start) / 3600000).toFixed(1);
            }
        },
        // 获取门店列表
        getStoreList() {
            req.getRequest('/shopApi/home/<USER>', {
                pageNum: 1,
                pageSize: 100,
            }, res => {
                this.storeList = res.data.list.map(item => ({
                    value: item.id,
                    text: item.storeName
                }));
            });
        },
        // 外出类型改变
        outTypeChange(e) {
            this.outType = e;
            if (e === 1) {
                this.getStoreList();
            }
            // 重置门店选择
            this.tempStoreId = '';
            this.selectedStores = [];
        },
        // 门店选择改变
        storeChange(e) {
            if (!e) return;
            const selectedStore = this.storeList.find(item => item.value === e);
            if (selectedStore && !this.selectedStores.some(store => store.value === selectedStore.value)) {
                this.selectedStores.push(selectedStore);
                this.tempStoreId = ''; // 清空选择
                this.outStore = this.selectedStores.map(store => store.text).join('、');
            }
        },
        // 移除已选择的门店
        removeStore(index) {
            this.selectedStores.splice(index, 1);
            this.outStore = this.selectedStores.map(store => store.text).join('、');
        },
        saveForm() {
            if (!this.formData.name) {
                req.msg('请输入申请人')
                return;
            }
            if (this.outType != 0 && this.outType != 1) {
                req.msg('请选择外出类型')
                return;
            }
            if (this.outType == 1 && this.selectedStores.length === 0) {
                req.msg('请选择门店')
                return;
            }
            if (this.formData.date.length != 2) {
                req.msg('请选择外出时间')
                return;
            }
            if (!this.formData.reason) {
                req.msg('请输入外出事由')
                return;
            }
            if (!this.formData.leader) {
                req.msg('请选择部门领导')
                return;
            }
            if (this.attachmentList.length === 0) {
                req.msg('请上传图片')
                return;
            }

            // TODO: 调用接口保存数据
            req.showLoading()
            req.postRequest("/shopApi/goOut/add", {
                applyUser: req.getStorage('userInfo').staffName,
                originalPostId: req.getStorage('userInfo').divId,
                promoteId: req.getStorage('userInfo').postId,
                deptleader: this.formData.leader,
                startTime: this.formData.date[0],
                endTime: this.formData.date[1],
                reason: this.formData.reason,
                photo: JSON.stringify(this.attachmentList),
                outType: this.outType,
                outStore: this.outStore,
                // storeIds: this.selectedStores.map(store => store.value).join(',')
            }, res => {
                uni.hideLoading()
                req.msg('提交成功')
                this.pageNum = 1
                this.getData()
                this.$refs.popup.close();
                this.formData = {
                    name: req.getStorage('userInfo').staffName,
                    divName: req.getStorage('userInfo').divName,
                    date: [],
                    reason: '',
                    leader: '',
                };
                this.attachmentList = [];
                this.outType = 0;
                this.selectedStores = [];
                this.outStore = '';
            })
        },
        goUrl(url) {
            uni.navigateTo({
                url: url
            })
        }
    }
}
</script>
<style lang="scss" scoped src="./outwardApply.scss"></style>
