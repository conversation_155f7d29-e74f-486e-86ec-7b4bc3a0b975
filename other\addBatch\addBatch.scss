.add-batch {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 150rpx;
  
  .section {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 20rpx;
    
    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;
      
      .section-title {
        font-size: 30rpx;
        font-weight: bold;
        position: relative;
        padding-left: 20rpx;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 8rpx;
          height: 30rpx;
          background-color: #ff3333;
          border-radius: 4rpx;
        }
      }
    }
    
    .form-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .label {
        width: 150rpx;
        font-size: 28rpx;
        color: #333;
      }
      
      .input {
        flex: 1;
        height: 70rpx;
        font-size: 28rpx;
        color: #333;
      }
      
      .uni-date {
        flex: 1;
        
        .uni-date-editor--x {
          width: 100%;
          height: 70rpx;
          
          .uni-date-editor--x__input {
            height: 70rpx;
          }
        }
      }
    }
  }
  
  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    
    .action-btn {
      flex: 1;
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      font-size: 32rpx;
      
      &.cancel {
        background-color: #fff;
        color: #333;
      }
      
      &.save {
        background-color: #ff3333;
        color: #fff;
      }
    }
  }
} 