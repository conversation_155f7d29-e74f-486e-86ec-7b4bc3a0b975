<template>
    <view class="card-replace">
        <!-- tab切换 -->
        <view class="tabs-box">
            <scroll-view scroll-x class="scroll-view" :scroll-into-view="scrollInto">
                <view class="tabs">
                    <view class="tab-item" :class="{ active: currentTab === 0 }" @click="switchTab(0)">
                        待办列表
                    </view>
                    <view class="tab-item" :class="{ active: currentTab === 1 }" @click="switchTab(1)">
                        办理记录
                    </view>
                </view>
            </scroll-view>
        </view>
        <!-- 顶部日期选择和筛选 -->
        <view class="header">
            <uni-datetime-picker v-model="date" type="daterange" @change="dateChange" />
            <view class="batch-approval" @click="showBatchApproval = !showBatchApproval" v-if="currentTab == 0">{{
                showBatchApproval ? '取消' : '批量审批' }}</view>
        </view>
        <!-- 申请列表 -->
        <view class="application-list">
            <view class="application-item" v-for="(item, index) in applications" :key="index"
                @click="goUrl(`/staff/approvalProgress/approvalProgress?item=${JSON.stringify(item)}`)">
                <view class="checkbox-box" @click.stop="checktask(item.taskId)"
                    v-if="showBatchApproval && currentTab == 0">
                    <uni-icons :type="checkTaskList.includes(item.taskId) ? 'checkbox-filled' : 'circle'" size="26"
                        :color="checkTaskList.includes(item.taskId) ? '#ea1306' : '#999'"
                        style="margin-right: 20rpx;"></uni-icons>
                </view>
                <view style="width: 100%;">
                    <view class="info-row">
                        <text class="iconfont" style="font-size:28rpx;color:red;margin-right:5rpx;">&#xe683;</text>
                        <text class="label">待办编号：</text>
                        <text class="value">{{ item.processInstanceId }}</text>
                    </view>
                    <view class="info-row">
                        <text class="iconfont" style="font-size:28rpx;color:red;margin-right:5rpx;">&#xe77a;</text>
                        <text class="label">任务名称：</text>
                        <text class="value">{{ item.taskName }}</text>
                    </view>
                    <view class="info-row">
                        <text class="iconfont" style="font-size:28rpx;color:red;margin-right:5rpx;">&#xe67f;</text>
                        <text class="label">流程名称：</text>
                        <text class="value">{{ item.processName }}</text>
                    </view>
                    <view style="display: flex;">
                        <view class="info-row" style="width: 50%;">
                            <text class="iconfont" style="font-size:28rpx;color:red;margin-right:5rpx;">&#xe689;</text>
                            <text class="label">发起人：</text>
                            <text class="value">{{ item.starter }}</text>
                        </view>
                        <view class="info-row" style="width: 50%;">
                            <text class="iconfont" style="font-size:28rpx;color:red;margin-right:5rpx;">&#xe810;</text>
                            <text class="label">办理人：</text>
                            <text class="value">{{ item.assignee }}</text>
                        </view>
                    </view>
                    <view v-if="item.createTime" class="info-row">
                        <text class="iconfont" style="font-size:28rpx;color:red;margin-right:5rpx;">&#xe77c;</text>
                        <text class="label">创建时间：</text>
                        <text class="value">{{ item.createTime }}</text>
                    </view>
                    <view class="info-row">
                        <text class="iconfont" style="font-size:28rpx;color:red;margin-right:5rpx;">&#xe676;</text>
                        <text class="label">审批人：</text>
                        <text class="value">{{ item.assignee }}</text>
                    </view>
                    <view class="info-row" v-if="currentTab == 1 && !item.processEndTime">
                        <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                        <text class="label">审批进度：</text>
                        <view @click="open(item)"
                            style="background-color: #ea1306;color: #fff;padding: 10rpx 20rpx;border-radius: 12rpx;">
                            查看审批进度</view>
                    </view>
                </view>
            </view>
            <view v-if="applications.length === 0" style="text-align: center;color: #999;">
                <image mode=""
                    src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png"
                    style="width: 372rpx;height: 312rpx;margin-top: 50rpx;">
                </image>
                <view style="margin-top: 10rpx;font-size: 28rpx;">暂无数据</view>
            </view>
        </view>
        <view class="batch-approval-box" v-if="showBatchApproval && checkTaskList.length > 0" @click="batchApproval">
            确认审批
        </view>
        <uni-popup :is-mask-click="false" ref="approve" type="bottom" border-radius="10px 10px 0 0">
            <view class="popup-content1">
                <view class="popup-header">
                    <text>审批进度</text>
                    <text class="close-icon" @click="$refs.approve.close()">×</text>
                </view>
                <view class="popup-body">
                    <view class="step-container">
                        <view class="step" v-for="(item, index) in stepList" :key="index">
                            <view class="step-number"></view>
                            <view class="step-title">
                                <view>开始时间:{{ item.startTime }}</view>
                                <view>{{ item.assignee }}：{{ item.taskName }}</view>
                                <view v-if="item.comment">审批意见：{{ item.comment }}</view>
                                <view v-if="item.endTime">结束时间:{{ item.endTime }}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const req = require("../../utils/request");

export default {
    data() {
        return {
            date: [],
            currentTab: 0,
            scrollInto: '',
            applications: [],
            pageNum: 1,
            total: 0,
            stepList: [],
            checkTaskList: [],
            showBatchApproval: false,
        }
    },
    onShow() {
        this.pageNum = 1;
        this.getData()
    },
    onReachBottom() {
        this.pageNum++
        this.getData()
    },
    methods: {
        // 批量审批
        batchApproval() {
            console.log(this.checkTaskList);
            req.postRequest('/shopApi/translate/completeTasks', this.checkTaskList, res => {
                console.log(res);
                this.checkTaskList = [];
                this.showBatchApproval = false;
                this.pageNum = 1;
                this.getData();
                uni.showToast({
                    title: '审批成功',
                    icon: 'success'
                });
            }, undefined, undefined, true);
        },
        // 多选
        checktask(taskId) {
            if (this.checkTaskList.includes(taskId)) {
                this.checkTaskList = this.checkTaskList.filter(item => item !== taskId)
            } else {
                this.checkTaskList.push(taskId)
            }
        },
        open(item) {
            req.showLoading();
            req.getRequest('/shopApi/translate/getDetails', {
                id: item.businessKey
            }, res => {
                let data = [...res.data];
                let list = data.filter(item2 => item2.processDefinitionId.includes(item.processDefinitionId.split(':')[0]));
                let taskId = list.length > 0 ? list[0].taskId : ""
                req.getRequest(`/shopApi/translate/history/${taskId}`, {}, res => {
                    console.log(res);
                    this.stepList = res;
                    uni.hideLoading();
                    this.$refs.approve.open();
                })
            })
        },
        switchTab(index) {
            if (this.currentTab === index) return;
            this.currentTab = index;
            this.pageNum = 1;
            this.applications = [];
            this.getData();
        },
        getData() {
            req.showLoading();
            const url = this.currentTab === 0 ? "/shopApi/translate/mylist" : "/shopApi/translate/getHistory";
            req.postRequest(url, {
                id: req.getStorage("userInfo").introductionId,
                pageNum: this.pageNum,
                pageSize: 10,
                startTime: this.date[0],
                endTime: this.date[1],
            }, res => {
                uni.hideLoading();
                this.total = res.total
                if (this.pageNum === 1) {
                    this.applications = res.rows
                } else {
                    this.applications = [...this.applications, ...res.rows]
                }
            })
        },
        goUrl(url) {
            if (this.currentTab === 0) {
                uni.navigateTo({
                    url: url
                })
            }
        },
        stateChange(e) {
            this.state = e
            this.pageNum = 1
            this.getData()
        },
        dateChange(e) {
            this.date = e
            this.pageNum = 1
            this.getData()
        },
    }
}
</script>
<style lang="scss" scoped src="./toDoList.scss"></style>
