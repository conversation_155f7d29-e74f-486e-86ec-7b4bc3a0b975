.seckill-page {
  min-height: 100vh;
  background: #f6f7fb;
  padding-bottom: env(safe-area-inset-bottom);
  .sort {
    position: absolute;
    top: 0;
    padding: 0 40rpx;
    margin-top: 20rpx;
    width: 100%;
    .sort_item {
      background: #edf3fa;
      padding: 10rpx 20rpx;
      border-radius: 40rpx;
      position: relative;
      z-index: 1;
      display: inline-block;
      font-size: 25rpx;
      color: #4e5969;
      margin-right: 10rpx;
      margin-bottom: 10rpx;
    }
    .sort_img {
      width: 24rpx;
      height: 32rpx;
      margin-right: 6rpx;
    }
    .active_sort_item {
      background: #f9edef;
      color: #ea1306;
    }
  }
  .scroll-view {
    width: 100%;
    white-space: nowrap;
    margin-top: 20rpx;
    padding-bottom: 20rpx;
    // 隐藏滚动条
    ::-webkit-scrollbar {
      display: none;
      width: 0;
      height: 0;
      color: transparent;
    }

    // 兼容不同平台
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    .scroll-content {
      display: inline-flex;
      padding-right: 20rpx;
    }
  }
  .product-list {
    position: absolute;
    top: 50rpx;
    padding: 60rpx 30rpx 30rpx;

    .product-item {
      margin-bottom: 30rpx;
    }

    .product-card {
      background: #fff;
      border-radius: 24rpx;
      overflow: hidden;
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
      transition: all 0.3s;
      display: flex;
      padding: 24rpx;

      &:active {
        transform: scale(0.98);
      }
      .image-wrapper {
        position: relative;
        .yibao-tongchou {
          position: absolute;
          top: 0;
          left: 0;
          background: #ea1306;
          padding: 4rpx 12rpx;
          border-radius: 20rpx 0 20rpx 0;
          color: #fff;
          font-size: 24rpx;
        }
        .product-image {
          width: 240rpx;
          height: 240rpx;
          border-radius: 20rpx;
          flex-shrink: 0;
          border: 4rpx solid #ea1306;
        }
        .date-wrapper {
          width: 100%;
          height: 60rpx;
          background: #ea1306;
          color: #fff;
          position: absolute;
          bottom: 0;
          left: 0;
          border-radius: 0 0 20rpx 20rpx;
          display: flex;
          align-items: center;
          justify-content: space-around;
        }
      }
      .product-info {
        flex: 1;
        padding-left: 24rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .title {
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
          line-height: 1.4;
          margin-bottom: 16rpx;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }

        .progress-wrapper {
          margin-bottom: 20rpx;

          .progress-bar {
            height: 16rpx;
            background: #f5f5f5;
            border-radius: 8rpx;
            position: relative;
            margin-bottom: 12rpx;
            overflow: hidden;

            .progress {
              position: absolute;
              left: 0;
              top: 0;
              height: 100%;
              background: linear-gradient(90deg, #ffb800 0%, #ff8a00 100%);
              border-radius: 8rpx;
              transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            }
          }

          .progress-text {
            font-size: 24rpx;
            color: #999;
          }
        }

        .price-row {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-around;
          margin-top: 10px;
          background: url("../../static/buyButton.png");
          background-size: 100% 100%;

          .price {
            display: flex;
            align-items: baseline;
            color: #ffffff;

            .symbol {
              font-size: 26rpx;
              margin-right: 2rpx;
            }

            .number {
              font-size: 44rpx;
              font-weight: bold;
              font-family: DIN;
            }

            .original {
              position: absolute;
              top: -10px;
              left: 0;
              width: 35px;
              height: 14px;
              font-size: 24rpx;
              color: #3d3d3d;
              text-decoration: line-through;
              text-align: center;
              margin-left: 16rpx;
              background: url("../../static/originalPrice.png");
              background-size: 100% 100%;
            }
          }
          .price_button {
            color: #ea1306;
          }
        }

        .stock-info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin: 12rpx 0;

          .stock-item {
            display: flex;
            flex-direction: column;
            gap: 6rpx;

            .stock {
              border: 1px solid #ea1306;
              border-radius: 4px;
              padding: 2px;
              font-size: 22rpx;
              color: #ea1306;
            }
          }

          .limit {
            font-size: 24rpx;
            padding: 4rpx 12rpx;
            border: 1px solid #ea1306;
            color: #ea1306;
            border-radius: 4rpx;
          }
        }
      }
    }
  }

  .empty-tip {
    padding: 120rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #999;
    font-size: 28rpx;

    image {
      width: 240rpx;
      height: 240rpx;
      margin-bottom: 30rpx;
      opacity: 0.8;
    }
  }

  .category-title {
    width: 100%;
    height: 100rpx;
    position: sticky;
    top: 0;
    .scroll-view {
      width: 100%;
      height: 100%;
      white-space: nowrap;
    }

    .category-list {
      padding: 20rpx;
      display: flex;
      align-items: center;
      height: 100%;

      .category-item {
        padding: 8rpx 24rpx;
        font-size: 26rpx;
        color: #666;
        position: relative;
        margin-right: 20rpx;
        background: #f5f5f5;
        border-radius: 32rpx;
        transition: all 0.3s ease;

        &.active {
          color: #fff;
          font-weight: 500;
          background: #ea1306;
          box-shadow: 0 4rpx 8rpx rgba(234, 19, 6, 0.2);

          &::after {
            display: none;
          }
        }

        &:last-child {
          margin-right: 20rpx;
        }
      }
    }
  }
}
