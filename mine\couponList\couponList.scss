.coupon-list {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: calc(188rpx + env(safe-area-inset-bottom));

  .tab-bar {
    display: flex;
    background: #fff;
    padding: 20rpx 0;
    position: sticky;
    top: 0;
    z-index: 1;

    .tab-item {
      flex: 1;
      text-align: center;
      font-size: 28rpx;
      color: #333;
      position: relative;

      &.active {
        color: #ea1306;

        &::after {
          content: "";
          position: absolute;
          bottom: -20rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          background: #ea1306;
          border-radius: 2rpx;
        }
      }
    }
  }

  .coupon-container {
    padding: 20rpx;

    .coupon-item {
      background: #fff;
      border-radius: 12rpx;
      margin-bottom: 20rpx;
      overflow: hidden;

      .coupon-main {
        display: flex;
        position: relative;

        .left {
          width: 220rpx;
          background: #ea1306;
          color: #fff;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 30rpx 0;

          .price {
            .symbol {
              font-size: 32rpx;
            }

            .num {
              font-size: 60rpx;
              font-weight: bold;
            }
          }

          .condition {
            font-size: 24rpx;
            margin-top: 10rpx;
          }
        }

        .right {
          flex: 1;
          padding: 20rpx 30rpx;
          position: relative;

          .title {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
          }

          .time {
            font-size: 24rpx;
            color: #999;
            margin: 10rpx 0;
          }

          .desc {
            font-size: 24rpx;
            color: #666;
          }

          .btn {
            position: absolute;
            right: 30rpx;
            bottom: 20rpx;
            padding: 8rpx 24rpx;
            border-radius: 30rpx;
            font-size: 24rpx;
            background: #ea1306;
            color: #fff;

            &.used {
              background: #999;
            }

            &.expired {
              background: #ccc;
            }
          }

          .arrow {
            position: absolute;
            right: 30rpx;
            top: 20rpx;
            transition: transform 0.3s;
            color: #999;
            font-size: 24rpx;

            &.expanded {
              transform: rotate(180deg);
            }
          }
        }
      }

      .coupon-detail {
        height: 0;
        overflow: hidden;
        background: #fff;
        border-top: 2rpx solid #eee;

        .detail-content {
          padding: 20rpx 30rpx;
          opacity: 0;
          transform: translateY(-20rpx);
          transition: all 0.3s ease-out;
        }

        &.expanded {
          height: 180rpx;
          transition: height 0.3s ease-out;

          .detail-content {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .detail-item {
          display: flex;
          font-size: 24rpx;
          margin-bottom: 16rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            width: 140rpx;
            color: #666;
            flex-shrink: 0;
          }

          .content {
            flex: 1;
            color: #333;
          }
        }
      }
    }

    .selected-icon {
      font-size: 40rpx;
      color: #8F8F8F;
    }

    .selected {
      color: #ea1306;
    }

  }

  .add-coupon {
    position: fixed;
    bottom: 0;
    width: 100%;
    background: #fff;
    padding: 30rpx;
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
    display: flex;
    // justify-content: center;
    flex-direction: column;
    align-items: center;
    border-top: 1rpx solid #f5f5f5;
    box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;

    .add-coupon-btn {
      width: 560rpx;
      height: 80rpx;
      background: #ea1306;
      color: #fff;
      border-radius: 40rpx;
      font-size: 28rpx;
      text-align: center;
      line-height: 80rpx;

    }
  }

  .single-line-text {
    white-space: nowrap;
    /* 防止换行 */
    overflow: hidden;
    /* 隐藏超出部分 */
    text-overflow: ellipsis;
    /* 使用省略号表示超出部分 */
    width: 400rpx;
    /* 设置宽度，可以根据需求调整 */
  }

  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 200rpx;

    image {
      width: 200rpx;
      height: 200rpx;
    }

    text {
      color: #999;
      font-size: 28rpx;
      margin-top: 20rpx;
    }
  }


}