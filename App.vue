<script>
const req = require("./utils/request");
export default {
	onLaunch: function () {
		if (req.getStorage("userInfo")) {
			this.globalData.updateCartBadge()
		}
	},
	onShow(options) {
		console.log(1111, options)
		if (options.scene == 1038) {
			this.globalData.authCode = options.referrerInfo.extraData.authCode;
		}
	},
	onHide: function () {
	},
	globalData: {
		// 更新购物车角标
		updateCartBadge() {
			// 获取当前页面路径
			const pages = getCurrentPages();
			const currentPage = pages[pages.length - 1];
			// TabBar页面路径列表
			const tabBarPages = [
				'pages/index/index',
				'pages/classify/classify',
				'pages/cart/cart',
				'pages/user/user'
			];

			// 只在TabBar页面设置角标
			if (currentPage && tabBarPages.includes(currentPage.route)) {
				req.getRequest("/shopApi/purchase/purchaseNum", {}, res => {
					if (res.data > 0) {
						uni.setTabBarBadge({
							index: 2,
							text: res.data.toString()
						})
					} else {
						uni.removeTabBarBadge({
							index: 2
						})
					}
				})
			}
			return
		},
		getSessionKey(success) {
			uni.login({
				success(res) {
					req.getRequest('/shopApi/weixin/getConversation/' + res.code, {
					}, data => {
						this.SESSION_KEY = data.data.session_key;
						req.setStorage('SESSION_KEY', data.data.session_key);
						success.call(this, data.data);
					});
				}
			});
		},

		/**
		 * 打开新页面
		 */
		openPage(e) {
			let url
			if (e.indexOf("plugin-private:") != -1) {
				url = `${e}`
			} else {
				url = `/${e}`
			}
			uni.navigateTo({
				// url: `/${e}` 
				url: url
			});
			// uni.navigateTo({
			//   url: `/${e}`
			// });
		},
	},
}
</script>

<style>
/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
@font-face {
  font-family: 'iconfont';  /* Project id 4744557 */
  src: url('//at.alicdn.com/t/c/font_4744557_yrpjx40hv7s.woff2?t=1751960224494') format('woff2'),
       url('//at.alicdn.com/t/c/font_4744557_yrpjx40hv7s.woff?t=1751960224494') format('woff'),
       url('//at.alicdn.com/t/c/font_4744557_yrpjx40hv7s.ttf?t=1751960224494') format('truetype');
}

.iconfont {
	font-family: "iconfont" !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.mohu {
	filter: blur(5px);
}

.mask {
	position: fixed;
	z-index: 99;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, .5);
}
</style>
