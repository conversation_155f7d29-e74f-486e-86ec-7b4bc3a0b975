.search {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  border-radius: 40rpx;
  width: 100%;
  height: 70rpx;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  font-size: 25rpx;
  padding: 0 20rpx;
  color: #bfbfbf;
  border: 1px solid #EA1306;
}

.list-page {
  min-height: 100vh;
  background: #f7f8fa;
}

.list-page .filter-bar {
  position: sticky;
  top: 0;
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #fff;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.list-page .filter-bar .filter-item {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
}

.list-page .filter-bar .filter-item.active {
  color: #ff4444;
  font-weight: 500;
}

.list-page .filter-bar .filter-item.price .iconfont {
  margin-left: 6rpx;
  font-size: 24rpx;
}

.list-page .filter-bar .filter-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 2rpx;
  height: 24rpx;
  background-color: #eee;
}

.list-page .recommend {
  padding: 20rpx 24rpx;
}

.list-page .recommend :deep(.waterfall-item) {
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.list-page .recommend :deep(.waterfall-item):active {
  transform: scale(0.98);
  opacity: 0.8;
}

.list-page .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.list-page .empty-state::before {
  content: "";
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 20rpx;
  opacity: 0.8;
}

.list-page .empty-state text {
  font-size: 28rpx;
  color: #999;
  letter-spacing: 2rpx;
}

.list-page .loading-text {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 30rpx 0;
  letter-spacing: 2rpx;
}

.list-page .no-more {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 30rpx 0;
}

.list-page .no-more::before, .list-page .no-more::after {
  content: "";
  display: inline-block;
  width: 100rpx;
  height: 1rpx;
  background: #eee;
  margin: 0 20rpx;
  vertical-align: middle;
}
