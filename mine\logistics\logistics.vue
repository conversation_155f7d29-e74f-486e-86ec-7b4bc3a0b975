<template>
    <view class="logistics">
        <!-- 物流信息头部 -->
        <view class="logistics-header">
            <view class="delivery-info">
                <view class="status">{{ logisticsInfo[0].shipperCode || '暂无物流信息' }}</view>
                <view class="company" v-if="logisticsInfo[0].logisticCode">物流单号：{{ logisticsInfo[0].logisticCode }}
                </view>
            </view>
            <view class="copy-btn" @click="copyTrackingNumber">复制单号</view>
        </view>

        <!-- 物流轨迹列表 -->
        <view class="logistics-list">
            <view class="timeline-item" v-for="(item, index) in logisticsInfo" :key="index"
                :class="{ 'active': index === 0 }">
                <view class="time-point"></view>
                <view class="time-line" v-if="index !== logisticsInfo.length - 1"></view>
                <view class="content">
                    <view class="desc">{{ item.nowValue }}</view>
                    <view class="time">{{ item.pushTime }}</view>
                </view>
            </view>
        </view>

        <!-- 暂无物流信息 -->
        <view class="empty" v-if="!logisticsInfo.length">
            <view>暂无物流信息</view>
        </view>
    </view>
</template>

<script>
const req = require('@/utils/request.js')

export default {
    data() {
        return {
            orderId: '', // 订单ID
            logisticsInfo: [], // 物流轨迹信息
        }
    },
    onLoad(options) {
        if (options.orderId) {
            this.orderId = options.orderId
            this.getLogisticsInfo()
        }
    },
    methods: {
        // 获取物流轨迹信息
        getLogisticsInfo() {
            req.getRequest('/shopApi/mp/order/trackInfo', {
                orderId: this.orderId
            }, res => {
                this.logisticsInfo = res.data
            })
        },
        // 复制物流单号
        copyTrackingNumber() {
            if (!this.logisticsInfo[0].logisticCode) {
                req.msg('暂无物流单号')
                return
            }
            uni.setClipboardData({
                data: this.logisticsInfo[0].logisticCode,
                success: () => {
                    req.msg('复制成功')
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.logistics {
    min-height: 100vh;
    background: #f8f8f8;
    padding: 20rpx;

    .logistics-header {
        background: #fff;
        border-radius: 12rpx;
        padding: 30rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .delivery-info {
            .status {
                font-size: 32rpx;
                font-weight: bold;
                color: #333;
                margin-bottom: 10rpx;
            }

            .company {
                font-size: 26rpx;
                color: #666;
            }
        }

        .copy-btn {
            padding: 10rpx 30rpx;
            background: #f5f5f5;
            border-radius: 30rpx;
            font-size: 26rpx;
            color: #666;
        }
    }

    .logistics-list {
        background: #fff;
        border-radius: 12rpx;
        padding: 30rpx;

        .timeline-item {
            position: relative;
            padding-left: 30rpx;
            margin-bottom: 40rpx;

            &:last-child {
                margin-bottom: 0;
            }

            .time-point {
                position: absolute;
                left: 0;
                top: 10rpx;
                width: 16rpx;
                height: 16rpx;
                background: #ddd;
                border-radius: 50%;
            }

            .time-line {
                position: absolute;
                left: 7rpx;
                top: 26rpx;
                width: 2rpx;
                height: calc(100% + 14rpx);
                background: #eee;
            }

            .content {
                .desc {
                    font-size: 28rpx;
                    color: #333;
                    line-height: 1.4;
                    margin-bottom: 10rpx;
                }

                .time {
                    font-size: 24rpx;
                    color: #999;
                }
            }

            &.active {
                .time-point {
                    background: #EA1306;
                    width: 20rpx;
                    height: 20rpx;
                    left: -2rpx;
                    border: 4rpx solid rgba(234, 19, 6, 0.2);
                    box-sizing: border-box;
                }

                .desc {
                    color: #EA1306;
                    font-weight: bold;
                }
            }
        }
    }

    .empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 200rpx;

        image {
            width: 200rpx;
            margin-bottom: 30rpx;
        }

        view {
            font-size: 28rpx;
            color: #999;
        }
    }
}
</style>