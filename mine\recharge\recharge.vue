<template>
    <view class="recharge-container">
        <!-- 余额信息 -->
        <view class="balance-card">
            <view class="record-link" @click="goToRecord">
                明细记录
                <text class="iconfont">&#xe6a3;</text>
            </view>
            <view class="title">当前余额</view>
            <view class="amount">
                <text class="symbol">¥</text>
                <text class="number">{{ balance }}</text>
            </view>
        </view>

        <!-- 充值金额选择 -->
        <view class="amount-section">
            <view class="section-title">选择充值金额</view>
            <view class="amount-grid">
                <view v-for="(item, index) in rechargeOptions" :key="index" class="amount-item"
                    :class="{ active: selectedIndex === index }" @click="selectAmount(index)">
                    <view class="money">¥{{ item.amount }}</view>
                    <view class="gift" v-if="item.gift">送{{ item.gift }}元</view>
                </view>
            </view>
        </view>

        <!-- 自定义金额 -->
        <view class="custom-amount">
            <view class="section-title">自定义金额</view>
            <view class="input-wrap">
                <text class="symbol">¥</text>
                <input v-model="customAmount" type="digit" placeholder="请输入充值金额" @input="handleCustomInput" />
            </view>
        </view>

        <!-- 充值按钮 -->
        <view class="bottom-button">
            <button class="recharge-btn" @click="handleRecharge">
                立即充值
            </button>
        </view>
    </view>
</template>

<script>
const req = require("../../utils/request")

export default {
    data() {
        return {
            balance: 0,
            selectedIndex: -1,
            customAmount: '',
            rechargeOptions: [
                { amount: 50 },
                { amount: 100 },
                { amount: 200 },
                { amount: 500 },
                { amount: 1000 },
                { amount: 2000 }
            ]
        }
    },
    computed: {
        canRecharge() {
            return this.selectedIndex >= 0 || (this.customAmount && Number(this.customAmount) <= 0)
        }
    },
    onLoad() {
        this.getBalance()
    },
    methods: {
        // 获取余额
        //钱包
        getBalance() {
            req.getRequest("/shopApi/mp/user/myBalance", {}, res => {
                this.balance = res.data.money
            })
        },

        // 选择固定金额
        selectAmount(index) {
            this.selectedIndex = index
            this.customAmount = ''
        },

        // 处理自定义输入
        handleCustomInput() {
            this.selectedIndex = -1
            // 限制小数点后两位
            if (this.customAmount.includes('.')) {
                const parts = this.customAmount.split('.')
                if (parts[1].length > 2) {
                    this.customAmount = parts[0] + '.' + parts[1].slice(0, 2)
                }
            }
        },

        // 处理充值
        handleRecharge() {
            const amount = this.selectedIndex >= 0
                ? this.rechargeOptions[this.selectedIndex].amount
                : Number(this.customAmount)

            if (amount <= 0) {
                req.msg('充值金额不能小于0元')
                return
            }
            req.showLoading()
            // 调用充值接口
            req.postRequest('/shopApi/user/rechargeBalance', {
                money: amount
            }, res => {
                // 处理支付逻辑
                uni.hideLoading()
                res = JSON.parse(res.data)
                uni.requestPayment({
                    package: res.miniPayRequest.package,
                    appId: res.miniPayRequest.appId,
                    paySign: res.miniPayRequest.paySign,
                    nonceStr: res.miniPayRequest.nonceStr,
                    timeStamp: res.miniPayRequest.timeStamp,
                    signType: res.miniPayRequest.signType,
                    success: () => {
                        req.msg('充值成功')
                        this.getBalance()
                    },
                    fail: () => {
                        req.msg('充值失败')
                    }
                })
            })
        },

        goToRecord() {
            uni.navigateTo({
                url: '/mine/rechargeRecord/rechargeRecord'
            })
        }
    }
}
</script>
<style lang="scss" scoped>
@import './recharge.scss';
</style>