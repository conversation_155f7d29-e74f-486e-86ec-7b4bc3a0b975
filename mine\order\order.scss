.search {
  padding: 0 30rpx;
  background: #fff;

  .is-input-border {
    border-radius: 100rpx !important;
  }
}

.tabs-box {
  width: 100%;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;

  .scroll-view {
    width: 100%;
    white-space: nowrap;

    // 隐藏滚动条
    ::-webkit-scrollbar {
      display: none;
      width: 0;
      height: 0;
      color: transparent;
    }

    .tabs {
      display: flex;
      padding: 0 20rpx;

      .tab-item {
        padding: 20rpx 25rpx;
        position: relative;
        flex-shrink: 0;
        transition: all 0.3s ease;

        &::after {
          content: "";
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-50%);
          width: 0;
          height: 4rpx;
          background-color: #f00;
          border-radius: 2rpx;
          transition: all 0.3s ease;
          opacity: 0;
        }

        &.active {
          color: #f00;
          font-weight: bold;

          &::after {
            width: 40rpx;
            opacity: 1;
          }
        }
      }
    }
  }
}

.order {
  padding: 30rpx;
  font-size: 26rpx;
  // margin-top: 20rpx;
  background: #f9f9f9;

  .order_item {
    padding: 20rpx;
    border-radius: 30rpx;
    background: #fff;
    margin-bottom: 20rpx;

    .order_item_top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #f2f3f5;
      padding-bottom: 20rpx;

      .order_id {
        display: flex;
        align-items: center;
      }
    }

    .order_item_content {
      border-bottom: 1px solid #f2f3f5;
      padding-bottom: 20rpx;
      margin-bottom: 20rpx;

      .logistics {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #edf3fa;
        padding: 20rpx;
        margin: 20rpx auto;
        border-radius: 20rpx;
      }

      .goods {
        display: flex;
        margin-top: 20rpx;

        .goods_info {
          width: 350rpx;
          font-size: 26rpx;
          margin-left: 20rpx;
        }

        .goods_price {
          flex: 1;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-end;
        }
      }

      .total_price {
        color: #4e5969;
        font-size: 30rpx;

        .price {
          color: red;
        }

        .price_item {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          margin: 10rpx 0;
          flex-wrap: wrap;

          .medical_pay,
          .overall_pay {
            margin-left: 20rpx;
          }
        }

        .total {
          display: flex;
          align-items: center;
          justify-content: flex-end;
        }
      }
    }

    .order_item_bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .time {
        color: #999;
        font-size: 24rpx;
      }

      .btn {
        display: flex;
        align-items: center;

        .btn_item {
          padding: 10rpx 18rpx;
          border-radius: 30rpx;
          background-color: #f00;
          margin-left: 10rpx;
          color: #fff;
          font-size: 24rpx;
        }

        .btn_item2 {
          background: #f2f3f5;
          color: #333;
          border: 1px solid #f2f3f5;
        }
      }
    }
  }
}

.swiper {
  height: calc(100vh - 200rpx);
}

.merchant-dialog {
  .dialog-content {
    background-color: #fff;
    height: 70vh;
    border-radius: 20rpx;
    padding: 40rpx;
    box-sizing: border-box;

    .dialog-title {
      text-align: center;
      font-size: 32rpx;
      font-weight: 600;
      line-height: 50rpx;
      margin-bottom: 20rpx;
    }

    .scroll-box {
      height: calc(70vh - 170rpx);

      .merchant-item {
        text-align: center;
        font-size: 30rpx;
        line-height: 56rpx;
      }
    }

    .dialog-cancel {}
  }
}

.popup-content {
  width: 690rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-sizing: border-box;

  .popup-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .delivery-options {
    display: flex;
    margin-bottom: 40rpx;

    .delivery-item {
      flex: 1;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2rpx solid #dcdfe6;
      margin: 0 20rpx;
      border-radius: 10rpx;

      &.active {
        border-color: #f00;

        .radio-wrap {
          .radio-inner {
            background: #f00;
          }
        }
      }

      .radio-wrap {
        width: 32rpx;
        height: 32rpx;
        border: 2rpx solid #dcdfe6;
        border-radius: 50%;
        margin-right: 10rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .radio-inner {
          width: 20rpx;
          height: 20rpx;
          border-radius: 50%;
        }
      }
    }
  }

  .delivery-info {
    .info-item {
      display: flex;
      align-items: center;
      padding: 20rpx;

      .label {
        width: 100%;
        font-size: 28rpx;
        color: #333;
      }
    }

    .area {
      display: flex;
      align-items: center;
      padding: 0 20rpx;

      .dzico {
        width: 32rpx;
        height: 32rpx;
        margin-right: 10rpx;
      }

      .address {
        font-size: 28rpx;
        color: #333;
      }

      .iconfont {
        font-size: 32rpx;
      }
    }
  }

  .popup-buttons {
    display: flex;
    margin-top: 40rpx;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      border-radius: 10rpx;
      font-size: 28rpx;
    }

    .cancel-btn {
      background: #f5f5f5;
      color: #666;
      margin-right: 20rpx;
    }

    .confirm-btn {
      background: #f00;
      color: #fff;
    }
  }
}