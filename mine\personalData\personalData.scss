.personalData {
  padding-top: 20rpx;
  padding-bottom: 50rpx;
  background-color: #f8f8f8;
  .real_item {
    display: flex;
    align-items: center;
    margin-bottom: 40rpx;
    border-bottom: 1rpx solid #f2f3f5;
    color: #3d3d3d;
    font-size: 28rpx;
  }

  .info_title {
    font-family: PingFang SC;
    font-size: 22rpx;
    font-weight: normal;
    color: #3d3d3d;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
  }

  .img_item {
    position: relative;
    width: 192rpx;
    height: 192rpx;
    background: #f6f6f6;
    margin: 0 20rpx 20rpx 0;
  }
}

/deep/.is-input-border {
  border: none;
}

/deep/.is-disabled {
  background-color: #fff !important;
  color: #333333 !important;
}

/deep/.uni-easyinput {
  text-align: right;
}
