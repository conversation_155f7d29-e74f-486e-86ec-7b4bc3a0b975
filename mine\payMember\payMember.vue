<template>
	<view>
		<view class="joinMember">
			<image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20250120/1ba071f388ce47b4b88ea1add7821879.jpg"
				style="width: 750rpx;" mode="widthFix">
			</image>
			<view class="pay_btn" @click="joinMember">
				<image
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20250325/341489b40c8f4f1ea618b00009f0c799.png">
				</image>
			</view>
		</view>
		<!-- 登录弹窗 -->
		<view class="login-modal" v-if="showLoginModal">
			<view class="login-container">
				<view class="login-header">
					<text class="login-title">登录</text>
					<text class="login-close" @click="closeLoginModal">×</text>
				</view>
				<view class="login-content">
					<image class="login-avatar" mode="aspectFit"
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240927/6f40d834eed1445b8a4fe3404a800611.png">
					</image>
					<view class="login-tip">登录后可用更多功能</view>
					<view class="login-btn">
						<button v-if="phone" @click="login" hover-class="none" class="btnphone">快捷登录</button>
						<button v-else @click="login" hover-class="none" class="btnphone" open-type="getPhoneNumber"
							@getphonenumber="getphonenumber">快捷登录</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
const req = require('../../utils/request.js')
const app = getApp()
export default {
	data() {
		return {
			scene: "",
			showLoginModal: false,
			infoParmes: {},
			isPay: false
		}
	},
	onLoad(options) {
		console.log(options);
		if (options.scene) {
			this.scene = options.scene;
		};
		// 获取sessionKey等信息
		app.globalData.getSessionKey(json => {
			this.openid = json.openid
			this.sessionKey = json.session_key
			this.phone = json.phone
			// 获取完sessionKey后检查登录状态并进行相应处理
			this.getCartCode();
		});
	},
	methods: {
		// 解码
		getCartCode() {
			if (!req.getStorage("userInfo")) {
				this.showLoginModal = true
				return
			};
			if (this.scene) {
				// 用户已登录，进行正常初始化
				req.getRequest("/shopApi/goldCard/programCode", {
					codeId: this.scene
				}, res => {
					this.infoParmes = JSON.parse(res.data);
					// console.log(this.infoParmes, "hhhhhhhhhhh");
				});
			}
		},
		joinMember() {
			if (this.isPay) {
				return
			};
			if (!req.getStorage("userInfo")) {
				this.showLoginModal = true
				return
			};
			if (this.infoParmes.tabValue == 1) {
				req.postRequest('/shopApi/goldCard/updateUser', {
					pid: this.infoParmes.pid, // 推荐人 id
					id: req.getStorage("userInfo").id
				}, res => {
					// this.scene = "";
					uni.hideLoading();
					req.msg(res.msg);
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/user/user'
						});
					}, 1500);
				})
			} else {
				this.isPay = true;
				req.postRequest("/shopApi/goldCard/crateOrder", {
					memId: req.getStorage("userInfo").id, // 会员id
					payState: 0, // 0 微信 1 余额 
				}, res1 => {
					req.postRequest('/shopApi/goldCard/wxPay', {
						id: res1.data.id, // 订单 id
						money: res1.data.money // 金额
					}, json => {
						json = JSON.parse(json.data);
						console.log(json, '支付');
						if (json.miniPayRequest) {
							console.log('走银联');
							wx.requestPayment({
								package: json.miniPayRequest.package,
								appId: json.miniPayRequest.appId,
								paySign: json.miniPayRequest.paySign,
								nonceStr: json.miniPayRequest.nonceStr,
								timeStamp: json.miniPayRequest.timeStamp,
								signType: json.miniPayRequest.signType,
								success: () => {
									this.isPay = false;
									req.postRequest('/shopApi/goldCard/updateOrder', {
										id: res1.data.id,
										payNum: json.merOrderId,
										memId: req.getStorage("userInfo").id,
										pid: this.infoParmes.pid
									}, res => {
										// this.scene = "";
										uni.hideLoading();
										req.msg('开通成功');
										setTimeout(() => {
											uni.switchTab({
												url: '/pages/user/user'
											});
										}, 1500);
									})
								},
								fail: () => {
									// that.scene = "";
									this.isPay = false;
									uni.hideLoading();
								}
							})
						} else {
							console.log('走余额');
							uni.requestPayment({
								timeStamp: json.timeStamp,
								nonceStr: json.nonceStr,
								package: json.packages,
								signType: json.signType,
								paySign: json.sign,
								success: () => {
									this.isPay = false;
									console.log('支付成功')
									req.postRequest('/shopApi/goldCard/updateOrder', {
										id: res1.data.id,
										payNum: json.merOrderId,
										memId: req.getStorage("userInfo").id,
										pid: this.infoParmes.pid
									}, res => {
										// this.scene = "";
										uni.hideLoading();
										req.msg('开通成功');
										setTimeout(() => {
											uni.switchTab({
												url: '/pages/user/user'
											});
										}, 1500);
									})
								},
								fail: () => {
									// this.scene = "";
									this.isPay = false;
									uni.hideLoading();
								}
							})
						}
					})
				});
			}
		},
		login() {
			uni.showLoading({
				title: '登录中...'
			});
			uni.getUserProfile({
				desc: "注册",
				success: res => {
					this.encryptedData = res.encryptedData
					this.iv = res.iv
					if (this.phone) {
						req.postRequest("/shopApi/weixin/login", {
							openid: this.openid,
							encryptedData: this.encryptedData,
							iv: this.iv,
							sessionKey: this.sessionKey
						}, res => {
							uni.hideLoading();
							req.setStorage("AUTH_TOKEN", res.data.accessToken);
							req.setStorage("userInfo", res.data);
							this.showLoginModal = false;
							this.getCartCode();
							req.msg('登录成功');
						})
					}
				},
			})
		},
		getphonenumber(e) {
			if (e.target.errMsg == "getPhoneNumber:ok") {
				req.postRequest("/shopApi/weixin/login", {
					openid: this.openid,
					encryptedData: this.encryptedData,
					iv: this.iv,
					sessionKey: this.sessionKey
				}, res => {
					req.setStorage("AUTH_TOKEN", res.data.accessToken)
					setTimeout(() => {
						req.getRequest("/shopApi/weixin/getPhone/" + e.detail.code, {}, res1 => {
							uni.hideLoading();
							req.setStorage("userInfo", res1.data);
							req.setStorage("AUTH_TOKEN", res1.data.accessToken);
							this.showLoginModal = false;
							this.getCartCode();
						})
					}, 500);
				})
			} else {
				uni.hideLoading();
				req.msg("取消登录");
			}
		},
		closeLoginModal() {
			this.showLoginModal = false
		}
	}
}
</script>

<style scoped lang="scss">
.joinMember {
	position: relative;

	.pay_btn {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		top: 920rpx;
		// background: #FE8849;
		color: #fff;
		border-radius: 12rpx;
		width: 391rpx;
		height: 97rpx;
		padding: 10rpx 40rpx;
		text-align: center;
		font-weight: 600;
		box-shadow: #666;

		image {
			width: 100%;
			height: 100%;
		}
	}
}

.login-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.login-container {
	width: 600rpx;
	background: #fff;
	border-radius: 24rpx;
	overflow: hidden;
}

.login-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1px solid #f5f5f5;
}

.login-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.login-close {
	font-size: 40rpx;
	color: #999;
	padding: 10rpx;
}

.login-content {
	padding: 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.login-avatar {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 20rpx;
}

.login-tip {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 40rpx;
}

.login-btn {
	width: 320rpx;
	height: 80rpx;
	border-radius: 40rpx;
	background: #0b78ff;
	display: flex;
	justify-content: center;
	align-items: center;
}

.login-btn button {
	font-size: 30rpx !important;
	color: #fff !important;
	background: transparent;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
