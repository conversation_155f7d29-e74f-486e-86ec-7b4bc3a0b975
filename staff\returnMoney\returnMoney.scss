/deep/ .header .uni-date__x-input,
/deep/ .header .uni-select__selector-empty,
/deep/ .header .uni-select__selector-item {
  font-size: 28rpx !important;
}

.card-replace {
  min-height: 100vh;
  background-color: #f5f7fa;

  .header {
    background: #fff;
    // margin: 0 20rpx;
    padding: 20rpx 15rpx;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 20rpx;
    position: relative;

    .add-btn {
      background: #fef0f0;
      color: #f23030;
      padding: 8px 16px;
      border-radius: 4px;
    }
  }

  .application-list {
    padding: 15rpx 30rpx;

    .application-item {
      background: #fff;
      border-radius: 8px;
      padding: 20rpx 15rpx 20rpx;
      margin-bottom: 15rpx;
      position: relative;

      .status-tag {
        position: absolute;
        top: 0;
        left: 0;
        padding: 4px 12px;
        color: #fff;
        font-size: 14px;
        border-radius: 8px 0 8px 0;

        &.pending {
          background: #ff6b35;
        }

        &.approved {
          background: #67c23a;
        }

        &.rejected {
          background: #f23030;
        }
      }

      .info-row {
        margin-bottom: 8px;
        display: flex;
        align-items: center;

        .label {
          color: #909399;
          font-size: 14px;
          width: 140rpx;
          margin-right: 10rpx;
          text-align: justify;
          text-align-last: justify;
        }

        .value {
          color: #303133;
          font-size: 14px;
          flex: 1;
        }
      }
    }
  }
}

.popup-content {
  background: #fff;
  border-radius: 10px 10px 0 0;

  .popup-header {
    position: relative;
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid #eee;

    text {
      font-size: 16px;
      font-weight: 500;
    }

    .close-icon {
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 24px;
      color: #999;
      padding: 5px;
    }
  }
  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }
  .popup-body {
    padding: 20px;
    max-height: 800rpx;
    overflow: scroll;

    .form-item {
      position: relative;
      margin-bottom: 20px;
      display: flex;
      align-items: center;

      .required {
        color: #f23030;
        margin-right: 5px;
      }

      .label {
        width: 80px;
        font-size: 14px;
        color: #333;
        margin-right: 10px;
      }

      .input,
      .textarea,
      .date-input,
      .select-input {
        flex: 1;
        border: 1px solid red;
        border-radius: 4px;
        font-size: 14px;
        color: #333;
        background: #fff;
      }

      .input {
        height: 40px;
        padding: 0 12px;
      }

      .textarea {
        height: 80px;
        padding: 10px 12px;
      }

      .date-input {
        height: 40px;
        padding-right: 12px;
        display: flex;
        align-items: center;
        // justify-content: space-between;

        .calendar-icon,
        .arrow-down {
          color: #999;
          font-size: 14px;
        }
      }
      .leader-list {
        position: absolute;
        top: 42px;
        left: 95px;
        right: 0;
        max-height: 300rpx;
        background: #fff;
        border: 1px solid #eee;
        border-radius: 4px;
        overflow-y: auto;
        z-index: 999;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

        .leader-item {
          padding: 20rpx;
          border-bottom: 1px solid #eee;
          cursor: pointer;

          &:hover {
            background: #f5f5f5;
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }

    .form-footer {
      display: flex;
      justify-content: space-between;
      gap: 20rpx;
      margin-top: 60rpx;
      padding: 0 30rpx 40rpx;

      .btn {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 8rpx;
        font-size: 32rpx;
        text-align: center;

        &.cancel {
          background: #f7f7f7;
          color: #333;
        }

        &.save {
          background: #f23030;
          color: #fff;
        }
      }
    }
  }
}
