<template>
	<view class="container">
		<!-- 搜索框 -->
		<view class="search-box">
			<uni-search-bar v-model="searchValue" clearButton="auto" placeholder="搜索单据编号" :cancelButton="false"
				@input="handleSearch" />
		</view>

		<!-- 标签页 -->
		<view class="tab-container">
			<text :class="['tab-item', current === 0 ? 'active' : '']" @click="handleTabChange(0)">单据审核</text>
			<text :class="['tab-item', current === 1 ? 'active' : '']" @click="handleTabChange(1)">取消已审核单据</text>
		</view>

		<!-- 日期选择 -->
		<view class="date-picker">
			<!-- <uni-datetime-picker type="daterange" v-model="dateRange" @change="handleDateChange" rangeSeparator="至" /> -->
			<uni-datetime-picker v-model="dateRange" type="daterange" @change="handleDateChange">
				<view class="date-input">
					<view
						style="background: red;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
						<text class="iconfont" style="color:#fff;font-size:34rpx;font-weight: 900;">&#xe685;</text>
					</view>
					<text>{{ dateRange[0] || '开始日期' }} 至 {{ dateRange[1] || '结束日期' }}</text>
				</view>
			</uni-datetime-picker>
		</view>
		<!-- 订单列表 -->
		<scroll-view scroll-y class="order-list" @scrolltolower="loadMore">
			<view v-if="orderList.length > 0" class="list-content">
				<view class="item" @click="showDetailPopup(item)" v-for="(item, index) in orderList" :key="index">
					<view v-if="item.audit == '是'" class="status">审核通过</view>
					<view v-if="item.audit == '否' && !item.auditOpinion" class="status" style="background: #F35215;">审核中
					</view>
					<view v-if="item.audit == '否' && item.auditOpinion" class="status" style="background: #F35215;">
						审核不通过</view>
					<view class="info-grid">
						<view class="info-row">
							<view class="info-item">
								<text class="label">单据编号：</text>
								<text class="value">{{ item.documentId }}</text>
							</view>
						</view>
						<view class="info-row">
							<view class="info-item full">
								<text class="label">单位名称：</text>
								<text class="value">无</text>
							</view>
						</view>
						<view class="info-row">
							<view class="info-item full">
								<text class="label">日　　期：</text>
								<text class="value">{{ item.date }}</text>
							</view>
						</view>
						<view class="info-row">
							<view class="info-item">
								<text class="label">总 金 额：</text>
								<text class="value red">¥{{ item.totalMoney }}</text>
							</view>
							<view class="info-item">
								<text class="label">预到货期：</text>
								<text class="value">{{ item.expectedDeliveryDate }}</text>
							</view>
						</view>
						<view class="info-row">
							<view class="info-item">
								<text class="label">结款方式：</text>
								<text class="value payment">无</text>
							</view>
							<view class="info-item">
								<text class="label">操 作 员：</text>
								<text class="value operator">{{ item.operator }}</text>
							</view>
						</view>
						<view class="info-row" v-if="item.auditOpinion && current === 0">
							<view class="info-item full">
								<text class="label">原　　因：</text>
								<text class="value">{{ item.auditOpinion }}</text>
							</view>
						</view>
					</view>
					<view v-if="current === 0" class="footer">
						<view class="btns">
							<view class="btn primary" @click.stop="handleApprove(item)">审核通过</view>
							<view class="btn default" @click.stop="handleReject(item)">审核不通过</view>
						</view>
					</view>
					<view v-else class="footer">
						<view class="btns">
							<view class="btn cancel" @click.stop="handleReject(item)">取消审核</view>
						</view>
					</view>
				</view>
				<!-- 加载更多 -->
				<view class="loading-more" v-if="orderList.length > 0">
					<text v-if="loading">加载中...</text>
					<text v-else-if="!hasMore">没有更多数据了</text>
				</view>
			</view>
			<view v-else style="text-align: center;color: #999;">
				<image mode=""
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png"
					style="width: 372rpx;height: 312rpx;margin-top: 50rpx;">
				</image>
				<view style="margin-top: 10rpx;font-size: 28rpx;">暂无数据</view>
			</view>
		</scroll-view>

		<!-- 审核不通过原因弹窗 -->
		<uni-popup ref="rejectPopup" type="center">
			<view class="popup-content">
				<view class="popup-header">
					<text>商品编号：{{ currentItem.documentId }}</text>
					<text class="close-icon" @click="closeRejectPopup">×</text>
				</view>
				<view class="popup-body">
					<textarea v-model="rejectReason" class="reason-input"
						:placeholder="current === 0 ? '请输入审核不通过原因' : '请输入审核取消原因'"></textarea>
				</view>
				<view class="popup-footer">
					<view class="btn default" @click="closeRejectPopup">取消</view>
					<view class="btn primary" @click="confirmReject">保存</view>
				</view>
			</view>
		</uni-popup>

		<!-- 详情弹窗 -->
		<uni-popup ref="detailPopup" type="bottom">
			<view class="popup-content detail-popup">
				<view class="popup-header">
					<text>单据审核({{ detailList.length }}张单)</text>
					<text class="close-icon" @click="closeDetailPopup">×</text>
				</view>
				<scroll-view scroll-y class="detail-order-list">
					<view class="list-content">
						<view class="item" v-for="(item, index) in detailList" :key="index">
							<!-- <view class="status">审核通过</view> -->
							<view class="info-grid">
								<view class="info-row">
									<view class="info-item">
										<text class="label">商品编号：</text>
										<text class="value">{{ item.productCode }}</text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item full">
										<text class="label">产品名称：</text>
										<text class="value">{{ item.goodsName }}</text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item">
										<text class="label">包装数量：</text>
										<text class="value payment">{{ item.packageQuantity }}</text>
									</view>
									<view class="info-item">
										<text class="label">订单数量：</text>
										<text class="value payment">{{ item.quantity }}</text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item">
										<text class="label">商品规格：</text>
										<text class="value">{{ item.doseSpec }}</text>
									</view>
									<view class="info-item">
										<text class="label">单　　价：</text>
										<text class="value red">¥{{ item.unitPrice }}</text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item">
										<text class="label">金　　额：</text>
										<text class="value red">¥{{ item.amount }}</text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item full">
										<text class="label">生产厂家：</text>
										<text class="value">{{ item.factory }}</text>
									</view>
								</view>
							</view>
						</view>
						<!-- 加载更多 -->
					</view>
				</scroll-view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
const req = require('../../utils/request')
export default {
	data() {
		return {
			searchValue: '',
			current: 0,
			dateRange: [],
			rejectReason: '', // 审核不通过原因
			currentItem: null, // 当前操作的订单
			orderList: [],
			detailList: [],
			page: 1,
			pageSize: 5,
			loading: false,
			hasMore: true,
			showDetail: false,
			timer: null
		}
	},
	onLoad() {
		this.getOrderList()
	},
	methods: {
		// 获取订单列表
		getOrderList(reset = false) {
			if (reset) {
				this.page = 1
				this.orderList = []
				this.hasMore = true
			}

			if (!this.hasMore) return

			// 模拟接口调用
			const params = {
				pageNum: this.page,
				pageSize: this.pageSize,
				startTime: this.dateRange[0],
				endTime: this.dateRange[1],
				documentId: this.searchValue,
				audit: this.current === 0 ? '否' : '是'
			}

			// TODO: 替换为实际的接口调用
			req.getRequest("/shopApi/saleOrder/page", params, res => {
				console.log(res);
				if (res.code === 200) {
					const { items, total } = res.data
					this.orderList = [...this.orderList, ...items]
					this.hasMore = this.orderList.length < total
				}
			})
		},
		// 加载更多
		loadMore() {
			this.page++
			this.getOrderList()
		},
		// 搜索
		handleSearch(e) {
			clearTimeout(this.timer)
			this.timer = setTimeout(() => {
				this.searchValue = e
				this.getOrderList(true)
			}, 500)
		},
		// 切换标签
		handleTabChange(index) {
			this.current = index
			this.getOrderList(true)
		},
		// 日期变化
		handleDateChange(e) {
			console.log('日期变化：', e)
			this.getOrderList(true)
		},
		handleApprove(item) {
			// 处理审核通过

			uni.showModal({
				title: '提示',
				content: '确定审核通过吗？',
				success: (res) => {
					if (res.confirm) {
						req.postRequest("/shopApi/saleOrder/update", {
							documentId: item.documentId,
							audit: '是'
						}, res => {
							req.msg("审核通过")
							this.getOrderList(true)
						})
					}
				}
			})
		},

		handleReject(item) {
			this.currentItem = item
			this.rejectReason = ''
			this.$refs.rejectPopup.open()
		},
		// 关闭审核不通过弹窗
		closeRejectPopup() {
			this.$refs.rejectPopup.close()
			this.rejectReason = ''
			setTimeout(() => {
				this.currentItem = null
			}, 100);
		},
		// 确认审核不通过
		confirmReject() {
			if (!this.rejectReason.trim()) {
				req.msg('请输入原因')
				return
			}
			req.showLoading();
			req.postRequest("/shopApi/saleOrder/update", {
				documentId: this.currentItem.documentId,
				audit: '否',
				auditOpinion: this.rejectReason
			}, res => {
				uni.hideLoading();
				req.msg(this.current === 0 ? "审核已拒绝" : "取消审核成功")
				this.getOrderList(true)
				this.$refs.rejectPopup.close()
				this.rejectReason = ''
				this.currentItem = null
			})

		},
		// 显示详情弹窗
		showDetailPopup(item) {
			req.showLoading();
			req.getRequest("/shopApi/saleOrder/detailPage", {
				documentId: item.documentId
			}, res => {
				uni.hideLoading();
				this.detailList = res.data.items
				this.$refs.detailPopup.open()
			})
		},
		// 关闭详情弹窗
		closeDetailPopup() {
			this.$refs.detailPopup.close()
			setTimeout(() => {
				this.currentItem = null
			}, 100)
		}
	}
}
</script>

<style lang="scss" scoped>
@import './orderApproval.scss';
</style>
