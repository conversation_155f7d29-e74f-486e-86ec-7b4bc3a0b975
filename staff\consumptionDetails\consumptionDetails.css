.title {
  padding: 30rpx 20rpx 50rpx;
  border-bottom: 1px solid #ddd;
  background: #fff;
}

.merchant {
  font-size: 40rpx;
  text-align: center;
}

.allMoney {
  font-weight: 900;
  font-size: 50rpx;
  text-align: center;
  margin-top: 20rpx;
}

.lumpSum_section {
  padding: 20rpx 40rpx;
  background: #fff;
}

.lumpSum {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.goods_section {
  margin-top: 30rpx;
}

.goods_title {
  display: flex;
  justify-content: space-between;
  background: #fff;
  padding: 20rpx 30rpx;
}

.goods {
  background-color: #fff;
  font-size: 24rpx;
  display: flex;
  justify-content: space-between;
}

.goods_item {
  padding: 20rpx 30rpx;
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
}
/* 处方单样式 */
.prescription-section {
  margin-top: 30rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}
.prescription-section > view:first-child {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.prescription-section image {
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.prescription-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.prescription-header text:first-child {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}
.prescription-header .save-tip {
  font-size: 24rpx;
  color: #999;
}
.prescription-section image {
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: opacity 0.3s;
}
.prescription-section image:active {
  opacity: 0.8;
}