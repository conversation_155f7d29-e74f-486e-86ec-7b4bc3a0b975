<template>
	<view class="couponDetail">
		<view style="width: 100%;background-color: #fff;padding: 40rpx 34rpx 10rpx;box-sizing: border-box;">
			<view style="position: relative;margin-bottom: 30rpx;">
				<image
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/583009e12b774a8aa3f4d6c54f7cc664.png"
					style="height: 216rpx;" mode="aspectFit">
				</image>
				<view class="title" style="top: 22rpx ;left: 22rpx;">【线上】满59-20元优惠劵</view>
				<view class="title" style="bottom: 22rpx ;left: 22rpx;">
					<view style="font-weight: 400;">¥<text style="font-weight: 500;">20</text>代金劵</view>
					<view style="font-size: 24rpx;font-weight: 400;">2024.11.19~2024.12.04</view>
				</view>
				<view class="title"
					style="width: 108rpx;text-align: center;right: 54rpx;top: 70rpx;font-size: 24rpx;line-height: 30rpx;">
					<view>20元</view>
					<view>满减劵</view>
				</view>
			</view>
			<view class="code">
				<image
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/583009e12b774a8aa3f4d6c54f7cc664.png"
					style="width: 560rpx;height: 188rpx;" mode="aspectFit"></image>
				<view style="text-align: center;color: #333333;font-size: 40rpx;line-height: 60rpx;">1-65LB6R8OTQROB
				</view>
			</view>
			<view class="text">
				<view style="font-weight: 600;margin-bottom: 10rpx;">使用说明:</view>
				<view>1.</view>
				<view>2.</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {

		};
	}
}
</script>

<style lang="scss" scoped src="./couponDetail.scss"></style>
