<template>
	<view class="user-info">
		<view class="info-item" @click="chooseImage">
			<text>头像</text>
			<view class="right">
				<image :src="userInfo.avatar" mode="aspectFill"></image>
				<text class="iconfont">&#xe667;</text>
			</view>
		</view>
		<view class="info-item">
			<text>昵称</text>
			<view class="right">
				<input type="text" v-model="userInfo.nickName" placeholder="请输入昵称" />
				<text class="iconfont">&#xe667;</text>
			</view>
		</view>
		<view class="info-item">
			<text>手机号</text>
			<view class="right">
				<text>{{ userInfo.phone || '未绑定' }}</text>
				<text class="iconfont">&#xe667;</text>
			</view>
		</view>
		<view class="info-item">
			<text>身份证</text>
			<view class="right">
				<input type="idNum" v-model="userInfo.idNum" maxlength="18" placeholder="请输入身份证号码" />
				<text class="iconfont">&#xe667;</text>
			</view>
		</view>
		<view class="info-item">
			<text>性别</text>
			<view class="right">
				<picker @change="bindPickerChange" :value="userInfo.sex" :range="genderArray">
					<text>{{ genderArray[userInfo.sex] }}</text>
				</picker>
				<text class="iconfont">&#xe667;</text>
			</view>
		</view>

		<button class="save-btn" @click="saveUserInfo">保存</button>
	</view>
</template>

<script>
const req = require("../../utils/request")
export default {
	data() {
		return {
			userInfo: {},
			genderArray: ['女', '男'],
			genderIndex: 0,
			isSubmitting: false
		}
	},
	onLoad() {
		if (req.getStorage("userInfo")) {
			this.userInfo = req.getStorage("userInfo")
			this.genderIndex = this.userInfo.gender || 0
		}
	},
	methods: {
		// 选择头像
		chooseImage() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					// 这里需要先将图片上传到服务器，获取URL
					this.uploadImage(res.tempFilePaths[0])
				}
			})
		},
		// 上传图片
		uploadImage(filePath) {
			req.showLoading('上传中...')
			const apiUrl = req.env[req.env.NODE_ENV].apiUrl;
			uni.uploadFile({
				url: apiUrl + '/shopApi/home/<USER>',
				filePath: filePath,
				name: 'file',
				header: {
					'Authorization': req.getStorage('AUTH_TOKEN') || ''
				},
				success: (res) => {
					uni.hideLoading()
					res = JSON.parse(res.data)
					if (res.code == 200) {
						this.userInfo.avatar = res.data
					}
				},
				fail: () => {
					uni.hideLoading()
					req.msg("上传失败")
				},
			})
		},
		// 性别选择
		bindPickerChange(e) {
			this.userInfo.sex = Number(e.detail.value)
		},
		// 验证表单
		validateForm() {
			if (!this.userInfo.nickName) {
				req.msg("请输入昵称")
				return false
			}

			if (this.userInfo.idNum) {
				if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(this.userInfo.idNum)) {
					req.msg("请输入正确的身份证号码")
					return false
				}
			}

			return true
		},
		// 保存用户信息
		saveUserInfo() {
			if (!this.validateForm()) return
			req.showLoading('保存中...')
			req.postRequest("/shopApi/weixin/updateMemberships", this.userInfo, res => {
				uni.hideLoading()
				if (res.code === 200) {
					req.msg("保存成功")
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.user-info {
	padding: 20rpx;
	background: #f5f5f5;
	min-height: 100vh;

	.info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 20rpx;
		background: #fff;
		margin-bottom: 2rpx;

		.right {
			display: flex;
			align-items: center;
			color: #999;

			image {
				width: 100rpx;
				height: 100rpx;
				border-radius: 50%;
				margin-right: 10rpx;
			}

			input {
				text-align: right;
				margin-right: 10rpx;
			}

			.iconfont {
				font-size: 24rpx;
				margin-left: 10rpx;
			}
		}
	}

	.save-btn {
		position: fixed;
		bottom: 40rpx;
		left: 20rpx;
		right: 20rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background: #ea1306;
		color: #fff;
		border-radius: 40rpx;
		font-size: 32rpx;

		&:active {
			opacity: 0.8;
		}
	}
}
</style>