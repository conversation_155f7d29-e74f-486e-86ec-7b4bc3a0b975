<template>
	<view class="container">
		<view class="account-list">
			<view class="account-item" v-for="(item, index) in accountList" :key="index" @click="editAccount(item)">
				<view class="account-info">
					<text class="name">{{item.name}}</text>
					<text class="account">支付宝账号：{{item.alipayAccount}}</text>
				</view>
				<text class="iconfont">&#xe667;</text>
			</view>
		</view>
		<view class="add-btn" @click="addAccount">
			<text>添加账号</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				accountList: [
					{
						name: '张三',
						alipayAccount: '<EMAIL>'
					},
					{
						name: '李四',
						alipayAccount: '<EMAIL>'
					}
				]
			}
		},
		methods: {
			editAccount(item) {
				uni.navigateTo({
					url: '/staff/editAccount/editAccount?data=' + JSON.stringify(item)
				})
			},
			addAccount() {
				uni.navigateTo({
					url: '/staff/editAccount/editAccount'
				})
			}
		}
	}
</script>

<style>
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 8rpx 8rpx 100rpx;
		box-sizing: border-box;
	}
	
	.account-list {
		
	}
	
	.account-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-radius: 10rpx;
		margin-bottom: 10rpx;
		background-color: #fff;
		/* border-bottom: 1rpx solid #eee; */
	}
	
	.account-info {
		display: flex;
		flex-direction: column;
	}
	
	.name {
		font-size: 32rpx;
		color: #333;
		margin-bottom: 10rpx;
	}
	
	.account {
		font-size: 28rpx;
		color: #666;
	}
	
	.icon-right {
		color: #999;
		font-size: 32rpx;
	}
	
	.add-btn {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background-color: #EA1306;
		color: #fff;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 32rpx;
	}
</style>
