.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.search-box {
  background-color: #fff;
}

.tab-container {
  background-color: #fff;
  padding: 0 20rpx;
  display: flex;
  border-bottom: 1px solid #eee;

  .tab-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    padding: 20rpx 30rpx;
    font-size: 30rpx;
    color: #666;

    &.active {
      color: #333;
      font-weight: bold;

      &::after {
        content: "";
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        width: 100%;
        height: 5rpx;
        background-color: #ff0000;
        border-radius: 2rpx;
      }
    }
  }
}

.date-picker {
  background-color: #fff;
  padding: 20rpx;
  margin: 20rpx;

  .date-input {
    border: 1px solid red;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    background: #fff;
    height: 35px;
    padding-right: 12px;
    display: flex;
    align-items: center;
  }
}

.order-list {
  flex: 1;
  height: 0;
  padding: 0;

  .list-content {
    padding: 0 20rpx;
    padding-bottom: env(safe-area-inset-bottom);
  }

  .item {
    background-color: #fff;
    padding: 30rpx;
    margin-bottom: 20rpx;
    border-radius: 15rpx;
    position: relative;

    .status {
      position: absolute;
      top: 0;
      left: 0;
      background: red;
      color: #fff;
      padding: 5rpx 10rpx;
      font-size: 24rpx;
      border-radius: 15rpx 0 15rpx 0;
      display: inline-block;
    }

    .info-grid {
      margin-top: 20rpx;

      .info-row {
        display: flex;
        margin-bottom: 16rpx;

        &:first-child {
          padding-bottom: 16rpx;
          border-bottom: 1px dashed #e3e4e5;
        }

        .info-item {
          flex: 1;
          display: flex;
          align-items: center;
          font-size: 28rpx;

          &.full {
            flex: 2;
          }

          .label {
            width: 155rpx;
            color: #333;
            letter-spacing: 2rpx;
          }

          .value {
            flex: 1;
            color: #666;

            &.red {
              color: #ff0000;
              font-weight: bold;
            }

            &.profit {
              color: #52c41a;
              font-weight: 900;
            }

            &.payment {
              color: #0b78ff;
            }

            &.operator {
              color: #722ed1;
            }
          }
        }
      }
    }

    .footer {
      margin-top: 20rpx;
      display: flex;
      justify-content: flex-end;
      gap: 20rpx;

      .btns {
        display: flex;
        gap: 20rpx;
      }

      .btn {
        padding: 18rpx 30rpx;
        font-size: 26rpx;
        border-radius: 30rpx;

        &.primary {
          background-color: #ff0000;
          color: #fff;
        }

        &.default {
          background-color: #eff5fb;
          color: #666;
        }

        &.cancel {
          background-color: #f35215;
          color: #fff;
        }

        &.detail {
          background-color: #fff;
          color: #666;
          border: 1px solid #ddd;
        }
      }

      .tag {
        padding: 6rpx 20rpx;
        font-size: 24rpx;
        border-radius: 20rpx;

        &.success {
          background-color: #f0f9eb;
          color: #67c23a;
        }

        &.error {
          background-color: #fef0f0;
          color: #f56c6c;
        }
      }
    }
  }
}

.popup-content {
  width: 700rpx;
  background-color: #fff;
  border-radius: 24rpx;

  .popup-header {
    position: relative;
    padding: 30rpx;
    // text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #fff;
    background-color: #ff0000;
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;

    .close-icon {
      position: absolute;
      right: 30rpx;
      top: 50%;
      transform: translateY(-50%);
      font-size: 40rpx;
      color: #fff;
      line-height: 1;
    }
  }

  .popup-body {
    padding: 30rpx;

    .reason-input {
      width: 100%;
      height: 240rpx;
      background-color: #f8f8f8;
      border-radius: 6rpx;
      padding: 20rpx;
      font-size: 28rpx;
      box-sizing: border-box;
    }
  }

  .popup-footer {
    padding: 0 30rpx 30rpx;
    display: flex;
    gap: 20rpx;

    .btn {
      flex: 1;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      border-radius: 40rpx;
      font-size: 28rpx;

      &.default {
        background-color: #eff5fb;
        color: #666;
      }

      &.primary {
        background-color: #ff0000;
        color: #fff;
      }
    }
  }
}

.loading-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;
}

.detail-popup {
  width: 100% !important;
  height: 800rpx;
  border-radius: 24rpx 24rpx 0 0 !important;
  padding-bottom: 80rpx;

  .detail-order-list {
    height: 800rpx;
    background: #f5f5f5;

    .list-content {
      padding: 20rpx;
    }

    .item {
      background-color: #fff;
      padding: 30rpx;
      margin-bottom: 20rpx;
      border-radius: 15rpx;
      position: relative;

      .status {
        position: absolute;
        top: 0;
        left: 0;
        background: red;
        color: #fff;
        padding: 5rpx 10rpx;
        font-size: 24rpx;
        border-radius: 15rpx 0 15rpx 0;
        display: inline-block;
      }

      .info-grid {
        margin-top: 20rpx;

        .info-row {
          display: flex;
          margin-bottom: 16rpx;

          &:first-child {
            padding-bottom: 16rpx;
            border-bottom: 1px dashed #e3e4e5;
          }

          .info-item {
            flex: 1;
            display: flex;
            align-items: center;
            font-size: 28rpx;

            &.full {
              flex: 2;
            }

            .label {
              width: 155rpx;
              color: #333;
              letter-spacing: 2rpx;
            }

            .value {
              flex: 1;
              color: #666;

              &.red {
                color: #ff0000;
                font-weight: bold;
              }

              &.profit {
                color: #52c41a;
                font-weight: 900;
              }

              &.payment {
                color: #0b78ff;
              }

              &.operator {
                color: #722ed1;
              }
            }
          }
        }
      }
    }
  }
}
