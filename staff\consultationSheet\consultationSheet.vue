<template>
    <view>
        <view class="header">
            <uni-search-bar @input='searchConfirm' v-model="searchValue" placeholder='请输入患者姓名' cancelButton='none'
                @clear="searchClear">
            </uni-search-bar>
        </view>

        <view class="content">
            <view @click="toDetail(item)" class="nav" v-for="(item, index) in RegistrationList" :key="index">
                <view class="canState" v-if="item.state == 0">待接诊</view>
                <view class="nocanState" v-if="item.state == 1">已接诊</view>
                <view class="canState" v-if="item.state == 2 || item.state == 8 || item.state == 9">已完诊</view>
                <view class="doctorDetail">
                    <view>
                        <image
                            src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240801/ac29575146fb4c498dc55c2e2fcde020.png"
                            mode="aspectFit" class="avatar"></image>
                    </view>

                    <view class="dotInfo">
                        <view class="dotName">
                            <view style="font-size: 18px;font-weight:600;margin-right:10px;"
                                :class="[item.state == 0 ? 'inquiryState0' : 'inquiryState1']">{{ item.name }}</view>
                            <view style="color:#cccccc">|</view>
                            <view style="margin-left:10px">{{ item.sex == 1 ? '男' : '女' }}-{{ item.age }}岁</view>
                            <image src="/static/image/rico.png" class="rico"></image>
                        </view>
                        <view class="dotIntro c9">
                            <view>申请服务：</view>
                            <view>
                                图文问诊
                            </view>
                        </view>
                        <view class="dotMoney c9">
                            <view>问诊时间：<text>{{ item.createDate }}</text></view>
                        </view>

                        <view v-if="item.orderId" class="dotMoney c9">
                            <view>订单号：<text>{{ item.orderId }}</text></view>
                        </view>
                        <view v-if="item.clerkName" class="dotMoney c9">
                            <view>流程状态：<text>{{ item.clerkName }}</text></view>
                        </view>
                        <view v-if="item.isRatify == 4" class="dotMoney c9">
                            <view>拒绝原因：<text>{{ item.refuseReason }}</text></view>
                        </view>
                        <view style="position: absolute;right: 20rpx;bottom: 20rpx;font-size:24rpx;"
                            :style="{ color: item.isRatify == 1 ? '#67C23A' : '#FF0000' }"
                            v-if="item.isRatify == 1 || item.isRatify == 4">
                            <view>{{ item.isRatify == 1 ? '已通过' : '已拒绝' }}</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
    data() {
        return {
            doctorId: req.getStorage('uid'),
            searchValue: '',
            inquiryState: 0,
            RegistrationList: [],//挂号列表
        }
    },
    onLoad() {
    },
    onShow() {
        this.getRegistration()
    },
    methods: {
        // 搜索框
        searchConfirm(e) {
            this.searchValue = e
            clearTimeout(this.timer)
            this.timer = setTimeout(() => {
                this.getRegistration()
            }, 1500)
        },
        searchClear(res) {
            this.searchValue = ''
        },

        // 查询挂号列表
        getRegistration() {
            req.getRequest('/shopApi/shopEmployee/bookingRegister', {
                search: this.searchValue
            }, res => {
                this.RegistrationList = res.data
            })
        },
        // 去问诊
        toDetail(item) {
            console.log(item);
            wx.navigateTo({
                url: '/staff/staffChat/staffChat?staffId=' + item.staffId + '&patientId=' + item.uid + '&id=' + item.id + '&isOnline=' + item.isOnline + '&uid=' + item.uid,
            });

        },
    }
}
</script>

<style>
@import "./consultationSheet.css";
</style>