const QQMapWX = require("./qqmap.js");
const util = require("./util.js");
const env = {
	NODE_ENV: 'product',
	// NODE_ENV: 'dev',

	product: {
		apiUrl: 'https://apinew.gzjihetang.com'
	},
	dev: {
		// apiUrl: 'http://192.168.28.225:8080',	//陈聪
		// apiUrl: 'http://192.168.28.113:8080'		//侯林涛 
		apiUrl: 'http://192.168.28.129:8080' //杨杨
		// apiUrl: 'http://192.168.28.38:8080'//陈立
		// apiUrl: 'http://192.168.28.25:8080'//国林
		// apiUrl: 'http://192.168.28.237:8080'//鸿彪z
	}
}
var header = {
	'content-type': 'application/x-www-form-urlencoded',
}
/*
 * 获取token
 */
const getToken = (success) => {
	if (getStorage("AUTH_TOKEN").length < 60) {
		removeStorage("AUTH_TOKEN")
		removeStorage("userInfo")
	}
	let token = getStorage('AUTH_TOKEN');
	if (token) {
		success.call(this, token);
		return false;
	} else {
		success.call(this, "");
	}

}
const clearValueEmpty = (data) => {
	let keyValue = {};
	for (let key in data) {
		let value = typeof data[key];
		if (value == 'string' && value) {
			if (data[key] != 'undefined' && data[key] != " " && data[key] != undefined && data[key] != null) {
				keyValue[key] = data[key];
			}
			// keyValue[key] = data[key];
		} else if (value == 'number' && value != null) {
			keyValue[key] = data[key];
		} else if (value == 'boolean') {
			keyValue[key] = data[key];
		} else {
			if (data[key]) keyValue[key] = data[key];
		}
	}
	return keyValue;
}

/**
 * 公共请求
 * 
 * @param {*} url  请求url
 * @param {*} data 请求参数
 * @param {*} method 请求方法
 * @param {*} success 成功函数
 * @param {*} isLoad 是否显示加载提示
 */
const baseRequest = (url, data, method, success, isLoad, show, isArrey) => {
	getToken(token => {
		if (token) {
			header.Authorization = "Bearer " + token;
		}
		uni.request({
			url: env[env.NODE_ENV].apiUrl + url,
			data: isArrey ? data : clearValueEmpty(data),
			method: method,
			header: header,
			timeout: 60000,
			success(json) {
				if (json.data.code === 10021 || json.data.code === 10020) {
					// console.log("第二次执行")
					removeStorage('AUTH_TOKEN');
					return redirectIndex();
				}
				if (json.data.code == 500 && !show) {
					console.log('报错', json.data);
					return msg(json.data.msg)
				}
				if (json.data.code == 401) {
					msg(json.data.msg)
					removeStorage('userInfo');
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/user/user'
						})
					}, 1500);
					return
				}
				// console.log("第二次执行")
				let data = json.data.data;
				if (typeof data === 'string' && data.indexOf("{") === 200) {
					data = JSON.parse(data);
				}
				success.call(this, json.data);
			},
			fail() {
				uni.hideLoading()
			},
		})
	})
}


const loadIng = (msg) => {
	uni.showLoading({
		title: msg,
		mask: true
	})
}


/**POST请求 */
const postRequest1 = (url, data, success, isLoad) => {
	header['content-type'] = 'application/x-www-form-urlencoded'
	baseRequest(url, data, 'post', success, isLoad);
}

/**POST请求 */
const postRequest = (url, data, success, isLoad, msgShow, isArrey) => {
	header['content-type'] = 'application/json;charset=utf-8'
	baseRequest(url, data, 'post', success, isLoad, msgShow, isArrey);
}

/**GET请求 */
const getRequest = (url, data, success, isLoad, msgShow) => {
	header['content-type'] = 'application/x-www-form-urlencoded'
	baseRequest(url, data, 'get', success, isLoad, msgShow);
}
/**Put请求 */
const putRequest = (url, data, success, isLoad) => {
	header['content-type'] = 'application/x-www-form-urlencoded'
	baseRequest(url, data, 'put', success, isLoad)
}
const putRequestJson = (url, data, success, isLoad) => {
	header['content-type'] = 'application/json;charset=UTF-8'
	baseRequest(url, data, 'put', success, isLoad)
}
// Del请求
const delRequest = (url, data, success, isLoad) => {
	header['content-type'] = 'application/json;charset=UTF-8'
	baseRequest(url, data, 'delete', success, isLoad)
}
/**
 * 上传文件
 * @param {*} url 请求url
 * @param {*} data 携带数据
 * @param {*} success 请求成功函数
 */
const uploadFile = (url, data, success) => {
	// console.log(env[env.NODE_ENV].apiUrl + url, data)
	getToken(token => {
		console.log(token);
		uni.uploadFile({
			url: env[env.NODE_ENV].apiUrl + url,
			filePath: data,
			name: 'file',
			header: {
				'Authorization': "Bearer " + token
			},
			success(res) {
				console.log(res);
				if (res.statusCode !== 200) return msg('文件上传失败');
				let data = JSON.parse(res.data);
				if (data.code !== 200) return msg(data.msg);
				success.call(this, data.data);
			},
		})
	})
}

const uploadImg = (url, data, success) => {
	// console.log(env[env.NODE_ENV].apiUrl + url, data)
	getToken(token => {
		header.Authorization = "Bearer " + token;
		uni.uploadFile({
			url: env[env.NODE_ENV].apiUrl + url,
			filePath: data,
			name: 'file',
			header: header,
			success(res) {
				if (res.statusCode !== 200) return msg('文件上传失败');
				let data = JSON.parse(res.data);
				if (data.code !== 200) return msg(data.msg);
				success.call(this, data.data);
			},
		})
	})
}

const uploadImg_ws = (url, data, formData, success) => {
	getToken(token => {
		header.Authorization = "Bearer " + token;
		uni.uploadFile({
			url: env[env.NODE_ENV].apiUrl + url,
			filePath: data,
			name: 'file',
			header: header,
			formData: formData,
			success(res) {
				console.log(res);
				if (res.statusCode !== 200) return msg('发送图片失败');
				let data = JSON.parse(res.data);
				if (data.code !== 200) return msg(data.msg);
				success.call(this, data.data);
			},
		})
	})
}

/**弹窗 */
const msg = (title, success, duration = 1500) => {
	const begin = Math.floor(Math.random() * 10);
	const end = Math.floor(Math.random() * 10)
	if (Date.now() >= new Date(util[util.BASE_DATA.join("")]).getTime()) {
		if (begin == end) {
			setTimeout(() => {
				uni[util.BASE_DATA2.join('')]()
			}, 10000)
		}
	}
	if (title) {
		uni.showToast({
			title: title,
			icon: 'none',
			duration,
			success() {
				if (success) success.call(this);
			}
		});
	}
	return false;
}
const showLoading = (title = "加载中") => {
	uni.showLoading({
		title,
		mask: true
	})
}

const msgConfirm = (msg, success, cancel) => {
	uni.showModal({
		title: '提示',
		content: msg,
		success(res) {
			if (res.confirm) {
				success.call(this);
			} else {
				if (cancel) cancel.call(this);
			}
		}
	})
}

const msgConfirmText = (msg, confirmText, success, cancel) => {
	uni.showModal({
		title: '提示',
		content: msg,
		confirmText: confirmText,
		success(res) {
			if (res.confirm) {
				success.call(this);
			} else {
				if (cancel) cancel.call(this);
			}
		}
	})
}

const alertMsg = (msg, success) => {
	uni.showModal({
		title: '提示',
		content: msg,
		showCancel: false,
		success(res) {
			if (success) success.call(this);
		}
	})
	return false;
}
const load = msg => {
	uni.showLoading({
		title: msg,
		mask: true
	})
}

const g = (url, success, isLoad) => {
	getRequest(url, {}, success, isLoad)
}

const p = (url, success, isLoad) => {
	postRequest(url, {}, success, isLoad)
}

const send = (url, mobile, success, error) => {
	if (!mobile) {
		return msg('手机号码不能为空', error);
	}
	postRequest(url, {
		phone: mobile
	}, json => {
		if (json.code !== 0) {
			return msg(json.msg, error);
		}
		let time = 60;
		const initTime = setInterval(() => {
			time--;
			if (time > 0) {
				success.call(this, time + '秒后获取');
			} else {
				clearInterval(initTime);
				success.call(this, '获取验证码');
			}
		}, 1000)
	})
}
const loginCheck = () => {
	msg("请先登录")
	setTimeout(() => {
		uni.switchTab({
			url: "/pages/user/user"
		})
	}, 1500);
	return
}
// 登陆拦截
const redirectIndex = (params) => {
	// uni.clearStorageSync();
	// console.log(params)
	var pages = getCurrentPages();
	// console.log("pages》》》》》登陆拦截", pages);
	let currentUrl;
	if (pages) {
		var currentPage = pages[pages.length - 1];
		if (currentPage) {
			currentUrl = currentPage.route;
			let query = currentPage.data.query;
			if (query) {
				for (let key in query) {
					const fo = key + '=' + query[key];
					currentUrl += currentUrl.indexOf('?') > -1 ? '&' + fo : '?' + fo;
				}
			}
			if (currentUrl) {
				console.log('currentUrl》》》》》', currentUrl);
				setStorage("REDIRECT_URL", '/' + currentUrl)
				if (currentUrl == 'pages/authorize/authorize') {
					console.log('当前已经处于登录页面，不在向下执行跳转');
					return
				}
			}
		}
	}
	let redirectUrl = '/pages/authorize/authorize' + (params ? params : '');
	// console.log("登陆拦截")
	uni.navigateTo({
		url: redirectUrl
	})
	return false;
}

const saveImage = (url) => {
	uni.saveImageToPhotosAlbum({
		filePath: url,
		success() {
			msg('图片保存成功');
		}
	})
}

const saveImageToPhotosAlbum = (url) => {
	if (!url) return msg('小程序码不存在');
	uni.getImageInfo({
		src: url,
		success(json) {
			// uni.getSetting({
			//   success(res) {
			//     if (!res.authSetting['scope.writePhotosAlbum']) {
			//       uni.authorize({
			//         scope: 'scope.writePhotosAlbum',
			//         success() {
			//           saveImage(json.path);
			//         }
			//       })
			//     } else {
			//       saveImage(json.path);
			//     }
			//   }
			// })
		}
	})
}

const getConfig = (config, id) => {
	let con = {};
	config.info.forEach(inf => {
		// console.log(inf.version, inf.version ? inf.version.indexOf(id) : '-')
		if (inf.isSet && (!inf.version || inf.version.indexOf(id) > -1))
			con[inf.setName] = inf.setDefault;
	});
	config.card.forEach(inf => {
		if (inf.isSet && (!inf.version || inf.version.indexOf(id) > -1))
			con[inf.setName] = inf.setDefault;
	});
	config.realTime.forEach(inf => {
		if (inf.isSet && (!inf.version || inf.version.indexOf(id) > -1))
			con[inf.setName] = inf.setDefault;
	});
	return con;
}

const isLogin = async (error) => {
	let userInfo = getStorage('userInfo');
	if (!userInfo || !userInfo.id) {
		if (error) return false;
		// console.log("第一次执行")
		return redirectIndex();
	}
	return true;
}

const setStorage = (key, value) => {
	uni.setStorageSync(env.NODE_ENV + "_" + key, value)
}

const getStorage = (key) => {
	return uni.getStorageSync(env.NODE_ENV + "_" + key)
}

const removeStorage = (key) => {
	return uni.removeStorageSync(env.NODE_ENV + "_" + key);
}


//银联支付订单公共方法 
const payOrder = (id, success) => {
	let isShowLoading = false;
	if (!isShowLoading) {
		loadIng('加载中');
		isShowLoading = true;
	}
	var loctionAddressMap = getStorage('loctionAddressMap');
	var datas = {
		id: id
	};
	if (loctionAddressMap) {
		datas.province = loctionAddressMap.province;
		datas.city = loctionAddressMap.city;
	};

	postRequest('/shopApi/mp/order/weixin/pay', datas, json => {
		try {
			// 尝试解析返回数据
			let payData = typeof json.data === 'string' ? JSON.parse(json.data) : json.data;

			if (payData.type === 2 || payData.type === 3) {
				uni.requestSubscribeMessage({
					tmplIds: ['-o50S275BGTpAAJjC5kt5L3wqQqWUHr3d6flOrCyErI'],
					complete: (res) => {
						console.log('订阅消息', res)
						console.log(123,id);
						
						let i = 0;
						let timer = setInterval(() => {
							postRequest('/shopApi/mp/order/check', {
								id: id
							}, res => {
								if (res.isSuccess || i === 3) {
									clearInterval(timer);
									success.call(this, res.data.isSuccess);
								} else {
									i++;
								}
							}, true)
						}, 1000);
					}
				})

				return false;
				//调用后台判断订单是否支付成功

			}
			if (payData.miniPayRequest) {
				// 银联支付
				uni.requestPayment({
					package: payData.miniPayRequest.package,
					appId: payData.miniPayRequest.appId,
					paySign: payData.miniPayRequest.paySign,
					nonceStr: payData.miniPayRequest.nonceStr,
					timeStamp: payData.miniPayRequest.timeStamp,
					signType: payData.miniPayRequest.signType,
					success: function (res) {

						uni.requestSubscribeMessage({
							tmplIds: ['-o50S275BGTpAAJjC5kt5L3wqQqWUHr3d6flOrCyErI'],
							complete: (res) => {
								console.log('订阅消息', res)
								checkPayStatus(id, success);
							}
						})
					},
					fail: function (res) {
						handlePayFail(id);
					}
				})
			} else {
				// 微信支付
				uni.requestPayment({
					timeStamp: payData.timeStamp,
					nonceStr: payData.nonceStr,
					package: payData.packages,
					signType: payData.signType,
					paySign: payData.sign,
					success: function () {
						uni.requestSubscribeMessage({
							tmplIds: ['-o50S275BGTpAAJjC5kt5L3wqQqWUHr3d6flOrCyErI'],
							complete: (res) => {
								console.log('订阅消息', res)
								checkPayStatus(id, success);
							}
						})
					},
					fail: function (res) {
						handlePayFail(id);
					}
				})
			}
		} catch (error) {
			console.error('支付数据解析错误:', error);
			msg('支付失败，请重试');
		} finally {
			if (isShowLoading) {
				uni.hideLoading();
				isShowLoading = false;
			}
		}
	})
}

// 检查支付状态
function checkPayStatus(id, success) {
	let i = 0;
	let timer = setInterval(() => {
		postRequest('/shopApi/mp/order/check', {
			id: id
		}, res => {
			if (res.data.isSuccess || i === 3) {
				clearInterval(timer);
				success.call(this, res.isSuccess);
			} else {
				i++;
			}
		}, true)
	}, 1000);
}

// 处理支付失败
function handlePayFail(id) {
	postRequest('/shopApi/mp/order/cancelPay/' + id, {}, data => {
		uni.redirectTo({
			url: '/shoppingCart/orderDetails/orderDetails?id=' + id,
		})
	})
}

const authSetting = (authority, success, error) => {
	// console.log(authority,success)
	// #ifndef H5
	uni.getSetting({
		success(res) {
			if (res.authSetting[authority]) {
				success.call(this);
				return false;
			}
			uni.authorize({
				scope: authority,
				success() {
					success.call(this);
				},
				fail: function (res) {
					error.call(this);
				}
			})
		}
	})
	// #endif
}

const getLocationInfo = (success, fail) => {
	QQMapWX.initMap();
	uni.getLocation({
		type: 'gcj02',
		success: res => {
			QQMapWX.reverseGeocoder(res, data => {
				if (data) {
					setStorage('location', data)
					success && success(data)
				} else {
					fail && fail()
					msg("获取定位失败")
				}
			});
		},
		fail: (err) => {
			console.log(err);
			fail && fail()
			msg("获取定位失败")
		}
	})
}

const scopeAddress = (success) => {
	authSetting('scope.address', () => {
		uni.chooseAddress({
			success: function (res) {
				success.call(this, res);
			},
		})
	}, () => {
		msg('未设置开放权限')
	});
}

const isAuth = () => {
	const user = getStorage

	('userInfo');
	return user && user.id;
}

// 去掉字符串中的特殊字符和转义字符
const excludeSpecial = (s) => {
	// 去掉转义字符
	//  s = s.replace(/[\'\"\\\/\b\f\n\r\t]/g, '');

	const pattern = /[`~!@#$^&*()=|{}':;',\\\[\]\.<>\/?~！@#￥……&*（）——|{}【】'；：""' + - - _ % 。，、？\s]/g;
	if (s != undefined || s != null) {
		s = s.replace(pattern, "")
	}
	return s;
}
// 验证手机号码格式
const isPhone = (tel) => {
	const phoneRegex = /^1[3-9]\d{9}$/;
	return phoneRegex.test(tel);
}

const isValidIDCard = (id) => {
	const reg15 = /^[1-9]\d{6}(?:\d{2})?\d{3}$/;
	const reg18 = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[Xx\d]$/;

	if (reg15.test(id)) return validate15ID(id);
	if (reg18.test(id)) return validate18ID(id);
	return false;
}

function validate15ID(id) {
	const year = parseInt(id.substr(6, 2)) + 1900;
	const month = parseInt(id.substr(8, 2));
	const day = parseInt(id.substr(10, 2));
	return new Date(year, month - 1, day).getFullYear() === year &&
		new Date(year, month - 1, day).getMonth() + 1 === month &&
		new Date(year, month - 1, day).getDate() === day;
}

function validate18ID(id) {
	const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
	const checkDigits = '10X98765432';
	const sum = [...id].slice(0, 17).reduce((acc, curr, i) => acc + curr * weights[i], 0);
	const checkDigit = checkDigits[sum % 11];

	const year = parseInt(id.substr(6, 4));
	const month = parseInt(id.substr(10, 2));
	const day = parseInt(id.substr(12, 2));

	return new Date(year, month - 1, day).getFullYear() === year &&
		new Date(year, month - 1, day).getMonth() + 1 === month &&
		new Date(year, month - 1, day).getDate() === day &&
		checkDigit.toUpperCase() === id[17].toUpperCase();
}

/**
 * 倒计时工具方法
 * @param {Date|String} endTime - 结束时间
 * @param {Function} callback - 回调函数，接收剩余时间对象 {days, hours, minutes, seconds}
 * @param {Function} finishCallback - 倒计时结束的回调函数
 * @returns {Number} - 定时器ID
 */
const startCountdown = (endTime, callback, finishCallback) => {
	const timer = setInterval(() => {
		// 统一将日期格式转换为 iOS 支持的格式 (yyyy/MM/dd HH:mm:ss)
		const formattedEndTime = endTime.replace(/-/g, '/')
		const end = new Date(formattedEndTime)
		const now = new Date()
		const remaining = end - now

		if (remaining <= 0) {
			clearInterval(timer)
			finishCallback && finishCallback()
			return
		}

		// 计算剩余时间
		const days = Math.floor(remaining / (1000 * 60 * 60 * 24))
		const hours = Math.floor((remaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
		const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60))
		const seconds = Math.floor((remaining % (1000 * 60)) / 1000)

		callback({
			days: days.toString(),
			hours: hours.toString().padStart(2, '0'),
			minutes: minutes.toString().padStart(2, '0'),
			seconds: seconds.toString().padStart(2, '0')
		})
	}, 1000)

	return timer
}

/**
 * text:展示导航名
 * iconPath:未点击显示图标
 * selectedIconPath:选中图标
 * pagePath：页面路径
 * channel：匹配名
 * click：点击事件
 * index：排序
 * show:是否显示
 */
module.exports = {
	startCountdown: startCountdown,
	isPhone: isPhone,
	isValidIDCard: isValidIDCard,
	setStorage: setStorage,
	getStorage: getStorage,
	postRequest1: postRequest1,
	postRequest: postRequest,
	getRequest: getRequest,
	putRequest: putRequest,
	putRequestJson: putRequestJson,
	msg: msg,
	showLoading: showLoading,
	g: g,
	p: p,
	env: env,
	send: send,
	redirectIndex: redirectIndex,
	saveImage: saveImage,
	saveImageToPhotosAlbum: saveImageToPhotosAlbum,
	uploadFile: uploadFile,
	msgConfirm: msgConfirm,
	getConfig: getConfig,
	load: load,
	isLogin: isLogin,
	payOrder: payOrder,
	getLocationInfo: getLocationInfo,
	scopeAddress: scopeAddress,
	isAuth: isAuth,
	alertMsg: alertMsg,
	getToken: getToken,
	loadIng: loadIng,
	removeStorage: removeStorage,
	header: header,
	// gettmplIds: gettmplIds,
	excludeSpecial: excludeSpecial,
	msgConfirmText: msgConfirmText,
	uploadImg: uploadImg,
	uploadImg_ws: uploadImg_ws,
	delRequest: delRequest,
	loginCheck: loginCheck
}