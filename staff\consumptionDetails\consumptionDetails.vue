<template>
	<view v-if="show" style="padding-bottom:150rpx;">
		<view class="title">
			<view class="merchant">
				<view>广东集和堂健康产业管理有限公司</view>
				<view>伟诚中医门诊部</view>
			</view>
			<view class="allMoney">-¥{{ orderInfo.money }}</view>
		</view>
		<view class="lumpSum_section">
			<view class="lumpSum">
				<view>医保统筹基金支付金额</view>
				<view>{{ orderInfo.hisInsuranceFee == "0.00" ? "¥" : "-" }}{{ orderInfo.hisInsuranceFee }}</view>
			</view>
			<view class="lumpSum">
				<view>其他基金支付金额</view>
				<view>{{ orderInfo.hisInsuranceChronicDiseaseFee == "0.00" ? "¥" : "-" }}{{
					orderInfo.hisInsuranceChronicDiseaseFee }}</view>
			</view>
			<view class="lumpSum">
				<view>医保个人账户支付金额</view>
				<view>{{ orderInfo.hisPersonalMedicalCardFee == "0.00" ? "¥" : "-" }}{{
					orderInfo.hisPersonalMedicalCardFee }}
				</view>
			</view>
			<view class="lumpSum">
				<view>个人现金支付金额</view>
				<view>{{ orderInfo.hisWeChatFee == "0.00" ? "¥" : "-" }}{{ orderInfo.hisWeChatFee }}</view>
			</view>
			<view v-if="orderInfo.hisCheckUpFee" class="lumpSum">
				<view>诊查费金额</view>
				<view>{{ orderInfo.hisCheckUpFee }}.00</view>
			</view>
			<view class="lumpSum">
				<view>当前状态</view>
				<view>{{ orderInfo.stateName }}</view>
			</view>
			<view class="lumpSum">
				<view>支付时间</view>
				<view>{{ orderInfo.payTime }}</view>
			</view>
			<view class="lumpSum">
				<view>交易单号</view>
				<view>{{ orderInfo.orderIdStr }}</view>
			</view>
			<view class="lumpSum">
				<view>取药点</view>
				<view>{{ orderInfo.merchantName }}</view>
			</view>
		</view>
		<view class="goods_section">
			<view @click="goodsShow" class="goods_title">
				<view style="border-left:3px solid skyblue;padding-left:15rpx;">普通门诊</view>
				<view>共消费¥{{ orderInfo.money }} <uni-icons style="margin-left:10rpx;"
						:type="isGoodsShow ? 'down' : 'up'" size="18"></uni-icons>
				</view>
			</view>
			<block v-if="isGoodsShow">
				<view class="goods" style="background: none;">
					<view class="goods_item" style="flex:2">药品名</view>
					<view class="goods_item">单价</view>
					<view class="goods_item">数量</view>
					<view class="goods_item">总额</view>
					<!-- <view class="goods_item" style="flex:1.5">操作</view> -->
				</view>
				<view class="goods" style="border-bottom:1px solid #eee;" v-for="(item, index) in goodsList"
					:key="index">
					<view class="goods_item" style="flex:2">{{ item.product_name }}</view>
					<view class="goods_item">¥{{ item.sale_money || item.package_unit_price }}</view>
					<view class="goods_item">{{ item.quantity }}</view>
					<view class="goods_item" style="color: #0B78FF;">¥{{ ((item.sale_money || item.package_unit_price) *
						item.quantity).toFixed(2) }}</view>
					<!-- <view class="goods_item" style="flex:1.5;color:red;" @click="addCart(item)">再来一单</view> -->
				</view>
			</block>
			<view v-if="orderInfo.referralImg" class="prescription-section">
				<view class="prescription-header">
					<text>处方单</text>
					<text class="save-tip">点击图片可放大查看</text>
				</view>
				<image show-menu-by-longpress :src="orderInfo.referralImg" mode="widthFix" style="width: 100%;"
					@click="previewImage"></image>
			</view>
		</view>
	</view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
	data() {
		return {
			orderId: "",
			orderInfo: {},
			goodsList: [],
			show: false,
			isGoodsShow: true,
			uid: '',
			isNew: null
		};
	},
	onLoad(options) {
		uni.showLoading({
			title: '加载中'
		});
		this.orderId = options.orderId
		this.uid = options.uid
		this.isNew = options.isNew
		this.getDetail()
	},
	methods: {
		getDetail() {
			req.getRequest("/shopApi/shopEmployee/orderDetailsById", {
				orderId: this.orderId, uid: this.uid, carryGoodsMode: 1, isNew: this.isNew
			}, res => {
				uni.hideLoading()
				console.log(res);
				this.show = true
				res.data.orderInfo.money = res.data.orderInfo.money.toFixed(2)
				res.data.orderInfo.hisInsuranceFee = res.data.orderInfo.hisInsuranceFee.toFixed(2)
				res.data.orderInfo.hisPersonalMedicalCardFee = res.data.orderInfo.hisPersonalMedicalCardFee.toFixed(2)
				res.data.orderInfo.hisInsuranceChronicDiseaseFee = res.data.orderInfo.hisInsuranceChronicDiseaseFee.toFixed(2)
				res.data.orderInfo.hisWeChatFee = res.data.orderInfo.hisWechatFee.toFixed(2)
				// res.data.orderInfo.payTime = res.data.orderInfo.payTime.split(" ")[0]
				res.data.prodcuctInfo.forEach(item => {
					item.product_name = item.product_name.replace(/\d.*$/, '');
				});
				this.orderInfo = res.data.orderInfo
				this.goodsList = res.data.prodcuctInfo
			})
		},
		goodsShow() {
			this.isGoodsShow = !this.isGoodsShow
		},
		previewImage() {
			uni.previewImage({
				urls: [this.orderInfo.referralImg],
				longPressActions: {
					itemList: ['保存图片'],
					success: function (data) {
						if (data.tapIndex === 0) {
							uni.downloadFile({
								url: this.orderInfo.referralImg,
								success: (res) => {
									if (res.statusCode === 200) {
										uni.saveImageToPhotosAlbum({
											filePath: res.tempFilePath,
											success: function () {
												uni.showToast({
													title: '保存成功',
													icon: 'success'
												});
											},
											fail: function () {
												uni.showToast({
													title: '保存失败',
													icon: 'none'
												});
											}
										});
									}
								},
								fail: () => {
									uni.showToast({
										title: '图片下载失败',
										icon: 'none'
									});
								}
							});
						}
					},
					fail: function () {
						uni.showToast({
							title: '操作失败',
							icon: 'none'
						});
					}
				}
			});
		},
	}
}
</script>

<style lang="scss">
@import "./consumptionDetails.css";
</style>