.points-record {
  min-height: 100vh;
  background: #f6f7fb;
  padding: 20rpx;
  padding-bottom: env(safe-area-inset-bottom);

  .record-item {
    background: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.03);

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .left {
        width: 100%;
        .title {
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
          margin-bottom: 8rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .time {
          font-size: 24rpx;
          color: #999;
        }
      }

      .points {
        font-size: 36rpx;
        font-family: DIN;
        font-weight: 500;

        &.plus {
          color: #ea1306;
          &::before {
            content: "+";
          }
        }

        &.minus {
          color: #333;
          &::before {
            content: "-";
          }
        }

        .unit {
          font-size: 24rpx;
          margin-left: 4rpx;
          font-weight: normal;
          font-family: initial;
        }
      }
    }

    .content {
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;
      padding-top: 20rpx;
      border-top: 1rpx solid #f5f5f5;

      .label {
        color: #999;
        margin-right: 20rpx;
      }
    }
  }

  .empty {
    padding-top: 200rpx;
    text-align: center;

    image {
      width: 240rpx;
      height: 240rpx;
      margin-bottom: 20rpx;
    }

    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .load-more {
    text-align: center;
    padding: 20rpx 0;
    color: #999;
    font-size: 24rpx;
  }
}
