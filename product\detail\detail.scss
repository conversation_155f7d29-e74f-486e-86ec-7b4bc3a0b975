@mixin text-ellipsis($line: 1) {
  @if $line == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $line;
    overflow: hidden;
  }
}

.product-detail {
  min-height: 100vh;
  background: #f6f7fb;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));

  .swiper {
    width: 100%;
    height: 750rpx;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 120rpx;
      background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.05));
    }

    image {
      width: 100%;
      height: 100%;
    }

    .prescription-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
    }
  }

  .info-section {
    background: #fff;
    padding: 20rpx 30rpx 0;
    margin: -40rpx 20rpx 0rpx;
    border-radius: 15rpx;
    position: relative;
    // box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.05);

    .group {
      position: relative;
      height: 140rpx;
      margin-bottom: 32rpx;
      border-radius: 20rpx;
      overflow: hidden;

      .ptbg {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
      }

      .groups {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
        padding: 0 30rpx;
        justify-content: space-between;
        height: 100%;

        .left {
          .price {
            font-size: 52rpx;
            font-weight: 600;
            color: #fff;
            font-family: DIN;
          }

          .origin {
            font-size: 28rpx;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: line-through;
            margin-left: 12rpx;
          }
        }

        .right {
          text-align: right;

          .tag-text {
            display: inline-block;
            color: #fff;
            font-size: 28rpx;
            padding: 4rpx 16rpx;
            border-radius: 24rpx;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(4px);
            margin-bottom: 12rpx;
          }

          .endtime {
            display: flex;
            align-items: center;
            font-size: 24rpx;
            color: #fff;

            text {
              display: inline-block;
              min-width: 40rpx;
              height: 40rpx;
              line-height: 40rpx;
              background: rgba(0, 0, 0, 0.4);
              border-radius: 6rpx;
              text-align: center;
              margin: 0 4rpx;
              font-family: DIN;
              font-weight: 500;
            }

            view {
              margin-left: 8rpx;
              font-size: 24rpx;
              color: rgba(255, 255, 255, 0.9);
            }
          }
        }
      }
    }

    .price {
      .symbol {
        font-size: 36rpx;
        color: #ea1306;
      }

      .number {
        font-size: 64rpx;
        font-weight: bold;
        color: #ea1306;
        font-family: DIN;
        margin: 0 16rpx;
      }

      .original {
        font-size: 28rpx;
        color: #999;
        text-decoration: line-through;
      }

      .seckill-price {
        display: flex;
        align-items: baseline;
      }

      .seckill-tag {
        margin-top: 16rpx;
        display: flex;
        align-items: center;
        gap: 16rpx;

        .tag-text {
          background: linear-gradient(135deg, #ff6b6b 0%, #ea1306 100%);
          color: #fff;
          font-size: 24rpx;
          padding: 6rpx 16rpx;
          border-radius: 20rpx;
          font-weight: 500;
        }

        .countdown {
          font-size: 24rpx;
          color: #ea1306;
          background: rgba(234, 19, 6, 0.1);
          padding: 6rpx 16rpx;
          border-radius: 20rpx;
          font-weight: 500;
        }
      }
    }

    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
      line-height: 1.4;
    }

    .desc {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      background: #f8f9fc;
      padding: 20rpx;
      border-radius: 16rpx;
    }

    // 金卡价格样式
    .gold-card-price {
      display: flex;
      align-items: center;
      position: relative;
      padding: 8rpx 20rpx;
      background: linear-gradient(90deg, #FDF6E1 0%, rgba(250, 235, 192, 0.2) 97%);
      border-radius: 15rpx;
      margin-top: 20rpx;

      .gold-icon {
        color: #EA8F06;
      }

      .gold-card-badge {
        padding: 3rpx 10rpx;
        background: linear-gradient(90deg, #FF5F00 0%, #EA3202 99%);
        color: #fff;
        border-radius: 20rpx;
        font-size: 25rpx;
        margin: 0 8rpx;
      }

      .gold-card-image {
        height: 100rpx;
        position: absolute;
        left: 0;
        top: -50%;
        transform: translateY(30%);
      }

      .gold-card-amount {
        color: #EA1306;
        font-weight: 900;
        font-size: 50rpx;

        &.with-image {
          margin-left: 100rpx;
        }
      }
    }

    // 分隔线
    .divider {
      width: 100%;
      border-bottom: 1px solid #f5f5f5;
    }
  }

  .info-blocks {
    margin: 0 20rpx 5rpx;
    background: #fff;
    border-radius: 15rpx;
    padding: 30rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.03);
    .info-block {
      flex: 1;
      text-align: center;
      position: relative;
      padding: 20rpx 0;

      &:not(:last-child)::after {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        height: 60%;
        width: 1px;
        background: #eee;
      }

      .block-title {
        font-size: 26rpx;
        color: #999;
        margin-bottom: 12rpx;
      }

      .block-content {
        font-size: 28rpx;
        color: #333;
        line-height: 1.4;
        font-weight: 500;
        display: -webkit-box;
        -webkit-box-orient: vertical;    
        -webkit-line-clamp: 3;           
        overflow: hidden;                 
        text-overflow: ellipsis;   
      }
    }

    &:active {
      opacity: 0.8;
    }

    // &::after {
    //   content: "\e6a3";
    //   font-family: "iconfont";
    //   position: absolute;
    //   right: 30rpx;
    //   top: 50%;
    //   transform: translateY(-50%);
    //   color: #999;
    //   font-size: 28rpx;
    // }
  }
  .line {
    width: 6rpx;
    height: 26rpx;
    border-radius: 4rpx;
    background: #ea1306;
    margin-right: 8rpx;
  }
  .title {
    display: flex;
    align-items: center;
    margin-right: 20rpx;
    font-weight: 700;
  }
  .store-info {
    background: #fff;
    margin: 0 20rpx 5rpx;
    border-radius: 15rpx;
    padding: 10rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.03);
    .store-header {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;
      position: relative;
      padding-left: 20rpx;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 32rpx;
        background: #ea1306;
        border-radius: 3rpx;
      }

      .store-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .store-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx;
      border-radius: 16rpx;

      &:active {
        opacity: 0.8;
      }

      .store-left {
        flex: 1;
        padding-right: 20rpx;
        display: flex;
        align-items: center;
        .store-name {
          font-size: 28rpx;
          color: #333;
          font-weight: 700;
          margin-bottom: 12rpx;
        }

        .store-address {
          width: 360rpx;
          font-size: 24rpx;
          color: #666;
          line-height: 1.4;
        }
      }

      .store-right {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 24rpx;
        .iconfont {
          font-size: 50rpx;
          color: #fc9914;
          margin-bottom: 10rpx;
        }
      }
    }
  }

  .review-section {
    margin: 0 20rpx;
    background: #fff;
    border-radius: 24rpx;
    padding: 30rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.03);

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;
      position: relative;
      padding-left: 20rpx;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 32rpx;
        background: #ea1306;
        border-radius: 3rpx;
      }

      font-size: 32rpx;
      font-weight: bold;
      color: #333;

      .review-count {
        color: #999;
        font-size: 26rpx;
        margin-left: 12rpx;
        font-weight: normal;
      }
    }

    .rating-overview {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;
      padding: 24rpx;
      background: linear-gradient(135deg, #fff8e6 0%, #fff5e5 100%);
      border-radius: 20rpx;

      .score {
        margin-right: 40rpx;
        display: flex;
        align-items: baseline;

        .number {
          font-size: 64rpx;
          font-weight: bold;
          color: #ffb800;
          font-family: DIN;
          text-shadow: 0 2rpx 4rpx rgba(255, 184, 0, 0.2);
        }

        .total {
          font-size: 26rpx;
          color: #ffb800;
          margin-left: 8rpx;
          opacity: 0.8;
        }
      }

      .stars {
        display: flex;
        align-items: center;

        .iconfont {
          font-size: 44rpx;
          color: #ddd;
          margin-right: 8rpx;
          transition: all 0.3s;

          &.active {
            color: #ffb800;
            transform: scale(1.1);
          }
        }
      }
    }

    .tag-list {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      margin-bottom: 30rpx;

      .tag-item {
        padding: 12rpx 24rpx;
        border-radius: 30rpx;
        font-size: 26rpx;
        color: #666;
        background: #f8f9fc;
        transition: all 0.3s;

        &.active {
          color: #ea1306;
          background: rgba(234, 19, 6, 0.05);
          font-weight: 500;
          transform: scale(1.02);
        }
      }
    }

    .review-list {
      .review-item {
        padding: 24rpx 0;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        .user-info {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;

          .avatar {
            width: 72rpx;
            height: 72rpx;
            border-radius: 50%;
            margin-right: 16rpx;
            border: 2rpx solid #f0f0f0;
          }

          .right {
            flex: 1;

            .nickname {
              font-size: 28rpx;
              color: #333;
              margin-bottom: 8rpx;
              font-weight: 500;
            }

            .stars {
              display: flex;
              align-items: center;

              .iconfont {
                font-size: 28rpx;
                color: #ddd;
                margin-right: 4rpx;

                &.active {
                  color: #ffb800;
                }
              }

              .score-text {
                margin-left: 12rpx;
                color: #ffb800;
                font-size: 26rpx;
                font-weight: 500;
              }
            }
          }
        }

        .review-content {
          font-size: 28rpx;
          color: #333;
          line-height: 1.6;
          margin-bottom: 20rpx;
          background: #f8f9fc;
          padding: 20rpx;
          border-radius: 16rpx;
        }

        .review-images {
          display: flex;
          gap: 16rpx;
          margin-bottom: 20rpx;

          image {
            width: 180rpx;
            height: 180rpx;
            border-radius: 16rpx;
            box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
          }
        }

        .review-time {
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .more-reviews {
      margin-top: 30rpx;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #666;
      background: #f8f9fc;
      border-radius: 44rpx;
      transition: all 0.3s;

      &:active {
        transform: scale(0.98);
      }

      .iconfont {
        margin-left: 8rpx;
        font-size: 24rpx;
      }
    }
  }

  .bottom-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 16rpx 30rpx calc(16rpx + env(safe-area-inset-bottom));
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);

    .left {
      display: flex;
      gap: 50rpx;

      .icon-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 22rpx;
        color: #666;
        transition: all 0.3s;

        &:active {
          transform: scale(0.9);
        }

        .iconfont {
          font-size: 44rpx;
          margin-bottom: 6rpx;
        }
      }
    }

    .right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      gap: 24rpx;

      .add-cart,
      .buy-now,
      .seckill-btn {
        padding: 0 44rpx;
        height: 88rpx;
        line-height: 88rpx;
        border-radius: 44rpx;
        font-size: 30rpx;
        font-weight: 500;
        transition: all 0.3s;

        &:active {
          transform: scale(0.98);
        }

        &.disabled {
          background-color: #ccc;
          pointer-events: none;
          opacity: 0.7;
        }
      }

      .add-cart {
        background: rgba(234, 19, 6, 0.1);
        color: #ea1306;

        &.disabled {
          background: rgba(204, 204, 204, 0.1);
          color: #999;
        }
      }

      .buy-now,
      .seckill-btn {
        background-color:  #ea1306;
        // background: linear-gradient(135deg, #ff6b6b 0%, #ea1306 100%);
        color: #fff;
        box-shadow: 0 8rpx 16rpx rgba(234, 19, 6, 0.2);
      }
    }
  }
}

.detail-content {
  padding: 20rpx;
  background-color: #fff;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }

  .rich-text {
    width: 100%;

    :deep(img) {
      max-width: 100% !important;
      height: auto !important;
    }

    :deep(video) {
      width: 100% !important;
      height: auto !important;
    }
  }
}

.function-popup {
  background-color: #fff;
  border-radius: 16px 16px 0 0;
  padding: 20px;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .title {
      font-size: 16px;
      font-weight: bold;
    }

    .close {
      font-size: 24px;
      color: #999;
      padding: 5px;
    }
  }

  .popup-content {
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    padding-bottom: 20px;
  }
}
.medication-guide-popup {
  background: #fff;
  min-height: 60vh;
  max-height: 80vh;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;

  .popup-title {
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    padding: 30rpx 0;
    position: relative;
    border-bottom: 1rpx solid #f5f5f5;

    .close-btn {
      position: absolute;
      right: 30rpx;
      top: 50%;
      transform: translateY(-50%);
      font-size: 40rpx;
      color: #999;
      font-weight: normal;
      padding: 20rpx;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .popup-content {
    padding: 30rpx;
  }

  .guide-item {
    display: flex;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .item-title {
      width: 180rpx;
      font-size: 28rpx;
      color: #999;
      flex-shrink: 0;
    }

    .item-content {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
      padding-left: 20rpx;

      &:empty::after {
        content: "暂无信息";
        color: #999;
      }
    }
  }
}
/* 修改分享按钮样式 */
.title-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  // margin-bottom: 20rpx;

  .title {
    flex: 1;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    line-height: 1.4;
    margin-top:20rpx;
    margin-right: 20rpx;
  }
}
.share-button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  line-height: 1;
  color: #666;
  font-size: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80rpx;
  position: absolute;
  right: 20rpx;
  top: 50rpx;
  &::after {
    border: none;
  }

  .iconfont {
    font-size: 40rpx;
    margin-bottom: 4rpx;
  }
}

// 添加分享选项弹窗样式
.share-options {
  background-color: #fff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  overflow: hidden;
  
  .share-title {
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    border-bottom: 1px solid #f5f5f5;
  }
  
  .share-content {
    display: flex;
    padding: 40rpx 60rpx;
    
    .share-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: none;
      padding: 0;
      line-height: normal;
      
      &::after {
        border: none;
      }
      
      .share-icon {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
        background-color: #f8f8f8;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20rpx;
        
        .iconfont {
          font-size: 50rpx;
          color: #333;
        }
      }
      
      .share-text {
        font-size: 28rpx;
        color: #666;
      }
    }
  }
  
  .share-cancel {
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    font-size: 32rpx;
    color: #333;
    border-top: 10rpx solid #f5f5f5;
  }
}
