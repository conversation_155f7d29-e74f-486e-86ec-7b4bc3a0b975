<template>
    <view class="feedback">
        <!-- 顶部标题 -->
        <view class="feedback-header">
            <text class="title">集和堂小程序商城功能问题反馈</text>
            <text class="desc">亲爱的集和堂用户：为了提供更好的服务体验，在小程序使用过程中存在任何问题或建议，都可以通过此问卷反馈。反馈的问题会被逐一记录，感谢您的支持！</text>
        </view>

        <!-- 问题类型选择 -->
        <view class="feedback-section">
            <view class="section-title">
                <text class="required">*</text>
                <text>01 请选择您要反馈的问题类型(多选项)</text>
            </view>
            <checkbox-group class="checkbox-list" @change="handleTypeChange">
                <label class="checkbox-item" v-for="(item, index) in problemTypes" :key="index">
                    <checkbox :value="item.value" color="#FF0000" />
                    <text>{{ item.label }}</text>
                </label>
            </checkbox-group>
            <text class="error-text" v-if="errors.types">{{ errors.types }}</text>
        </view>

        <!-- 问题描述 -->
        <view class="feedback-section">
            <view class="section-title">
                <text class="required">*</text>
                <text>02 问题/建议描述</text>
            </view>
            <textarea class="feedback-textarea" 
                v-model="formData.description"
                placeholder="请描述具体建议/问题发生过程，越详细越能高效处理哦~"
                placeholder-class="placeholder">
            </textarea>
            <text class="error-text" v-if="errors.description">{{ errors.description }}</text>
        </view>

        <!-- 图片上传 -->
        <view class="feedback-section">
            <view class="section-title">
                <text>03 请将您遇到的问题图片上传</text>
            </view>
            <text class="upload-tip">有效的图片信息有助于定位解决问题哦~</text>
            <uni-file-picker v-model="formData.images" fileMediatype="image" mode="grid" :limit="4" @select="select" />
        </view>

        <!-- 联系方式 -->
        <view class="feedback-section">
            <view class="section-title">
                <text class="required">*</text>
                <text>04 方便留下您的联系方式:</text>
            </view>
            <text class="contact-tip">请留下您的联系方式，以便我们能够方便了解问题反馈及您反馈问题结果。紧急问题可拨打客服电话，获得及时帮助。</text>
            <input class="contact-input" placeholder="姓名" v-model="formData.name" />
            <text class="error-text" v-if="errors.name">{{ errors.name }}</text>
            <input class="contact-input" maxlength="11" placeholder="手机号" v-model="formData.phone" />
            <text class="error-text" v-if="errors.phone">{{ errors.phone }}</text>
        </view>

        <!-- 底部提示 -->
        <view class="feedback-footer">
            <text>本次问题反馈用于小程序功能问题收集(无专人回复)</text>
            <text>若您的问题亟待处理，请联系集和堂小程序客服~</text>
            <text>感谢您的反馈，祝您生活愉快！</text>
        </view>

        <!-- 提交按钮 -->
        <button class="submit-btn" @click="handleSubmit">提交</button>
    </view>
</template>

<script>
export default {
    data() {
        return {
            problemTypes: [
                { label: 'App功能 问题/建议', value: 'app' },
                { label: '地址定位 问题/建议', value: 'location' },
                { label: '新人福利 问题/建议', value: 'newuser' },
                { label: '商品相关 问题/建议', value: 'product' },
                { label: '配送时间 问题/建议', value: 'delivery' },
                { label: '拆货打包 问题/建议', value: 'package' },
                { label: '客服服务 问题/建议', value: 'service' },
                { label: '新增配送点', value: 'newpoint' },
            ],
            formData: {
                types: [],
                description: '',
                images: [],
                name: '',
                phone: ''
            },
            errors: {
                types: '',
                description: '',
                name: '',
                phone: ''
            }
        }
    },
    methods: {
        // 处理问题类型选择
        handleTypeChange(e) {
            this.formData.types = e.detail.value;
            this.errors.types = '';
        },

        // 处理图片选择
        select(e) {
            console.log('选择文件：', e);
        },

        // 验证表单
        validateForm() {
            let isValid = true;
            this.errors = {
                types: '',
                description: '',
                name: '',
                phone: ''
            };

            // 验证问题类型
            if (this.formData.types.length === 0) {
                this.errors.types = '请至少选择一个问题类型';
                isValid = false;
            }

            // 验证问题描述
            if (!this.formData.description.trim()) {
                this.errors.description = '请填写问题描述';
                isValid = false;
            }

            // 验证姓名
            if (!this.formData.name.trim()) {
                this.errors.name = '请填写姓名';
                isValid = false;
            }

            // 验证手机号
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(this.formData.phone)) {
                this.errors.phone = '请填写正确的手机号';
                isValid = false;
            }

            return isValid;
        },

        // 处理表单提交
        handleSubmit() {
            if (!this.validateForm()) {
                uni.showToast({
                    title: '请完善表单信息',
                    icon: 'none'
                });
                return;
            }

            // 模拟提交
            uni.showLoading({
                title: '提交中...'
            });

            // 这里添加实际的提交逻辑
            setTimeout(() => {
                uni.hideLoading();
                uni.showToast({
                    title: '提交成功',
                    icon: 'success',
                    duration: 2000,
                    success: () => {
                        // 延迟返回上一页
                        setTimeout(() => {
                            uni.navigateBack();
                        }, 2000);
                    }
                });
            }, 1500);
        }
    }
}
</script>

<style lang="scss" scoped>
.feedback {
    padding: 20rpx;
    background-color: #F5F5F5;
    min-height: 100vh;
    padding-bottom: 100rpx;

    .feedback-header {
        .title {
            font-size: 32rpx;
            font-weight: bold;
            margin-bottom: 20rpx;
            display: block;
            text-align: center;
        }

        .desc {
            font-size: 28rpx;
            color: #666;
            line-height: 1.5;
        }
    }

    .feedback-section {
        margin-top: 40rpx;
        background-color: #FFFFFF;
        padding: 20rpx;
        border-radius: 10rpx;

        .section-title {
            font-size: 30rpx;
            margin-bottom: 20rpx;

            .required {
                color: #FF0000;
                margin-right: 10rpx;
            }
        }
    }

    .checkbox-list {
        display: flex;
        flex-direction: column;
        gap: 20rpx;

        .checkbox-item {
            font-size: 28rpx;
        }
    }

    .feedback-textarea {
        width: 100%;
        height: 200rpx;
        background-color: #F8F8F8;
        padding: 20rpx;
        font-size: 28rpx;
    }

    .upload-tip,
    .contact-tip {
        font-size: 26rpx;
        color: #999;
        margin-bottom: 30rpx;
    }

    .contact-input {
        height: 80rpx;
        background-color: #F8F8F8;
        margin-bottom: 20rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
    }

    .feedback-footer {
        margin: 40rpx 0;
        text-align: center;

        text {
            display: block;
            font-size: 26rpx;
            color: #999;
            line-height: 1.5;
        }
    }

    .submit-btn {
        position: fixed;
        bottom: 40rpx;
        left: 20rpx;
        right: 20rpx;
        height: 88rpx;
        z-index: 2;
        line-height: 88rpx;
        background-color: #FF0000;
        color: #FFFFFF;
        border-radius: 44rpx;
        font-size: 32rpx;
    }
}

.placeholder {
    color: #999;
    font-size: 28rpx;
}

.error-text {
    color: #FF0000;
    font-size: 24rpx;
    margin-top: 10rpx;
}
</style>