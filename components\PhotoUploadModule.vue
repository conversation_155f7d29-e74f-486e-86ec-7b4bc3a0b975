<template>
	<view class="photo-upload">
		<view class="photo-title">{{ title }}</view>
		<view class="photo-container">
			<view v-if="referenceImage" class="photo-left">
				<text class="section-title">参考图</text>
				<image :src="referenceImage" mode="aspectFill" class="reference-image"></image>
			</view>
			<view class="photo-right">
				<text class="section-title">上传区</text>
				<view class="upload-content">
					<uni-file-picker v-model="fileList" :limit="limit" :image-styles="imageStyles"
						@select="handleSelect" @delete="handleDelete"></uni-file-picker>
				</view>
			</view>
		</view>
		<view class="remark-input">
			<textarea v-model="remark" :placeholder="`请输入${title}建议`" @input="handleRemarkChange"></textarea>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PhotoUploadModule',
	props: {
		// 模块标题
		title: {
			type: String,
			required: true
		},
		// 参考图片URL
		referenceImage: {
			type: String,
			default: ''
		},
		// 已上传的图片列表
		value: {
			type: Array,
			default: () => []
		},
		// 最大上传数量
		limit: {
			type: Number,
			default: 4
		},
		// 图片样式
		imageStyles: {
			type: Object,
			default: () => ({
				width: 100,
				height: 100,
				border: {
					radius: '8rpx'
				}
			})
		},
		// 建议内容
		remarkValue: {
			type: String,
			default: ''
		},
		// 建议字段名
		remarkField: {
			type: String,
			required: true
		}
	},
	data() {
		return {
			fileList: [],
			remark: ''
		}
	},
	watch: {
		value: {
			handler(newVal) {
				this.fileList = newVal;
			},
			immediate: true
		},
		remarkValue: {
			handler(newVal) {
				this.remark = newVal;
			},
			immediate: true
		}
	},
	methods: {
		// 选择文件
		handleSelect(e) {
			this.$emit('select', e);
		},
		// 删除文件
		handleDelete(e) {
			this.$emit('delete', e);
		},
		// 建议内容变化
		handleRemarkChange(e) {
			this.$emit('remark-change', {
				field: this.remarkField,
				value: this.remark
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.photo-upload {
	margin-bottom: 20rpx;
	background: #fff;
	padding: 20rpx;
	border-radius: 12rpx;

	.photo-title {
		font-size: 32rpx;
		color: #333;
		margin-bottom: 20rpx;
		font-weight: bold;
	}

	.photo-container {
		display: flex;
		gap: 20rpx;

		.section-title {
			font-size: 28rpx;
			color: #666;
			margin-bottom: 16rpx;
			display: block;
		}

		.photo-left {
			.reference-image {
				width: 200rpx;
				height: 200rpx;
				border-radius: 8rpx;
				background-color: #f8f8f8;
				object-fit: cover;
			}
		}

		.photo-right {
			flex: 1;

			.upload-content {
				:deep(.uni-file-picker) {
					.uni-file-picker__container {
						display: flex;
						flex-wrap: wrap;
						gap: 16rpx;
					}

					.file-picker__box {
						width: 200rpx !important;
						height: 200rpx !important;
						margin: 0 !important;

						.uni-file-picker__box-content {
							width: 100% !important;
							height: 100% !important;
						}
					}

					.is-add {
						border: 2rpx dashed #ddd !important;
						background-color: #fff !important;

						&:active {
							opacity: 0.8;
						}
					}

					.file-picker__progress {
						width: 200rpx !important;
						height: 200rpx !important;
					}

					.file-image {
						width: 200rpx !important;
						height: 200rpx !important;
						margin: 0 !important;
						border-radius: 8rpx;

						image {
							border-radius: 8rpx;
						}
					}

					.icon-del {
						top: -16rpx !important;
						right: -16rpx !important;
						background-color: rgba(0, 0, 0, 0.6) !important;
						border-radius: 50% !important;
						width: 36rpx !important;
						height: 36rpx !important;
						z-index: 1;

						&::before,
						&::after {
							background-color: #fff !important;
						}
					}
				}
			}
		}
	}
}

.remark-input {
	margin-top: 20rpx;
	
	textarea {
		width: 100%;
		height: 120rpx;
		padding: 20rpx;
		box-sizing: border-box;
		border: 2rpx solid #EEEEEE;
		border-radius: 8rpx;
		font-size: 28rpx;
		line-height: 1.5;
	}
}
</style> 