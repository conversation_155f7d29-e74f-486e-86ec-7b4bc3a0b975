<template>
  <view class="store-list">
    <!-- 当前选中门店 -->
    <view class="current-store" v-if="currentStore">
      <view class="store-title">当前选中门店</view>
      <view class="store-content">
        <image class="store-logo"
          src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241119/001451741aa0494f98a3c69511a397a3.png"
          mode="aspectFit">
        </image>
        <view class="store-detail">
          <view class="store-name">{{ currentStore.storeName }}</view>
          <view class="store-address">{{ currentStore.address }}</view>
        </view>
      </view>
    </view>

    <!-- 搜索框 -->
    <view class="search-box">
      <view class="search-input">
        <uni-icons type="search" size="16" color="#999"></uni-icons>
        <input type="text" v-model="keyword" placeholder="请输入门店名称或地址" @input="handleSearch" />
      </view>
    </view>

    <!-- 定位信息 -->
    <view class="location-info">
      <view class="location-title">
        <text class="iconfont">&#xe682;</text>
        <text>当前定位</text>
      </view>
      <view class="location-address" @click="refreshLocation">
        {{ address || '定位中...' }}
        <text class="refresh">重新定位</text>
      </view>
    </view>

    <!-- 门店列表 -->
    <view class="store-container">
      <view class="store-item" v-for="(item, index) in storeList" :key="index" @click="selectStore(item)"
        :class="{ 'active-store': item.id === currentStoreId }">
        <image class="store-image"
          src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241119/001451741aa0494f98a3c69511a397a3.png"
          mode="aspectFit"></image>
        <view class="store-info">
          <view class="store-name">{{ item.storeName }}</view>
          <view class="store-address">{{ item.address }}</view>
          <!-- <view class="store-distance" v-if="item.mDistance">
            <text class="iconfont">&#xe682;</text>
            <text>{{ formatDistance(item.mDistance) }}</text>
          </view> -->
        </view>
        <view class="selected-tag" v-if="item.id === currentStoreId">
          <text class="iconfont">&#xe608;</text>
          <text>当前选择</text>
        </view>
      </view>
    </view>

    <!-- 无数据提示 -->
    <view class="no-data" v-if="storeList.length === 0">
      <text>暂无门店数据</text>
    </view>
  </view>
</template>

<script>
const req = require('../../utils/request')
export default {
  data() {
    return {
      keyword: '',
      address: '',
      latitude: '',
      longitude: '',
      storeList: [],
      currentStoreId: '',
      pageNum: 1,
      pageSize: 10,
      hasMore: true,
      searchTimer: null,
      currentStore: null,
    }
  },
  onLoad() {
    this.currentStoreId = req.getStorage('currentStore').id || ''
    // this.getLocation()
    this.getStoreList()
    this.currentStore = req.getStorage("currentStore")
  },
  onReachBottom() {
    if (this.hasMore) {
      this.pageNum++
      this.getStoreList()
    }
  },
  methods: {
    // 获取定位
    getLocation() {
      req.getLocationInfo(res => {
        console.log(res);
        this.latitude = res.location.lat
        this.longitude = res.location.lng
        this.address = res.address
        this.getStoreList()
      })
    },
    // 刷新定位
    refreshLocation() {
      this.getLocation()
    },
    // 获取门店列表
    getStoreList() {

      req.getRequest('/shopApi/home/<USER>', {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        lat: this.latitude,
        lng: this.longitude,
        storeName: this.keyword
      }, res => {

        this.storeList = [...this.storeList, ...res.data.list]
        this.hasMore = this.storeList.length < res.data.total
      })
    },
    // 搜索
    handleSearch() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }

      this.searchTimer = setTimeout(() => {
        this.pageNum = 1
        this.storeList = []
        this.getStoreList()
      }, 500)
    },
    // 选择门店
    selectStore(store) {
      req.setStorage("currentStore", store)

      uni.showToast({
        title: '选择成功',
        icon: 'success',
        duration: 1500,
        success: () => {
          setTimeout(() => {
            let pages = getCurrentPages();
            var prevPage = pages[pages.length - 2];
            prevPage.$vm.store = store;
            if (prevPage.$vm.createByShop && prevPage.$vm.mode == 1 && prevPage.$vm.tabValue == 3) {
              //及时达美团校验
              prevPage.$vm.createByShop()
            }
            uni.navigateBack()
          }, 1500)
        }
      })
    },
    // 格式化距离显示
    formatDistance(distance) {
      if (!distance) return '';
      if (distance >= 1000) {
        return (distance / 1000).toFixed(1) + 'km';
      }
      return Math.floor(distance) + 'm';
    },
  }
}
</script>

<style lang="scss" scoped>
@import './storeList.scss';
</style>