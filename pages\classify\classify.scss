.classify {
  background-color: #f9f9f9;
  .header {
    background-color: #ffffff;

    .search {
      border: 2rpx solid #ea1306;
      height: 70rpx;
      margin: 20rpx;
      border-radius: 50rpx;
      padding: 0 70rpx;
      font-size: 28rpx;
      line-height: 70rpx;
      color: #666;
    }

    .search_icon {
      width: 30rpx;
      height: 30rpx;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }

    .tab_ {
      display: flex;
      align-items: center;
      background: #f5f6f7;
      padding: 4rpx;
      border-radius: 40rpx;
      width: 80%;
      margin: 20rpx auto;

      .tab_item {
        flex: 1;
        padding: 12rpx 0;
        text-align: center;
        font-size: 28rpx;
        color: #666;
        border-radius: 36rpx;
        transition: all 0.3s ease;
        position: relative;
        margin: 0 4rpx;

        &.tab_active {
          color: #fff;
          background: linear-gradient(90deg, #ea1306, #ff4b4b);
          box-shadow: 0 4rpx 8rpx rgba(234, 19, 6, 0.2);
        }
      }
    }
  }

  .body {
    padding: 0 0 0rpx 20rpx;
    background-color: #f9f9f9;
    display: flex;
    box-sizing: border-box;

    .left_tab {
      width: 166rpx;
      height: calc(100vh - 230rpx);
      background-color: #fff;
      margin-right: 12rpx;
      padding-bottom: 41rpx;

      .left_item {
        width: 100%;
        height: 64rpx;
        line-height: 64rpx;
        text-align: center;
        font-family: PingFang SC;
        font-size: 28rpx;
        font-weight: normal;
        color: #4e5969;
        margin-bottom: 40rpx;
      }

      .left_item_active {
        background: #f9e9e8;
        border-left: 6rpx solid #ea1306;
      }

      .card_ {
        position: relative;
        width: 64rpx;
        height: 64rpx;
        background: rgba(237, 243, 250, 0.8);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 20rpx;
        .cornermark {
          top: 10rpx;
          right: 10rpx;
          height: 12rpx;
          min-width: 12rpx;
          line-height: 12rpx;
          text-align: center;
          position: absolute;
          padding: 4rpx;
          font-family: Source Han Sans;
          font-size: 6px;
          font-weight: 500;
          line-height: 6px;
          color: #ffffff;
          background: #ea1306;
          border-radius: 50%;
        }
      }
    }

    .right_tab {
      width: 578rpx;

      .tc_view {
        display: flex;
        flex-wrap: wrap;

        .tc_item {
          width: 25%;
          text-align: center;
          margin-top: 16rpx;
          font-family: PingFang SC;
          font-size: 28rpx;
          color: #333333;
        }

        .tc_item_active {
          color: #ea1306;
        }
      }
      .right_bottom {
        box-sizing: border-box;
        background-color: #fff;
        // height: calc(100vh - 252px - env(safe-area-inset-bottom));
        border-radius: 20rpx 20rpx 0 0rpx;
        .swiper-box {
          border-radius: 20rpx;
          height: 238rpx;
        }
      }
      .goods_view {
        background-color: #fff;

        .restoratives {
          padding: 30rpx 0;
          box-sizing: border-box;
          .res_item {
            border-bottom: 1rpx solid #f2f3f5;
            padding: 40rpx 0;
            display: flex;
            .item_img {
              position: relative;
              width: 156rpx;
              height: 156rpx;
              background: #edf3fa;
              border-radius: 8rpx;
              // overflow: hidden;
              margin-right: 10rpx;

              .tag {
                position: absolute;
                left: 0;
                top: -6rpx;
                width: 60rpx;
                height: 28rpx;
                border-radius: 8rpx 0rpx 8rpx 0rpx;
                background: #0b78ff;
                font-family: Source Han Sans;
                font-size: 20rpx;
                text-align: center;
                line-height: 28rpx;
                color: #ffffff;
              }
            }
            .title {
              font-family: Source Han Sans;
              font-size: 30rpx;
              font-weight: 700;
              line-height: normal;
              color: #4e5969;
            }
            .red_view {
              width: 40rpx;
              height: 24rpx;
              text-align: center;
              line-height: 24rpx;
              font-size: 16rpx;
              display: inline-block;
            }
            .text_pay {
              font-family: Alibaba Sans;
              font-size: 30rpx;
              font-weight: 700;
              line-height: normal;
              color: #bfbfbf;
            }
            .volume {
              width: 356rpx;
              height: 44rpx;
              border-radius: 8rpx;
              opacity: 1;
              padding: 4rpx 8rpx;
              box-sizing: border-box;
              background: linear-gradient(
                90deg,
                #fdf6e1 0%,
                rgba(250, 235, 192, 0.2) 97%
              );
              display: flex;
              align-items: center;
              .text_ {
                width: 90%;
                font-family: Alibaba Sans;
                font-size: 20rpx;
                font-weight: normal;
                line-height: 36rpx;
                /* 儿童专区热销榜 */
                color: #3d3d3d;
                margin-left: 10rpx;
                display: flex;
                justify-content: space-between;
              }
            }
          }
        }
        .restoratives:first-child {
          padding-top: 0;
        }
        .restoratives:last-child {
          padding-bottom: 0;
        }
      }
    }
  }

  .screen_tag {
    display: flex;
    font-size: 28rpx;
    color: #4e5969;
    justify-content: flex-end;
    margin-bottom: 20rpx;
  }
  .screen_radio {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    .text_radio {
      font-family: Alibaba Sans;
      font-size: 28rpx;
      font-weight: normal;
      line-height: normal;
      color: #4e5969;
      width: 136rpx;
      height: 40rpx;
      padding: 10rpx;
      border-radius: 8rpx;
      opacity: 1;
      background: rgba(237, 243, 250, 0.5);
      text-align: center;
      line-height: 40rpx;
      margin-right: 10rpx;
      margin-bottom: 10rpx;
    }
    .text_radio_active {
      background: #f9e9e8;
      color: #ea1306;
      font-weight: 700;
    }
  }
  .classify_tag {
    width: 540rpx;
    height: 56rpx;
    border-radius: 8rpx;
    // background: rgba(237, 243, 250, 0.5);
    background: #f9e9e8;
    font-family: Alibaba Sans;
    font-size: 28rpx;
    line-height: 56rpx;
    font-weight: normal;
    color: #ea1306;
    padding-left: 24rpx;
    box-sizing: border-box;
  }
  // /deep/.vue-ref {
  //   right: 0 !important;
  //   top: -100rpx !important;
  // left: auto !important;
  // left: inherit !important;
  // }
  ::-webkit-scrollbar {
    width: 0;
    height: 0;
    color: transparent;
    display: none;
  }
}
