<template>
    <view class="record-container">
        <!-- Tab栏 -->
        <view class="tab-bar">
            <view 
                v-for="(tab, index) in tabs" 
                :key="index"
                class="tab-item"
                :class="{ active: currentTab === index }"
                @tap="switchTab(index)"
            >
                {{ tab }}
            </view>
        </view>
        <!-- 记录列表 -->
        <view v-if="records.length > 0">
            <view class="record-item" v-for="(item, index) in records" :key="index">
                <view class="left">
                    <view class="type">{{ item.remark }}</view>
                    <view class="time">{{ item.createDate }}</view>
                </view>
                <view class="right">
                    <text class="amount">{{currentTab === 0 ? '+' : ''}}{{ item.changeMoney }}</text>
                    <!-- <text class="status" :class="item.status === 1 ? 'success' : 'fail'">
                        {{ item.status === 1 ? '充值成功' : '充值失败' }}
                    </text> -->
                </view>
            </view>
            <!-- 加载更多提示 -->
            <view class="load-more" v-if="hasMore">加载中...</view>
            <view class="load-more" v-else>没有更多数据了</view>
        </view>
        <!-- 空状态 -->
        <view v-else class="empty-state">
            <text>{{ currentTab === 0 ? '暂无充值记录' : '暂无使用记录' }}</text>
        </view>
    </view>
</template>

<script>
const req = require("../../utils/request")

export default {
    data() {
        return {
            page: 1,
            records: [],
            hasMore: true,
            isRefreshing: false,
            tabs: ['充值明细', '使用明细'],
            currentTab: 0
        }
    },
    onLoad() {
        this.getData()
    },
    // 添加上拉加载
    onReachBottom() {
        if (this.hasMore && !this.isRefreshing) {
            this.page++
            if (this.currentTab === 0) {
                this.getData()
            } else {
                this.getCardData()
            }
        }
    },
    methods: {
        switchTab(index) {
            if (this.currentTab === index) return
            this.currentTab = index
            this.page = 1
            this.records = [];
            this.hasMore = true
            if (index === 0) {
                this.getData()
            } else {
                this.getCardData()
            }
        },
        getData(callback) {
            console.log(this.currentTab);
            req.getRequest("/shopApi/user/balance/page", {
                pageNum: this.page,
                pageSize: 10,
                logType: 5
            }, res => {
                const { items, total } = res.data
                // 处理数据
                if (this.page === 1) {
                    this.records = items
                } else {
                    this.records = [...this.records, ...items]
                }

                // 判断是否还有更多数据
                this.hasMore = this.records.length < total

                // // 执行回调函数
                callback && callback()
            })
        },
        getCardData() {
            req.getRequest("/shopApi/user/balanceRecords", {
                pageNum: this.page,
                pageSize: 10
            }, res => {
                console.log(res);
                const { list, total } = res.data
                if (this.page === 1) {
                    this.records = list
                } else {
                    this.records = [...this.records, ...list]
                }

                // 判断是否还有更多数据
                this.hasMore = this.records.length < total

                // // 执行回调函数
                // callback && callback()
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.record-container {
    min-height: 100vh;
    background: #f5f5f5;
    padding: 20rpx;

    .tab-bar {
        display: flex;
        background: #fff;
        border-radius: 12rpx;
        margin-bottom: 20rpx;
        padding: 20rpx 0;

        .tab-item {
            flex: 1;
            text-align: center;
            font-size: 28rpx;
            color: #666;
            position: relative;

            &.active {
                color: #FF6B6B;
                font-weight: bold;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: -20rpx;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 40rpx;
                    height: 4rpx;
                    background: #FF6B6B;
                    border-radius: 2rpx;
                }
            }
        }
    }

    .record-list {
        height: calc(100vh - 40rpx);
    }

    .record-item {
        background: #fff;
        border-radius: 12rpx;
        padding: 30rpx;
        margin-bottom: 20rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
            .type {
                font-size: 32rpx;
                color: #333;
                margin-bottom: 10rpx;
            }

            .time {
                font-size: 24rpx;
                color: #999;
            }
        }

        .right {
            text-align: right;

            .amount {
                display: block;
                font-size: 36rpx;
                color: #FF6B6B;
                font-weight: bold;
                margin-bottom: 10rpx;
            }

            .status {
                font-size: 24rpx;

                &.success {
                    color: #52c41a;
                }

                &.fail {
                    color: #ff4d4f;
                }
            }
        }
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 200rpx;

        image {
            width: 200rpx;
            height: 200rpx;
            margin-bottom: 20rpx;
        }

        text {
            font-size: 28rpx;
            color: #999;
        }
    }

    .load-more {
        text-align: center;
        font-size: 24rpx;
        color: #999;
        padding: 20rpx 0;
    }
}
</style>