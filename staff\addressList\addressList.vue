<template>
	<view
		style="margin:20rpx;box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.04);padding-bottom:50rpx;background: #fff;border-radius: 10rpx;">
		<view v-for="(item, index) in addressList" :key="index"
			style="background: #fff;padding:40rpx 30rpx;border-bottom:1px solid #F2F3F5">
			<view @click="upIsDefault(item)"
				style="display: flex;align-items: center;justify-content: space-between;margin-bottom:15rpx;">
				<view style="display: flex;margin-right:20rpx;align-items: center;">
					<image
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240831/4a98f067f47e48a5bc75fd56cb246eb7.png"
						mode="widthFix" style="width:28rpx;margin-right:20rpx;"></image>
					<view style="flex:1;">{{ item.address }}{{ item.house }}</view>
				</view>
				<view @click.stop="goUrl(`/staff/addressDetail/addressDetail?id=${item.id}&userId=${userId}`)"
					style="width:150rpx;display: flex;justify-content: flex-end;">
					<image
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240831/b2c823e9fa8f403f9191182c34e657b6.png"
						style="width:35rpx;height:35rpx;"></image>
				</view>
			</view>
			<view style="display: flex;margin-left:40rpx;">
				<view v-if="item.isDefault == 1"
					style="padding:3rpx 8rpx;font-size:20rpx;border-radius: 8rpx;display: flex;align-items: center;background: #0B78FF;color:#fff;margin-right:15rpx;">
					默认</view>
				<view style="color:#666;margin-right:15rpx;">{{ item.name }}</view>
				<view style="color:#999;">{{ item.phone }}</view>
			</view>
		</view>
		<view class="submit" @click="goUrl(`/staff/addressDetail/addressDetail?userId=${userId}`)">新增收货地址</view>
	</view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
	data() {
		return {
			addressList: [],
			userId: "",
			choice: null,
		}
	},
	onLoad(options) {
		this.userId = options.userId
		this.choice = options.choice
	},
	onShow() {
		this.getData()
	},
	methods: {
		upIsDefault(item) {
			if (this.choice) {
				let pages = getCurrentPages();
				console.log(111, pages);
				var prevPage = pages[pages.length - 2];
				prevPage.$vm.setData({
					address: item,
					choice: true
				});
				uni.navigateBack();
			} // } 
		},
		goUrl(url) {
			uni.navigateTo({
				url: url
			});
		},
		getData() {
			req.getRequest("/shopApi/mp/address/list", {
				uid: this.userId,
				// page: 1,
				// limit: 99
			}, res => {
				console.log(res);
				this.addressList = res.data.list
			})
		},
	}
}
</script>

<style>
.submit {
	position: fixed;
	left: 10%;
	margin-top: 10px;
	bottom: 100rpx;
	width: 80%;
	height: 46px !important;
	line-height: 46px !important;
	background: #0b78ff;
	font-size: 36rpx;
	color: #fff;
	text-align: center;
	border-radius: 50rpx !important;
}
</style>
