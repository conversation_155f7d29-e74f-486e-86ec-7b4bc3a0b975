<template>
    <view>
        <view class="popup-mask" v-if="showPopup" @click="closePopup">
            <view class="popup-content" @click.stop>
                <view class="popup-body">
                    <image class="popup-logo"
                        src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20250812/8c02c7fb8fe04db7a7377a3e5df55be9.jpg"
                        mode="aspectFit"></image>
                    <text>即将跳转到集和堂互联网医院小程序</text>
                </view>
                <view class="popup-footer">
                    <view class="popup-btn confirm" @click="confirmJump">确认</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const req = require('../../utils/request')
export default {
    data() {
        return {
            showPopup: false,
            staffId: "",
            isOnline: "",
            type: ""
        }
    },
    onLoad(options) {
        this.isOnline = options.isOnline
        this.type = options.type
        this.staffId = options.uid
        this.showPopup = true
    },
    methods: {
        confirmJump() {
            // uni.navigateToMiniProgram({
            //     appId: 'wx017bcf0b1797fef4',
            //     path: `reserve/chat/chat?&isOnline=3&staffId=${this.staffId}&back=1`,
            //     envVersion: req.env.NODE_ENV == 'product' ? 'release' : 'trial',
            //     complete(res) {
            //         uni.switchTab({
            //             url: '/pages/index/index'
            //         })
            //     }
            // });
            uni.navigateToMiniProgram({
                appId: 'wx017bcf0b1797fef4',
                path: `pages/index/index`,
                envVersion: req.env.NODE_ENV == 'product' ? 'release' : 'trial',
                complete(res) {
                    uni.switchTab({
                        url: '/pages/index/index'
                    })
                }
            });
        },

    }
}
</script>

<style lang="scss" scoped>
.popup-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(17, 17, 17, 0.85);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.25s ease-out;
}

.popup-content {
    width: 580rpx;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 36rpx;
    overflow: hidden;
    box-shadow: 0 25rpx 50rpx -12rpx rgba(0, 0, 0, 0.15);
    animation: scaleIn 0.25s ease-out;
    transform-origin: center;
}

.popup-header {
    padding: 52rpx 40rpx 36rpx;
    text-align: center;
    position: relative;
}

.popup-logo {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 28rpx;
    border-radius: 40rpx;
    box-shadow: 0 12rpx 24rpx -8rpx rgba(236, 57, 57, 0.2);
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.popup-logo:active {
    transform: scale(0.95);
}

.popup-title {
    font-size: 38rpx;
    font-weight: 600;
    color: #111;
    letter-spacing: 1rpx;
    margin-bottom: 8rpx;
}

.popup-body {
    padding: 0 48rpx 48rpx;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 100rpx;
}

.popup-body text {
    font-size: 30rpx;
    color: #666;
    line-height: 1.6;
    display: block;
}

.popup-footer {
    display: flex;
    padding: 32rpx 48rpx 48rpx;
}

.popup-btn.confirm {
    flex: 1;
    height: 96rpx;
    line-height: 96rpx;
    text-align: center;
    font-size: 34rpx;
    font-weight: 600;
    border-radius: 48rpx;
    color: #fff;
    background: linear-gradient(135deg, #ff6b6b, #ec3939);
    box-shadow: 0 8rpx 24rpx -4rpx rgba(236, 57, 57, 0.35);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    letter-spacing: 2rpx;
}

.popup-btn.confirm:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 12rpx -4rpx rgba(236, 57, 57, 0.35);
    background: linear-gradient(135deg, #ff5c5c, #d63030);
}
</style>