<template>
    <view class="member-code">
        <!-- 会员信息 -->
        <view class="member-info">
            <image class="avatar" :src="userInfo.avatar" mode="aspectFill"></image>
            <view class="info">
                <view class="nickname">{{ userInfo.memName || userInfo.nickName }}</view>
                <view class="level">
                    <text class="tag">{{ userInfo.levels == 1 || userInfo.levels == 2 ? '金卡会员' : '普通会员' }}</text>
                    <!-- <text class="number">NO.{{ userInfo.memberNo || '---' }}</text> -->
                </view>
            </view>
        </view>

        <!-- 会员码 -->
        <view class="code-box">
            <view class="title">会员码</view>
            <view class="desc">向门店工作人员出示此码</view>
            <view class="barcode">
                <image v-if="barcode" :src="barcode" mode="widthFix"></image>
                <view v-else class="loading">
                    <text class="iconfont loading-icon">&#xe891;</text>
                    <text>加载中...</text>
                </view>
                <!-- <text v-if="barcode" class="code-number">{{ barcodeNumber }}</text> -->
            </view>
            <!-- <view class="refresh" @click="refreshCode">
                <text class="iconfont refresh-icon">&#xe665;</text>
                <text>刷新会员码</text>
            </view> -->
        </view>

        <!-- 使用说明 -->
        <view class="instructions">
            <view class="title">使用说明</view>
            <view class="item">
                <text class="dot"></text>
                <text class="text">会员码用于线下门店消费时积分、优惠等</text>
            </view>
            <view class="item">
                <text class="dot"></text>
                <text class="text">请勿将会员码提供给他人使用</text>
            </view>
            <view class="item">
                <text class="dot"></text>
                <text class="text">如遇到问题请联系门店工作人员</text>
            </view>
        </view>
        <view class="record-title" @click="goToRecord">明细记录</view>
    </view>
</template>

<script>
const req = require("../../utils/request");
export default {
    data() {
        return {
            userInfo: {},
            barcode: '', // 条形码图片地址
            barcodeNumber: '', // 条形码数字
        }
    },
    onLoad() {
        this.getUserInfo()
        this.getBarcode()
    },
    methods: {
        // 获取用户信息
        getUserInfo() {
            const userInfo = req.getStorage('userInfo')
            if (userInfo) {
                this.userInfo = userInfo
            }
        },
        // 获取条形码
        getBarcode() {
            // TODO: 调用后端接口获取条形码
            // 这里模拟接口调用
            req.getRequest(`/shopApi/weixin/getBarcode/${this.userInfo.id}`, {}, res => {
                this.barcode = res.msg
            })
            // setTimeout(() => {
            //     this.barcode = 'https://example.com/barcode.png'
            //     this.barcodeNumber = '1234 5678 9012'
            // }, 1000)
        },
        // 手动刷新条形码
        refreshCode() {
            uni.showLoading({
                title: '刷新中'
            })
            this.getBarcode()
            setTimeout(() => {
                uni.hideLoading()
                uni.showToast({
                    title: '刷新成功',
                    icon: 'success'
                })
            }, 1000)
        },
        goToRecord() {
            uni.navigateTo({
                url: '/mine/rechargeRecord/rechargeRecord'
            })
        }
    }
}
</script>

<style lang="scss">
.member-code {
    min-height: 100vh;
    background: #f5f5f5;
    padding: 20rpx;

    .member-info {
        background: linear-gradient(135deg, #ea1306 0%, #ff4b4b 100%);
        border-radius: 20rpx;
        padding: 40rpx 30rpx;
        display: flex;
        align-items: center;

        .avatar {
            width: 120rpx;
            height: 120rpx;
            border-radius: 50%;
            border: 4rpx solid rgba(255, 255, 255, 0.3);
        }

        .info {
            flex: 1;
            margin-left: 30rpx;
            color: #fff;

            .nickname {
                font-size: 36rpx;
                font-weight: 500;
                margin-bottom: 16rpx;
            }

            .level {
                display: flex;
                align-items: center;
                font-size: 24rpx;

                .tag {
                    background: rgba(255, 255, 255, 0.2);
                    padding: 4rpx 16rpx;
                    border-radius: 20rpx;
                    margin-right: 20rpx;
                }

                .number {
                    opacity: 0.8;
                }
            }
        }
    }

    .code-box {
        margin-top: 20rpx;
        background: #fff;
        border-radius: 20rpx;
        padding: 40rpx 30rpx 0;
        text-align: center;

        .title {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 12rpx;
        }

        .desc {
            font-size: 26rpx;
            color: #999;
            margin-bottom: 40rpx;
        }

        .barcode {
            width: 600rpx;
            height: 200rpx;
            margin: 0 auto;
            border-radius: 20rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            image {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            .loading {
                display: flex;
                flex-direction: column;
                align-items: center;
                color: #999;
                font-size: 26rpx;

                .loading-icon {
                    font-size: 48rpx;
                    margin-bottom: 20rpx;
                    animation: rotate 1s linear infinite;
                }
            }

            .code-number {
                margin-top: 20rpx;
                font-size: 32rpx;
                color: #333;
                font-family: monospace;
                letter-spacing: 2rpx;
            }
        }

        .refresh {
            margin-top: 40rpx;
            display: inline-flex;
            align-items: center;
            font-size: 28rpx;
            color: #666;

            .refresh-icon {
                font-size: 32rpx;
                margin-right: 8rpx;
            }

            &:active {
                opacity: 0.7;
            }
        }
    }

    .instructions {
        margin-top: 20rpx;
        background: #fff;
        border-radius: 20rpx;
        padding: 30rpx;

        .title {
            font-size: 30rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 20rpx;
        }

        .item {
            display: flex;
            align-items: center;
            margin-bottom: 16rpx;

            &:last-child {
                margin-bottom: 0;
            }

            .dot {
                width: 8rpx;
                height: 8rpx;
                border-radius: 50%;
                background: #ea1306;
                margin-right: 16rpx;
            }

            .text {
                font-size: 26rpx;
                color: #666;
                line-height: 1.5;
            }
        }
    }
    .record-title{
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 28rpx;
        color: #4E5969;
        line-height: 42rpx;
        text-align: center;
        margin: 30rpx auto;
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>