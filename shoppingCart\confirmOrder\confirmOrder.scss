.confirmOrder {
  position: relative;
  padding-bottom: 136rpx;
  background: #f8f8f8
    url("https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241108/81700ddcbdf54f7684146b26ea704531.png");
  background-repeat: no-repeat;
  // background-image: url("https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241108/81700ddcbdf54f7684146b26ea704531.png");
  //   background-size: cover;
  .distribution_tab {
    width: 710rpx;
    // height: 216rpx;
    border-radius: 16rpx;
    background: #ffffff;
    // margin: 40rpx 0 20rpx;
    position: relative;
    .tab_item {
      width: 50%;
      height: 76rpx;
      border-radius: 0px 16rpx 0px 40rpx;
      background: #f7f8fa;
      font-family: Alibaba Sans;
      font-size: 28rpx;
      font-weight: normal;
      line-height: 76rpx;
      color: #4e5969;
      text-align: center;
      display: flex;
      justify-content: center;
    }
    .left {
      border-radius: 16rpx 0rpx 40rpx 0rpx;
    }
    .tab_item_active {
      border-radius: 16rpx 16rpx 0px 0px;
      background: #ffffff;
      // margin-top: -10rpx;
      font-weight: 700;
      font-size: 30rpx;
      color: #333333;
      top: -10rpx;
    }
    .text_ {
      font-family: Alibaba Sans;
      font-size: 28rpx;
      font-weight: 500;
      line-height: normal;
      color: #3d3d3d;
    }
    .address_ {
      font-family: Alibaba Sans;
      font-size: 32rpx;
      font-weight: 600;
      line-height: normal;
      color: #333333;
      line-height: 50rpx;
    }
    .phone_ {
      border-top: 1px solid #f2f3f5;
      padding-top: 20rpx;
      padding-left: 24rpx;
      font-family: Alibaba Sans;
      font-size: 24rpx;
      font-weight: normal;
      line-height: 32rpx;
      color: #4e5969;
      .phone_input {
        padding: 0 20rpx;
        border: 1px solid #333333;
        margin-left: 20rpx;
        border-radius: 8rpx;
      }
    }
  }
  .post_mode {
    position: relative;
    width: 710rpx;
    border-radius: 16rpx;
    background: #ffffff;
    margin-bottom: 20rpx;
    font-family: Alibaba Sans;
    font-size: 28rpx;
    font-weight: 500;
    line-height: normal;
    color: #333333;
    .address_ {
      .tag {
        height: 32rpx;
        border-radius: 8rpx;
        background: #f9e9e8;
        box-sizing: border-box;
        border: 1px solid rgba(234, 19, 6, 0.2);
        font-family: Alibaba Sans;
        font-size: 20rpx;
        font-weight: 500;
        line-height: 30rpx;
        text-align: center;
        color: #ea1306;
        padding: 0 6rpx;
        margin-right: 8rpx;
        margin-top: 6rpx;
      }
      .text_ {
        font-family: Alibaba Sans;
        font-size: 28rpx;
        font-weight: normal;
        line-height: 44rpx;
        color: #3d3d3d;
      }
    }
  }
  .getTime {
    width: 710rpx;
    height: 96rpx;
    border-radius: 16rpx;
    background: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx;
    box-sizing: border-box;
    font-family: Alibaba Sans;
    font-size: 28rpx;
    font-weight: 500;
    line-height: normal;
    color: #3d3d3d;
    margin-bottom: 20rpx;
  }
  .goods_info {
    padding: 0 40rpx 1rpx;
    .goods_ {
      display: flex;
      // justify-content: space-between;
      // align-items: center;
      // .goods_img {
      //   display: flex;
      //   margin-bottom: 30rpx;
      // }
      font-family: Alibaba Sans;
      font-size: 28rpx;
      font-weight: normal;
      line-height: 32rpx;
      color: #3d3d3d;
      border-bottom: 1px solid #f2f3f5;
      margin-bottom: 20rpx;
      .goods_img {
        position: relative;
        width: 160rpx;
        height: 136rpx;
        border-radius: 8rpx;
        opacity: 1;
        background: #edf3fa;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 30rpx;
        margin-right: 20rpx;
        .discount {
          position: absolute;
          bottom: 20rpx;
          left: 20rpx;
          height: 22rpx;
          border-radius: 6rpx;
          opacity: 1;
          background: #fcf1f5;
          box-sizing: border-box;
          border: 1px solid #ea1306;
          padding: 0 10rpx;
          font-family: Alibaba Sans;
          font-size: 16rpx;
          font-weight: normal;
          line-height: 20rpx;
          text-align: center;
          color: #ea1306;
        }
      }
      .icon {
        color: #ffffff;
        display: inline-block;
        width: 50rpx;
        height: 24rpx;
        border-radius: 4rpx;
        background: #0b78ff;
        text-align: center;
        line-height: 24rpx;
        font-size: 28rpx;
        margin-right: 10rpx;
      }
    }
    .details {
      display: flex;
      justify-content: space-between;
      font-family: Alibaba Sans;
      font-size: 30rpx;
      font-weight: normal;
      line-height: 36rpx;
      color: #333333;
      margin-bottom: 30rpx;
      .distribution {
        display: inline-block;
        height: 32rpx;
        border-radius: 8rpx;
        box-sizing: border-box;
        border: 1px solid #999999;
        font-family: Alibaba Sans;
        font-size: 24rpx;
        font-weight: normal;
        line-height: 30rpx;
        color: #4e5969;
        padding: 0 14rpx;
        margin-left: 8rpx;
      }
    }
  }
  .payMode {
    width: 710rpx;
    border-radius: 16rpx;
    background: #ffffff;
    margin-bottom: 20rpx;
    .text_icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 12rpx;
      border: 1px solid #f2f3f5;
      text-align: center;
      line-height: 60rpx;
      margin-right: 16rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .text_ {
      font-family: Alibaba Sans;
      font-size: 28rpx;
      font-weight: 600;
      line-height: 60rpx;
      color: #3d3d3d;
    }
  }
  .balance {
    font-family: Alibaba Sans;
    font-size: 28rpx;
    font-weight: normal;
    line-height: 44rpx;
    color: #3d3d3d;
  }
  .mark {
    display: flex;
    font-family: Alibaba Sans;
    font-size: 28rpx;
    font-weight: normal;
    line-height: 64rpx;
    color: #4e5969;
    height: 64rpx;
    border-radius: 32rpx;
    background: #f7fafd;
    text-align: center;
    padding: 0 20rpx;
    margin-right: 16rpx;
  }
  .other {
    margin-bottom: 176rpx;
    font-family: Alibaba Sans;
    font-size: 28rpx;
    font-weight: normal;
    line-height: 44rpx;
    color: #3d3d3d;
    .text_ {
      color: #999999;
      font-size: 24rpx;
      line-height: 40rpx;
    }
  }
  .prescription_drug {
    font-family: Alibaba Sans;
    font-size: 24rpx;
    font-weight: normal;
    line-height: 40rpx;
    background: rgba(247, 3, 3, 0.6);
    text-align: center;
    color: #fff;
    padding: 10rpx 0;
  }
  .pay_btn {
    // position: fixed;
    // bottom: 0;
    width: 750rpx;
    height: 136rpx;
    border-radius: 40rpx 40rpx 0px 0px;
    background: #ffffff;
    box-shadow: 0px 8rpx 20rpx 0px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 0 40rpx;

    .text_ {
      font-family: Alibaba Sans;
      font-size: 28rpx;
      font-weight: normal;
      line-height: 28rpx;
      color: #3d3d3d;
    }
    .btn {
      width: 216rpx;
      height: 72rpx;
      border-radius: 40rpx;
      background: #ea1306;
      font-family: Alibaba Sans;
      font-size: 32rpx;
      font-weight: normal;
      line-height: 72rpx;
      color: #ffffff;
      text-align: center;
      &.btn-disabled {
        background: #cccccc !important;
        opacity: 0.7;
        color: #ffffff;
        cursor: not-allowed;

        &:active {
          opacity: 0.7;
        }
      }
    }
  }
  // 弹出层
  .popup_content {
    width: 750rpx;
    // height: 768rpx;
    border-radius: 40rpx 40rpx 0px 0px;
    background: #ffffff;
    padding-top: 40rpx;
    .popup_title {
      font-family: Alibaba Sans;
      font-size: 36rpx;
      line-height: 36rpx;
      color: #333333;
      text-align: center;
      border-bottom: 1px solid #f2f3f5;
      padding-bottom: 24rpx;
    }
    .popup_details {
      padding: 20rpx;
      .active {
        background-color: #fff;
        border-left: 4rpx solid #ea1306;
        color: #ea1306;
      }
      .active_time {
        color: #ea1306;
      }
    }
    .btn_time {
      width: 600rpx;
      height: 92rpx;
      border-radius: 80rpx;
      opacity: 1;
      background: #ea1306;
      font-family: Alibaba Sans;
      font-size: 32rpx;
      font-weight: normal;
      line-height: 92rpx;
      text-align: center;
      color: #ffffff;
      margin: 20rpx 75rpx 40rpx;
    }
  }
  /deep/.uni-section {
    border-radius: 16rpx;
  }
  /deep/.uni-section .uni-section-header__decoration {
    background-color: #ea1306;
  }
}
