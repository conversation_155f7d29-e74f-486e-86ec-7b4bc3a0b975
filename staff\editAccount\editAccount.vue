<template>
	<view class="container">
		<view class="form-container">
			<view class="form-item">
				<text class="label"><text style="color: #EA1306;">*</text>姓名:</text>
				<input type="text" v-model="formData.name" placeholder="请输入姓名" />
			</view>
			<view class="form-item">
				<text class="label"><text style="color: #EA1306;">*</text>证件号码:</text>
				<input type="text" v-model="formData.idNumber" placeholder="请输入证件号码" />
			</view>
			<view class="form-item">
				<text class="label"><text style="color: #EA1306;">*</text>联系方式:</text>
				<input type="text" v-model="formData.phone" placeholder="请输入联系方式" disabled />
			</view>
			<view class="form-item">
				<text class="label"><text style="color: #EA1306;">*</text>支付宝账号:</text>
				<input type="text" v-model="formData.alipayAccount" placeholder="请输入支付宝账号" />
			</view>
		</view>

		<view class="btn-group" v-if="isEdit">
			<view class="btn delete" @click="deleteAccount">删除账号</view>
			<view class="btn save" @click="saveAccount">修改账号</view>
		</view>
		<view class="btn-group" v-else>
			<view class="btn save" @click="saveAccount">确定</view>
		</view>
	</view>
</template>

<script>
const req = require("../../utils/request");
export default {
	data() {
		return {
			isEdit: false,
			formData: {
				name: '陈聪',
				idNumber: '440897199911109876',
				phone: req.getStorage('userInfo').phone,
				alipayAccount: '***********'
			}
		}
	},
	onLoad(options) {
		this.formData.name = req.getStorage('userInfo').memName || '';
		this.formData.idNumber = req.getStorage('userInfo').idNum || '';
		this.formData.alipayAccount = req.getStorage('userInfo').alipayAccount || '';
		// if (options.data) {
		// 	this.isEdit = true;
		// 	this.formData = JSON.parse(options.data);
		// }
	},
	methods: {
		saveAccount() {
			// 表单验证
			if (!this.formData.name) {
				uni.showToast({
					title: '请输入姓名',
					icon: 'none'
				});
				return;
			}
			if (!this.formData.idNumber) {
				uni.showToast({
					title: '请输入证件号码',
					icon: 'none'
				});
				return;
			}
			if (!this.formData.alipayAccount) {
				uni.showToast({
					title: '请输入支付宝账号',
					icon: 'none'
				});
				return;
			};
			req.postRequest('/shopApi/weixin/updateMemberships', {
				idNum: this.formData.idNumber,
				memName: this.formData.name,
				alipayAccount: this.formData.alipayAccount
			}, res => {
				// TODO: 调用保存接口
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});
				this.getUserInfo();
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			})
		},
		getUserInfo() {
			req.getRequest("/shopApi/weixin/getUserInfo", {}, res => {
				const userInfo = req.getStorage('userInfo');
				const data = { ...userInfo, ...res.data };
				req.setStorage("userInfo", data);
			});
		},
		deleteAccount() {
			uni.showModal({
				title: '提示',
				content: '确定要删除该账号吗？',
				success: (res) => {
					if (res.confirm) {
						// TODO: 调用删除接口
						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				}
			});
		}
	}
}
</script>

<style>
.container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 8rpx 8rpx 120rpx;
	box-sizing: border-box;
}

.form-container {
	background-color: #fff;
	padding: 30rpx;
	border-radius: 10rpx;
	overflow: hidden;
}

.form-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	/* flex-direction: column; */
	padding: 30rpx 0;
	border-bottom: 1rpx solid #eee;
}

.label {
	font-size: 28rpx;
	color: #333;
	/* margin-bottom: 20rpx; */
}

input {
	font-size: 28rpx;
	color: #333;
	text-align: right;
}

.btn-group {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	height: 100rpx;
}

.btn {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 32rpx;
	color: #fff;
}

.delete {
	background-color: #ff4d4f;
}

.save {
	background-color: #EA1306;
}
</style>
