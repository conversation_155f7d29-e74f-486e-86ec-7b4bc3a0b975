<template>
	<view class="pickupList">
		<view class="search">
			<view style="margin-right: 16rpx;">
				<picker mode="region" @change="bindRegionChange" :value="region" :custom-item="customItem">
					<view class="picker dflex">
						{{ regions }}<uni-icons type="down" size="13"></uni-icons>
					</view>
				</picker>
			</view>
			<view class="search_input">
				<view
					style="display: flex;width: 480rpx;align-items: center;border: 1px solid #EA1306;box-sizing: border-box;border-radius: 80rpx;padding: 0 28rpx;">
					<i class="iconfont" style="color: #666666;margin-right: 8rpx;font-size: 28rpx;">&#xe621;</i>
					<input v-model="searchVal" class="input_" placeholder="搜索您想要找的网点" />
				</view>
				<view class="search_btn">搜索</view>
			</view>
		</view>
		<view class="orientation">
			<view style="display: flex;">
				<i class="iconfont" style="color: #EA1306;font-size: 28rpx;margin-right: 8rpx;">&#xe60e;</i>
				<text>广东省广州市天河区天府路1号</text>
				<i class="iconfont" style="color: #4E5969;font-size: 8rpx;margin-left: 8rpx;">&#xe604;</i>
			</view>
			<view style="display: flex;">
				<i class="iconfont" style="color: #EA1306;font-size: 28rpx;margin-right: 8rpx;">&#xe61f;</i>
				<text style="font-weight: 400;">获取定位</text>
			</view>
		</view>
		<view style="background-color: #f8f8f8;padding: 0 20rpx;">
			<uni-section title="当前选择" type="line">
				<view class="current">
					<image
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241119/001451741aa0494f98a3c69511a397a3.png"
						style="width: 124rpx;height: 124rpx;margin-right: 16rpx;"></image>
					<view class="text_">
						<view>广州天河风栗园分店</view>
						<view style="font-weight: 400;color: #999999;font-size: 24rpx;line-height: 30rpx;">
							广东省广州市天河区风栗园南3号首层</view>
						<view style="display: flex;font-weight: 400;font-size: 24rpx;">
							<i class="iconfont " style="color: #999999;margin-right: 4rpx;">&#xe620;</i><text
								style="color: #666666;margin-right: 8rpx;">距您883m</text>|<text
								style="color: #666666;margin-left: 8rpx;">查看地图</text><uni-icons type="right"
								size="10"></uni-icons>
						</view>
					</view>
				</view>
			</uni-section>
		</view>
		<view style="background-color: #f8f8f8;padding: 0 20rpx;">
			<uni-section title="附近选择" type="line">
				<view class="current">
					<image
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241119/001451741aa0494f98a3c69511a397a3.png"
						style="width: 124rpx;height: 124rpx;margin-right: 16rpx;"></image>
					<view class="text_">
						<view>广州天河风栗园分店</view>
						<view style="font-weight: 400;color: #999999;font-size: 24rpx;line-height: 30rpx;">
							广东省广州市天河区风栗园南3号首层</view>
						<view style="display: flex;font-weight: 400;font-size: 24rpx;">
							<i class="iconfont " style="color: #999999;margin-right: 4rpx;">&#xe620;</i><text
								style="color: #666666;margin-right: 8rpx;">距您883m</text>|<text
								style="color: #666666;margin-left: 8rpx;">查看地图</text><uni-icons type="right"
								size="10"></uni-icons>
						</view>
					</view>
				</view>
			</uni-section>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			region: [],
			regions: '天河区',
			customItem: "全部",
			searchVal: "",
		};
	},
	methods: {
		bindRegionChange: function (e) {
			return
			let that = this;
			console.log('picker发送选择改变，携带值为', e.detail);
			let code = e.detail.code;
			that.isLoad = true;
			that.form.page = 1;
			// that.getList(e.detail.code[2]);
			if (code.length == 1) {
				that.form.province = code[0];
				that.form.city = '';
				that.form.country = '';
				that.region = e.detail.value[0];
				that.regions = that.region.toString('');
			} else if (code.length == 2) {
				that.form.province = code[0];
				that.form.city = code[1];
				that.form.country = '';
				that.region = [e.detail.value[0], e.detail.value[1]];
				that.regions = that.region[1].toString('');
			} else {
				that.form.province = code[0];
				that.form.city = code[1];
				that.form.country = code[2];
				that.region = [e.detail.value[0], e.detail.value[1], e.detail.value[2]];
				that.regions = that.region[2].toString('');
			}
			// that.getList();
			// this.setData({
			// 	region: e.detail.value
			// 	'form.regionCode': e.detail.code[2]
			// });
		},
	},
}
</script>

<style lang="scss" scoped src="./pickupList.scss"></style>
