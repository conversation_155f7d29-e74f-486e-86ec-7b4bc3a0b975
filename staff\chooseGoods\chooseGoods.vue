<template>
	<!-- <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta> -->
	<view>
		<!-- 禁止滚动穿透 -->
		<view class="classify">
			<view class="header">
				<view style="position: relative;">
					<image
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241113/aa02af4309b4409f8eb8925b36d99ef5.png"
						class="search_icon" style="left:48rpx;" />
					<!-- <input type="text" placeholder="搜索疾病、症状、商品" class="search" v-model="search_val"
              @click="goUrl('/product/search/search')"> -->
					<view class="search" @click="goUrl('/product/search/search')">搜索疾病、症状、商品</view>
					<view style="position: absolute;width: 70rpx;height: 70rpx;top: 2rpx;right: 50rpx;"
						@click.stop="scanCode">
						<image
							src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241112/faaec3e1db604501bd4e36272ffce43e.png"
							class="search_icon" style="right: 20rpx;" />
					</view>
				</view>
				<view class="tab_">
					<view class="tab_item" :class="tabValue === 0 ? 'tab_active' : ''" @click="changTabVable(0)">
						非处方药</view>
					<view class="tab_item" :class="tabValue === 1 ? 'tab_active' : ''" @click="changTabVable(1)">处方药
					</view>
				</view>
			</view>
			<view class="body">
				<scroll-view class="left_tab" scroll-y enhanced :show-scrollbar="false">
					<view class="left_item" v-for="(item, index) in classifyList" :key="index"
						:class="leftTab === index ? 'left_item_active' : ''" @click="classifyConfirm(index, item.id)">
						{{ item.name }}
					</view>
					<view class="card_" v-if="false">
						<i class="iconfont" style="color: #4E5969;">&#xe60d;</i>
						<view class="cornermark">1</view>
					</view>
				</scroll-view>
				<view class="right_tab">
					<!-- 统筹专区 -->
					<view style="padding-bottom: 20rpx;margin-bottom: 14rpx;background-color: #fff;"
						v-if="leftTab === 0">
						<view class="tc_view">
							<view class="tc_item" @click="searchGood(-1, '全部')">
								<image
									src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/87fc01116fa145f297b445640b8d7504.png"
									style="width: 96rpx;height: 96rpx;border-radius: 50%;" />
								<view :class="topTab === -1 ? 'tc_item_active' : ''">全部</view>
							</view>
							<view class="tc_item" v-for="(item, index) in list" :key="index"
								@click="searchGood(index, item.name, item.id)">
								<image :src="item.icon" style="width: 96rpx;height: 96rpx;border-radius: 50%;"
									mode="aspectFit" />
								<view :class="topTab === index ? 'tc_item_active' : ''">{{ item.name }}</view>
							</view>
						</view>
						<view
							style="text-align: center;font-size: 24rpx;font-weight:700;color: #4e5969;margin-top: 12rpx;"
							@click="showMore" v-if="showMore_btn">{{ show_more ? '收起'
								: '显示更多' }}</view>
					</view>
					<!-- 药品 -->
					<view class="right_bottom">
						<uni-swiper-dot :current="current" field="content" v-if="leftTab !== 0" :autoplay="true"
							:interval="3000" :duration="1000">
							<swiper autoplay :circular="true" class="swiper-box" @change="bannerChange">
								<swiper-item style="display: flex;justify-content: center;"
									v-for="(item, index) in bannerList" :key="index">
									<view style="height:100%;width:100%;border-radius: 20rpx;">
										<image :src="item" mode="aspectFill" style="height:100%;width:100%;">
										</image>
									</view>
								</swiper-item>
							</swiper>
						</uni-swiper-dot>
						<view style="padding: 40rpx 26rpx 0 12rpx;">
							<view style="margin-bottom: 20rpx;" v-if="leftTab !== 0">
								<view class="screen_tag">
									<view style="margin-right:60rpx;display: flex;align-items: center;"
										@click="selectSort(1)">
										<view style="margin-right: 10rpx;"
											:style="sort == 4 || sort == 3 ? 'color:#EA1306;' : ''">价格
										</view>
										<view>
											<i class="iconfont"
												style="font-size: 10rpx;color: #D9D9D9;margin-bottom: 5rpx;"
												:style="sort == 3 ? 'color:#EA1306;' : ''">&#xe605;</i>
											<i class="iconfont" style="font-size: 10rpx;color: #D9D9D9;"
												:style="sort == 4 ? 'color:#EA1306;' : ''">&#xe604;</i>
										</view>
									</view>
									<view @click="selectSort(2)" :style="sort === 2 ? 'color:#EA1306;' : ''">销量
									</view>
								</view>
								<view class="screen_radio">
									<view style="display: flex;">
										<view class="text_radio " :class="topTab === -1 ? 'text_radio_active' : ''"
											@click="searchGood(-1, '全部')">全部
										</view>
										<view class="text_radio" v-for="(item, index) in list_copy" :key="index"
											@click="searchGood(index, item.name, item.id)"
											:class="topTab === index ? 'text_radio_active' : ''">{{
												item.name }}
										</view>
									</view>
									<uni-icons type="down" size="14" color="#999999" @click="showRadio"></uni-icons>
								</view>
								<view class="classify_tag ">{{ selectName }}</view>
							</view>
							<scroll-view scroll-y enhanced :show-scrollbar="false" class="goods_view"
								:style="{ height: `calc(100vh - ${scrollHeight}rpx)` }" lower-threshold="30"
								@scrolltolower="scrolltolower">
								<view class="restoratives">
									<view class="res_item" v-for="(item, index) in goodsList" :key="index">
										<!-- @click="goToDetail(item)" -->
										<view class="item_img">
											<image :src="item.img"
												style="width: 156rpx;height: 156rpx;border-radius: 8rpx;"
												mode="aspectFit"
												:style="{ filter: item.isPrescription == 1 ? 'blur(5px)' : '' }" />
											<image style="position: absolute;top:0;left:0;width: 156rpx;height: 156rpx;"
												v-if="item.isPrescription == 1"
												src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240111/a338c782689a421b8e9ab0848c3c5c10.png">
											</image>
											<view class="tag" v-if="item.isMedicalInsurance === 1">医保</view>
											<view class="tag" style="background-color: #08BF8B;"
												v-if="item.isOverallPlanning === 1">统筹
											</view>
										</view>
										<view>
											<view style="display: flex;margin: 10rpx 0;">
												<view
													style="background-color: #0B78FF;width: 50rpx;height: 30rpx;text-align: center;border-radius: 6rpx; margin-right: 12rpx;margin-top: 4rpx;line-height: 32rpx;"
													v-if="item.isOtc === 1">
													<i class="iconfont"
														style="color: #fff;font-size: 28rpx;">&#xe606;</i>
												</view>
												<view
													style="background-color: #08BF8B;width: 50rpx;height: 30rpx;line-height: 24rpx;text-align: center;border-radius: 6rpx; margin-right: 12rpx;margin-top: 4rpx;"
													v-else-if="item.isPrescription === 1">
													<text style="color: #fff;font-size: 20rpx;">RX</text>
													<!-- <i class="iconfont" style="color: #fff;font-size: 28rpx;">&#xe606;</i> -->
												</view>
												<view class="title">{{ item.goodsName }}</view>
											</view>
											<view style="display: flex;align-items: center;" v-if="false">
												<text class="red_view"
													style=" background: #ea1306;border-radius: 8rpx 0rpx 0rpx 8rpx;color: #fff;">促</text>
												<text class="red_view"
													style="width: 108rpx;border-radius: 0rpx 8rpx 8rpx 0rpx;color: #EA1306;border: 1rpx solid #EA1306;box-sizing: border-box;line-height: 22rpx;">积分换</text>
											</view>
											<view>
												<view
													style="display: flex;align-items: center;justify-content: space-between;margin: 12rpx 0;width: 340rpx;">
													<view class="text_pay">
														<text style="color: #E91306;font-weight: 600;">¥{{ levels ==
															2 ? item.level2Price : item.level1Price }} </text>
														<text style="text-decoration: line-through;"> /¥{{
															item.salePrice }}</text>
													</view>
													<view
														style="border-radius: 50%;width: 44rpx;height: 44rpx;text-align: center;line-height: 44rpx;"
														@click.stop="handleBuyConfirm(item)">
														<i class="iconfont"
															style="color: #fff; font-size: 19px;background: #E91306; border-radius: 44rpx;">&#xe60c;</i>
													</view>
												</view>
											</view>
											<view class="volume" v-if="false">
												<image
													src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241116/a99be970bf7543138a7b201102d13449.png"
													style="width: 28rpx;height: 28rpx;">
												</image>
												<view class="text_">
													<view>儿童专区热销榜<text style="color:#F09C20;">.TOP1</text></view>
													<uni-icons type="right" size="10" color="#F09C20"></uni-icons>
												</view>
											</view>
										</view>
									</view>
									<view v-if="goodsList.length === 0" style="text-align: center;color: #999;">
										<image mode=""
											src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png"
											style="width: 372rpx;height: 312rpx;margin-top: 50rpx;">
										</image>
										<view style="margin-top: 10rpx;font-size: 28rpx;">暂无药品</view>
									</view>
								</view>
							</scroll-view>
						</view>
					</view>
				</view>
			</view>
			<!-- v-if="!manage && goodsList.length" -->
			<view class="manage">
				<view style="display: flex;justify-content: space-between;align-items:center;">
					<view class="check_btn" style="line-height: 32rpx;align-items: flex-start;">
						<i class="iconfont" style="color: #EA1306;margin-right: 16rpx;">&#xe610;</i>
						<view style="line-height: 30rpx;">
							<view style="font-weight: 500;font-size: 28rpx;color: #4E5969;">已选{{ cartList.length }}件
							</view>
							<view style="font-size: 20rpx;color: #666666;text-decoration: underline;"
								@click.stop="$refs.settlement.open('bottom')">
								查看详情
							</view>
						</view>
					</view>
					<view style="display: flex;">
						<view class="total_text">
							<view>合计：<text style="font-weight: 600;color: #EA1306;">¥<text style="font-size: 32rpx;">{{
								detailInfo.amountPrice ? detailInfo.amountPrice : 0.00 }}</text></text>
							</view>
							<view>
								<!-- <text style="color: #999999;font-size: 20rpx;">免费配送</text> -->
								<text class="preferential_btn" @click="open">优惠明细¥{{
									detailInfo.discountPrice ? detailInfo.discountPrice : 0 }}</text>
							</view>
						</view>
						<view class="manage_btn" style="color: #fff;background: #EA1306;" @click="comfirmOrder">提交
						</view>
					</view>
				</view>
			</view>
			<uni-popup ref="popup" :type="center" :animation="false" border-radius="10px 10px 0 0" @change="change">
				<view
					style="margin-bottom: 20rpx;background-color: #fff;width: 540rpx;padding: 20px 13px 20rpx 6px;border-radius: 16rpx 16rpx 0 16rpx;position: fixed;right: 0;top: 450rpx;">
					<view class="screen_tag">
						<view style="margin-right:60rpx;display: flex;align-items: center;" @click="selectSort(1)">
							<view style="margin-right: 10rpx;" :style="sort == 4 || sort == 3 ? 'color:#EA1306;' : ''">
								价格
							</view>
							<view>
								<i class="iconfont" style="font-size: 10rpx;color: #D9D9D9;margin-bottom: 5rpx;"
									:style="sort == 3 ? 'color:#EA1306;' : ''">&#xe605;</i>
								<i class="iconfont" style="font-size: 10rpx;color: #D9D9D9;"
									:style="sort == 4 ? 'color:#EA1306;' : ''">&#xe604;</i>
							</view>
						</view>
						<view @click="selectSort(2)" :style="sort === 2 ? 'color:#EA1306;' : ''">销量</view>
					</view>
					<view class="screen_radio">
						<view style="display: flex;flex-wrap: wrap;">
							<view class="text_radio " :class="topTab === -1 ? 'text_radio_active' : ''"
								@click="searchGood(-1, '全部')">全部
							</view>
							<view class="text_radio" v-for="(item, index) in listAll" :key="index"
								@click="searchGood(index, item.name, item.id)"
								:class="topTab === index ? 'text_radio_active' : ''">{{
									item.name }}
							</view>
						</view>
						<uni-icons :type="radio_pop ? 'up' : 'down'" size="14" color="#999999"
							@click="showRadio"></uni-icons>
					</view>
				</view>
			</uni-popup>
		</view>
		<!-- 结算 -->
		<uni-popup ref="settlement" type="bottom" background-color="#fff" borderRadius="40rpx 40rpx 0px 0px"
			@change="change">
			<view class="popup_content" :style="{ 'padding-bottom': 'calc(124rpx + env(safe-area-inset-bottom))' }">
				<i class="iconfont" style="font-size: 24rpx;position: fixed;right: 40rpx;top: 40rpx;color: #fff;"
					@click="$refs.settlement.close()">&#xeb6a;</i>
				<view class="popup_title">
					<view style="font-weight: 600;">优惠明细</view>
					<view style="font-size: 24rpx;color: #fff;">自动计算最佳优惠，实际以结算页为准</view>
				</view>
				<scroll-view class="popup_details" scroll-y enhanced :show-scrollbar="false">
					<view style="padding-bottom: 10rpx;">
						<view style="background-color: #fff;margin-bottom: 20rpx;border-radius: 16rpx;">
							<view style="display: flex;flex-wrap: wrap;text-align: center;padding: 20rpx 10rpx;">
								<view style="width: 156rpx;margin-right: 16rpx;" v-for="(item, index) in showList"
									:key="index">
									<view style="position: relative;">
										<image :src="item.pic" style="width: 120rpx;height: 76rpx;"
											:style="{ filter: item.prescriptionDrug == 1 ? 'blur(5px)' : '' }">
										</image>
										<image
											style="position: absolute;top:0;left:50%;transform: translateX(-50%);width: 120rpx;height: 76rpx;"
											v-if="item.prescriptionDrug == 1"
											src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240111/a338c782689a421b8e9ab0848c3c5c10.png">
										</image>

									</view>
									<view style="line-height: 32rpx;font-size: 20rpx;">{{ item.title }}</view>
								</view>
								<view
									style="font-size: 24rpx;line-height: 28rpx;text-align: center;width: 100%;color: #999999;margin-top: 20rpx;"
									@click="checkshowMore">
									已选{{ detailInfo.goodsList ? detailInfo.goodsList.length : 0 }}件商品<uni-icons
										:type="showAllGoods ? 'up' : 'down'" size="10"></uni-icons>
								</view>
							</view>
						</view>
						<view style="background-color: #fff;border-radius: 16rpx;padding: 20rpx;margin-bottom: 20rpx;">
							<view class="details">
								<view>商品总价</view>
								<view style="font-weight: 600;">¥{{ detailInfo.sumPrice }}</view>
							</view>
							<!-- <view v-if="modeTab == 2" class="details">
								<view>配送费<text class="distribution" v-if="detailInfo.fullSubtractName">{{
									detailInfo.fullSubtractName }}</text></view>
								<view style="font-weight: 600;"><text
										style="text-decoration: line-through;color: #999999;margin-right: 12rpx;">¥{{
											detailInfo.deliveryPrice }}</text>¥{{ chargePrice }}<uni-icons type="right"
										size="14"></uni-icons>
								</view>
							</view> -->
							<view class="details">
								<view>优惠券<uni-icons type="down" size="10"></uni-icons></view>
								<view style="font-weight: 600;color: #EA1306;font-size: 28rpx;">-¥{{
									detailInfo.couponPrice ? detailInfo.couponPrice : 0.00 }}
								</view>
							</view>
							<view class="details">
								<view>共优惠</view>
								<view style="font-weight: 600;color: #EA1306;font-size: 28rpx;">-¥{{
									detailInfo.discountPrice ? Math.abs(detailInfo.discountPrice) : 0 }}
								</view>
							</view>
							<view class="total_">
								<view>合计金额</view>
								<view style="font-weight: 600;">¥{{ detailInfo.amountPrice }}</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
	data() {
		return {
			levels: req.getStorage("userInfo").levels,
			search_val: "",
			tabValue: 0,
			current: 0,
			bannerList: ["https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241210/86b69553d66745fdaffc3f2cfadc4e84.jpg", "https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/9d7f88c526c64e80bc10019fbbf2e7c8.jpg", "https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/7fa7653053ab413188ef3199ce4bb3b1.jpg"],
			radio_pop: false,
			show: false,
			classifyList: [],
			topTab: -1,
			leftTab: 0,
			listAll: [],
			list: [],
			list_copy: [],
			showMore_btn: false,  // 显示更多的按钮
			show_more: false,  // 显示更多
			pageNum: 1,
			pageSize: 10,
			total: 0,
			categoryId: "",
			categoryItemId: "",
			goodsList: [], // 商品列表
			scrollHeight: 0,
			data: {},
			selectName: '',
			sort: 1,  // 1：默认、2：销量、3：价格正序、4：价格降序
			cartList: [],
			memberId: "",
			couponUserId: "",
			detailInfo: {},
			checked: [],
			showAllGoods: false,
			showList: []
		}
	},
	onLoad(options) {
		console.log(options);
		if (options.tabValue) {
			this.tabValue = Number(options.tabValue);
			this.searchGood(this.topTab, this.selectName, this.categoryId);
		};
		this.memberId = options.memberId ? Number(options.memberId) : ""
	},
	onShow() {
		// 获取全局变量或本地存储中的状态
		let tabValue = uni.getStorageSync('classifyTabValue');
		if (tabValue != null) {
			this.tabValue = Number(tabValue);
			console.log(this.tabValue, 'this.tabValue');
			// 清除状态,避免影响下次进入
			uni.removeStorageSync('classifyTabValue');
		}
		if (this.categoryId) {
			this.searchGood(this.topTab, this.selectName, this.categoryId);
		} else {
			this.getList();
		};
		this.getcartList();
	},
	methods: {
		// 
		scanCode() {
			// 允许从相机和相册扫码
			uni.scanCode({
				success: res => {
					console.log(res, "扫码扫码扫码");
					// uni.navigateTo({
					//   url: '/' + res.path
					// });
					// this.scene = '1290926887903780865';
					// this.loadCodeParams();
				}
			});
		},
		selectSort(type) {
			if (type == 1) {
				switch (this.sort) {
					case 1:
						this.sort = 3;
						break;
					case 2:
						this.sort = 3;
						break;
					case 3:
						this.sort = 4;
						break;
					default:
						this.sort = 1;
				}
			} else if (type == 2) {
				if (this.sort !== 2) {
					this.sort = 2
				} else {
					this.sort = 1
				}
			};
			this.searchGood(this.topTab, this.selectName, this.categoryId);
		},
		scrolltolower() {
			if (this.pageNum * this.pageSize >= this.total) return req.msg("没有更多数据了")
			this.pageNum++;
			// console.log("触底", this.pageNum);
			// this.getHealthRecord();
			this.searchGood(this.topTab, this.selectName, this.categoryId);
		},
		bannerChange(e) {
			this.current = e.detail.current;
		},
		showRadio() {
			this.radio_pop = !this.radio_pop;
			if (this.radio_pop) {
				this.$refs.popup.open('center');
			} else {
				this.$refs.popup.close();
			};
		},
		change(e) {
			// console.log(e);
			this.radio_pop = e.show;
			this.show = e.show;
		},
		// 处方药跟非处方药 - 切换
		changTabVable(value) {
			this.tabValue = value;
			this.pageNum = 1;
			this.searchGood(this.topTab, this.selectName, this.categoryId);
		},
		// 显示更多
		showMore() {
			this.show_more = !this.show_more;
			if (this.show_more) {
				this.list = [...this.listAll];
				let num = Math.ceil((this.list.length + 1) / 4) * 150;
				this.scrollHeight = 430 + num;
				// console.log(Math.ceil((this.list.length + 1) / 4) * 150);
			} else {
				this.list = this.listAll.slice(0, 3);
				if (this.showMore_btn) {
					this.scrollHeight = 480;
				} else {
					this.scrollHeight = 440;
				};
			}
		},
		// 左边商品分类切换获取
		classifyConfirm(index, id) {
			// console.log(index, id);
			this.topTab = -1;
			this.leftTab = index;
			this.categoryId = id;
			this.pageNum = 1;
			this.sort = 1;
			this.getList(id);
		},
		// 查询商品分类列表
		getList(id) {
			this.goodsList = [];
			req.showLoading();
			req.getRequest("/shopApi/home/<USER>", {
				pid: id
			}, res => {
				console.log(res, '查询商品分类列表');
				if (!id) {
					this.classifyList = [...res.data];
					this.categoryId = this.classifyList[0].id;
					this.getList(this.categoryId);
				} else {
					this.listAll = [...res.data];
					if (this.listAll.length > 3) {
						this.showMore_btn = true;
						this.scrollHeight = 480;
					} else {
						this.showMore_btn = false;
						this.scrollHeight = 440;
					};
					if (this.show_more) {
						this.list = [...this.listAll];
						let num = Math.ceil((this.list.length + 1) / 4) * 150;
						this.scrollHeight = 430 + num;
					} else {
						this.list = this.listAll.slice(0, 3);
						if (this.showMore_btn) {
							this.scrollHeight = 480;
						} else {
							this.scrollHeight = 440;
						};
					}
					if (this.leftTab != 0) {
						this.list_copy = this.listAll.slice(0, 2);
						this.scrollHeight = 788;
					}
					this.searchGood(-1, '全部', this.categoryId);
				};
			})
		},
		// 搜索商品
		searchGood(index, name, id) {
			this.topTab = index;
			this.selectName = name;
			this.categoryId = id;
			if (!this.categoryId) {
				this.categoryId = this.classifyList[this.leftTab].id
			};
			req.getRequest("/shopApi/home/<USER>", {
				sort: this.sort,
				storeId: req.getStorage("currentStore").id,
				categoryId: this.categoryId,
				title: this.search_val,
				isPrescription: this.tabValue,
				pageNum: this.pageNum,
				pageSize: this.pageSize
			}, res => {
				console.log(res, "搜索商品");
				if (this.pageNum == 1) {
					this.goodsList = [...res.data.list];
				} else {
					this.goodsList = [...this.goodsList, ...res.data.list];
				};
				this.total = res.data.total;
				uni.hideLoading();
				this.$refs.popup.close();
			})
		},
		// 查看更多商品
		checkshowMore() {
			this.showAllGoods = !this.showAllGoods;
			if (this.showAllGoods) {
				this.showList = [...this.detailInfo.goodsList];
			} else {
				this.showList = this.detailInfo.goodsList.slice(0, 4);
			}
		},
		goToDetail(item) {
			console.log(item, "item.............");
			if (!item || !item.id) {
				req.msg('商品信息不完整')
				return
			}
			let url = `/product/detail/detail?id=${item.id}`
			if (item.type) {
				url += `&type=${item.type}`
			}
			// console.log(url);
			this.goUrl(url);
		},
		// 获取购物车列表
		getcartList() {
			req.showLoading();
			req.getRequest('/shopApi/purchase/list', {
				mode: 1,
				merchantId: req.getStorage("currentStore").id,
				isDirect: 0,
				uid: this.memberId,
			}, res => {
				console.log(res, "购物车列表");
				this.cartList = [...res.data];
				this.checked = this.cartList.map(item => item.id);
				uni.hideLoading();
				this.getDiscount();
			})
		},
		// 添加购物车
		handleBuyConfirm(item) {
			console.log(item);
			req.postRequest("/shopApi/purchase/cart", {
				skuId: null,
				merchantId: req.getStorage("currentStore").id,
				quantity: 1,
				productId: item.id,
				mode: 1,
				actId: item.actId ? item.actId : "",
				uid: this.memberId
			}, res => {
				this.getcartList();
				req.msg('加入成功');
			})
		},
		// 查看结算优惠明细
		getDiscount(open) {
			let ids_ = this.checked.join(",");
			if (!ids_) {
				this.detailInfo = {};
				this.couponUserId = '';
				return;
			}
			req.getRequest('/shopApi/purchase/discountDetail1', {
				ids: ids_,
				// discountType: 1,
				merchantId: req.getStorage("currentStore").id,
				mode: 1,
				couponUserId: this.couponUserId,
				uid: this.memberId
			}, res => {
				console.log(res, "优惠明细");
				this.detailInfo = res.data;
				if (this.showAllGoods) {
					this.showList = [...this.detailInfo.goodsList];
				} else {
					this.showList = this.detailInfo.goodsList.slice(0, 4);
				}
				// this.chargePrice = (this.detailInfo.deliveryPrice - this.detailInfo.deliverySubtractPrice).toFixed(2);
				this.detailInfo.deliveryPrice = this.detailInfo.deliveryPrice ? this.detailInfo.deliveryPrice.toFixed(2) : '0.00';
				this.couponUserId = res.data.couponUserId ? res.data.couponUserId : "";
			})
		},
		// 去结算 - 确认订单
		comfirmOrder() {
			// console.log(this.checked);
			// if (!this.checked.length) {
			// 	return req.msg("请选择结算商品")
			// };
			// let list_ = this.cartList.filter(item => this.checked.includes(item.id));
			// console.log(list_);
			// let result = this.checkProperties(list_, 'prescriptionDrug');  // 含有处方药 则返回false; 不含处方药则返回true;
			// uni.navigateTo({
			// 	url: `/shoppingCart/confirmOrder/confirmOrder?prescriptionDrug=${result}&mode=${this.modeTab}&goodslist=${this.checked}&merchantId=${this.merchantId}&discountType=1&couponId=${this.couponUserId}`
			// });
			const eventChannel = this.getOpenerEventChannel();
			eventChannel.emit('acceptDataFromOpenedPage');
			uni.navigateBack();
		},
		goUrl(url) {
			uni.navigateTo({
				url: url
			})
		},
	},

}
</script>

<style lang="scss" scoped src="./chooseGoods.scss"></style>
