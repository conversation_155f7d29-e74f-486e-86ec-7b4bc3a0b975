.cartView {
  background-color: #f9f9f9;

  //   height: 100vh;
  .mode_tab {
    height: 76rpx;
    border-radius: 80rpx;
    background: #fef7f7;
    margin-bottom: 30rpx;
    font-family: Source <PERSON>;
    font-size: 28rpx;
    font-weight: normal;
    line-height: normal;
    color: #3d3d3d;
    display: flex;
    // line-height: 76rpx;
    padding: 0 40rpx;
    box-sizing: border-box;
    align-items: center;

    .active {
      color: #e42a2e;
      width: 320rpx;
      height: 56rpx;
      border-radius: 20rpx;
      background: #ffffff;
      line-height: 56rpx;
      text-align: center;
      font-weight: 600;
    }
  }

  .address {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
  }

  .cart_tab {
    display: flex;
    justify-content: space-between;

    .tab_item {
      min-width: 108rpx;
      height: 60rpx;
      font-family: PingFang SC;
      font-size: 28rpx;
      font-weight: normal;
      line-height: 48rpx;
      color: #4e5969;
      text-align: center;
    }

    .tab_item_active {
      color: #ea1306;
      font-weight: 600;
      border-bottom: 4rpx solid #ea1306;
    }
  }

  // 购物车
  .cart_ {
    padding: 40rpx 20rpx 20rpx;

    .buy_text {
      padding: 0 24rpx;
      height: 48rpx;
      border-radius: 16rpx 26rpx 26rpx 0px;
      background: linear-gradient(89deg, #ff8f24 0%, #ff3237 104%);
      font-family: Alibaba Sans;
      font-size: 28rpx;
      font-weight: normal;
      line-height: 48rpx;
      text-align: center;
      color: #fefefe;
    }

    .cart_trade_in {
      padding: 26rpx 20rpx;
      box-sizing: border-box;
      border-radius: 8rpx;
      background: linear-gradient(180deg, #fdf6f6 0%, #f8f8f8 60%);
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06);

      .trade_in_title {
        display: flex;
        justify-content: space-between;
        padding-right: 18rpx;
        box-sizing: border-box;

        .title_ {
          font-family: Alibaba Sans;
          font-size: 32rpx;
          font-weight: 600;
          line-height: 28rpx;
          color: #3d3d3d;
          margin-right: 8rpx;
          //   width: 160rpx;
        }

        .title_tag {
          width: 130rpx;
          height: 32rpx;
          border-radius: 8rpx 0px 8rpx 0px;
          background: linear-gradient(90deg, #f87c48 0%, #ed3743 109%);
          font-family: Alibaba Sans;
          font-size: 20rpx;
          font-weight: normal;
          line-height: 32rpx;
          color: #ffffff;
          text-align: center;
        }
      }

      .trade_in_goods {
        // min-width: 726rpx;
        // height: 210rpx;
        margin-top: 20rpx;

        // box-shadow: 0px 4rpx 10rpx 0px rgba(0, 0, 0, 0.04);
        .goods_item {
          width: 194rpx;
          //   height: 210rpx;
          border-radius: 8rpx;
          background: #ffffff;
          padding: 16rpx 18rpx 14rpx;
          box-sizing: border-box;
          text-align: center;
          margin-left: 15rpx;

          .goods_title {
            font-family: Alibaba Sans;
            font-size: 20rpx;
            font-weight: 500;
            line-height: 32rpx;
            color: #3d3d3d;
            width: 160rpx;
          }

          .price {
            font-family: Alibaba Sans;
            font-size: 28rpx;
            font-weight: normal;
            line-height: 32rpx;
            color: #ea1306;
          }

          .add_cart {
            width: 40rpx;
            height: 40rpx;
            background: linear-gradient(148deg, #f9847c -5%, #fa372b 109%);
            border-radius: 50%;
            color: #ffffff;
            text-align: center;
            line-height: 40rpx;
          }
        }
      }
    }

    .cart_content {
      .cart_item {
        padding: 0 10rpx 20rpx 18rpx;

        .items {
          background: linear-gradient(180deg, #ffefef 0%, #fefefe 100%);
          border: 1px solid #e4c094;
          padding: 40rpx 20rpx 30rpx;
          border-radius: 22rpx;
          margin-bottom: 15rpx;
        }

        .cart_img {
          position: relative;
          background: #edf3fa;
          width: 158rpx;
          height: 136rpx;
          border-radius: 8rpx;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 20rpx;
        }

        .cart_title {
          display: flex;
          flex-wrap: wrap;
          align-content: space-between;
          width: 440rpx;

          .title_ {
            display: flex;
            width: 440rpx;
            font-family: Alibaba Sans;
            font-weight: 400;
            font-size: 28rpx;
            color: #4e5969;
          }
        }

        .buy_nums {
          height: 36rpx;
          border-radius: 8rpx;
          background: #fcf1f5;
          box-sizing: border-box;
          border: 1px solid #ea1306;
          font-family: Alibaba Sans;
          font-size: 20rpx;
          font-weight: normal;
          line-height: 34rpx;
          text-align: center;
          color: #ea1306;
          padding: 0 10rpx;
          margin-bottom: 10rpx;
        }

        .cart_num {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;

          // margin-bottom: 14rpx;
          .price {
            font-family: Alibaba Sans;
            font-size: 32rpx;
            font-weight: 600;
            color: #e91306;
            line-height: 44rpx;
          }
        }

        .num {
          height: 44rpx;
          border: 1px solid #e5e5e5;
          border-radius: 8rpx;
          //   box-sizing: border-box;
          display: flex;
          display: -webkit-flex;
          align-items: center;
          text-align: center;
        }

        .jian {
          display: block;
          width: 34rpx;
          height: 50rpx;
          line-height: 50rpx;
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
          //   border: 1px solid #e5e5e5;
        }

        .nums {
          display: block;
          height: 40rpx !important;
          line-height: 40rpx;
          font-size: 28rpx;
          color: #007afc;
          width: 66rpx;
          background: #fff;
          border: 1px solid #e5e5e5;
          border-radius: 6rpx;
        }
      }
    }
  }

  // 弹出层
  .popup_content {
    width: 750rpx;
    height: 768rpx;
    border-radius: 40rpx 40rpx 0px 0px;
    background: #ffffff;
    padding-top: 40rpx;
    background: #ea1306;

    .popup_title {
      font-family: Alibaba Sans;
      font-size: 36rpx;
      line-height: 36rpx;
      color: #fff;
      text-align: center;
      border-bottom: 1px solid #f2f3f5;
      padding-bottom: 24rpx;
    }

    .popup_details {
      padding: 20rpx;
      height: 690rpx;
      box-sizing: border-box;
      background-color: #f8f8f8;

      .details {
        display: flex;
        justify-content: space-between;
        font-family: Alibaba Sans;
        font-size: 30rpx;
        font-weight: normal;
        line-height: 36rpx;
        color: #333333;
        margin-bottom: 30rpx;

        .distribution {
          display: inline-block;
          height: 32rpx;
          border-radius: 8rpx;
          box-sizing: border-box;
          border: 1px solid #999999;
          font-family: Alibaba Sans;
          font-size: 28rpx;
          font-weight: normal;
          line-height: 30rpx;
          color: #4e5969;
          padding: 0 14rpx;
          margin-left: 8rpx;
        }
      }

      .total_ {
        display: flex;
        justify-content: space-between;
        font-family: Alibaba Sans;
        font-size: 32rpx;
        font-weight: normal;
        line-height: 28rpx;
        color: #333333;
        border-top: 1px solid #f2f3f5;
        padding-top: 30rpx;
        padding-bottom: 15rpx;
      }
    }
  }

  // 管理 or 结算
  .manage {
    width: 750rpx;
    height: 124rpx;
    border-radius: 20rpx 20rpx 0px 0px;
    background: #ffffff;
    box-shadow: 0px 8rpx 20rpx 0px rgba(0, 0, 0, 0.1);
    position: fixed;
    // bottom: calc( env(safe-area-inset-bottom));
    bottom: 0;
    z-index: 999;
    padding: 30rpx 20rpx;
    box-sizing: border-box;

    .check_btn {
      font-family: Alibaba Sans;
      font-size: 30rpx;
      font-weight: normal;
      line-height: 64rpx;
      text-align: center;
      color: #999999;
      display: flex;
      //   align-items: flex-start;
    }

    .total_text {
      font-family: Alibaba Sans;
      font-size: 30rpx;
      font-weight: normal;
      line-height: 30rpx;
      color: #3d3d3d;

      .preferential_btn {
        display: inline-block;
        font-family: Alibaba Sans;
        font-size: 20rpx;
        font-weight: normal;
        line-height: 28rpx;
        color: #fa5349;
        text-align: center;
        height: 28rpx;
        border-radius: 8rpx;
        background: #fcf1f5;
        padding: 0 16rpx;
        margin-left: 8rpx;
      }
    }

    .manage_btn {
      height: 64rpx;
      border-radius: 80rpx;
      box-sizing: border-box;
      border: 1px solid #f2f3f5;
      padding: 0 34rpx;
      text-align: center;
      line-height: 64rpx;
      margin-left: 16rpx;
      font-family: Alibaba Sans;
      font-size: 28rpx;
      font-weight: normal;
      color: #3d3d3d;
    }
  }

  // 空页面
  .empty {
    padding: 116rpx 158rpx 0 170rpx;

    .empty_text {
      text-align: center;
      font-family: PingFang SC;
      font-size: 32rpx;
      font-weight: normal;
      line-height: 48rpx;
      color: #4e5969;
      margin-top: 40rpx;
    }

    .cart_btn {
      width: 216rpx;
      height: 84rpx;
      border-radius: 40rpx;
      background: #ffffff;
      box-sizing: border-box;
      border: 1px solid #ea1306;
      font-family: PingFang SC;
      font-size: 32rpx;
      text-align: center;
      color: #ea1306;
      line-height: 84rpx;
      margin-top: 44rpx;
      margin-bottom: 36rpx;
    }
  }

  //为你推荐
  .Recommended {
    padding: 60rpx 32rpx 0 44rpx;

    .restoratives {
      position: relative;
      width: 330rpx;
      height: 394rpx;
      border-radius: 16rpx;
      background: #feffff;
      padding: 66rpx 14rpx 24rpx 14rpx;
      box-sizing: border-box;
      margin-bottom: 22rpx;
      margin-right: 14rpx;

      .tag {
        position: absolute;
        top: 0;
        left: 0;
        width: 70rpx;
        height: 36rpx;
        border-radius: 8rpx 0px 8rpx 0px;
        background: #0b78ff;
        font-family: Source Han Sans;
        font-size: 28rpx;
        font-weight: normal;
        line-height: 36rpx;
        text-align: center;
        color: #ffffff;
      }

      .text_title {
        font-family: Alibaba Sans;
        font-size: 28rpx;
        font-weight: normal;
        line-height: normal;
        color: #3d3d3d;
        line-height: 38rpx;
        margin-bottom: 30rpx;
      }

      .price {
        text-align: left;
        font-family: Alibaba Sans;
        font-size: 28rpx;
        font-weight: normal;
        line-height: 44rpx;
        color: #ea1306;
      }

      .add_cart {
        width: 40rpx;
        height: 40rpx;
        background: linear-gradient(148deg, #f9847c -5%, #fa372b 109%);
        border-radius: 50%;
        color: #ffffff;

        text-align: center;
        line-height: 40rpx;
      }
    }

    .restoratives:nth-of-type(2n) {
      margin-right: 0;
    }
  }

  .single-line-text {
    white-space: nowrap;
    /* 防止换行 */
    overflow: hidden;
    /* 隐藏超出部分 */
    text-overflow: ellipsis;
    /* 使用省略号表示超出部分 */
    width: 400rpx;
    /* 设置宽度，可以根据需求调整 */
    font-weight: 600;
  }

  /deep/.uni-section {
    border-radius: 8rpx;
  }

  /deep/.uni-section .uni-section-header__decoration {
    background-color: #ea1306;
  }

  /deep/.uni-swipe_button {
    padding: 0 20rpx;
  }

  /deep/.uni-section__content-title {
    font-size: 32rpx !important;
  }

  /deep/.uni-swipe_button {
    width: 100rpx;
  }
}