page {
    background-color: #f9f9f9;
  }
  
  .header {
    background-color: #fff;
    padding-bottom: 10px;
  }
  
  .uni-searchbar__box {
    border: 0.5px solid #E6E6E6;
    border-radius: 50rpx !important;
  }
  
  .my_select {
    display: flex;
    justify-content: flex-end;
    margin-right: 20px;
  
  }
  
  .uni-select .text {
    color: red;
  }
  
  .uni-select {
    background-color: #fff;
    height: 39px !important;
    border: 0 !important;
  }
  
  .content {
    width: 96%;
    margin: 10px auto;
  }
  
  .nav {
    padding: 20px 10px 10px 10px;
    position: relative;
    margin: 10px 0;
    border-radius: 5px;
    background-color: #fff;
    box-shadow: 1px 1px 1px 0px #cccccc;
  }
  
  .canState {
    position: absolute;
    top: 0;
    left: 0;
    color: #fff;
    padding: 2px 4px;
    font-size: 12px;
    border-radius: 5px 0 5px 0;
    background-color: #007afc;
  }
  
  .nocanState {
    position: absolute;
    top: 0;
    left: 0;
    color: #fff;
    padding: 2px 4px;
    font-size: 12px;
    border-radius: 5px 0 5px 0;
    background-color: #00b1ff;
  }
  
  .doctorDetail {
    display: flex;
  }
  
  .avatar {
    display: block;
    width: 100rpx;
    height: 110rpx;
    border-radius: 50%;
    margin-right: 10rpx;
    background: #fff;
  }
  
  .dotInfo {
    margin: 5px 0 0 10px;
    width: 320px;
  }
  
  .dotName {
    display: flex;
  }
  
  .rico {
    display: block;
    width: 14rpx;
    height: 21rpx;
    margin-left: auto;
  }
  
  .dotIntro {
    margin: 5px 0;
    font-size: 12px;
    display: flex;
  }
  
  .dotMoney {
    font-size: 12px;
  }
  
  .Copay {
    font-weight: 600;
  }
  
  .c9 {
    color: #999999;
  }
  
  .inquiryState0 {
    color: #007AFC;
  }
  
  .inquiryState1 {
    color: #00B1FF;
  }