.consultation-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 180rpx;
}

.tip-box {
  margin: 20rpx;
  border-radius: 16rpx;
  background: #fff;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);
  overflow: hidden;

  .tip-content {
    background: rgba(255, 77, 79, 0.05);
    color: #333;
    padding: 24rpx;
    font-size: 28rpx;
    display: flex;
    align-items: center;

    .tip-icon {
      color: #ff4d4f;
      font-weight: bold;
      margin-right: 12rpx;
      font-size: 32rpx;
    }
  }
}

.risk-notice {
  margin: 20rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #666;
  background: #fff;
  border-radius: 16rpx;
  line-height: 1.6;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);

  .link {
    color: #ff4d4f;
    text-decoration: underline;
  }
}

.form-section {
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.03);

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    position: relative;
    padding-left: 24rpx;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6rpx;
      height: 28rpx;
      background: #ff4d4f;
      border-radius: 3rpx;
    }
  }
}

.user-info {
  align-items: center;
  padding: 24rpx;
  background: #fafafa;
  border-radius: 12rpx;

  .gender-age {
    color: #666;
    font-size: 28rpx;
    margin-right: 20rpx;
  }

  .main-user {
    color: #fff;
    background: linear-gradient(135deg, #ff4d4f, #ff7875);
    padding: 4rpx 16rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
  }

  .arrow {
    margin-left: auto;
    color: #999;
    font-family: "system-ui";
  }
}

.medicine-list {
  .medicine-item {
    position: relative;
    display: flex;
    padding: 24rpx;
    background: #fafafa;
    border-radius: 12rpx;
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .medicine-img {
      width: 160rpx;
      height: 160rpx;
      margin-right: 24rpx;
      border-radius: 8rpx;
    }

    .medicine-info {
      flex: 1;

      .medicine-name {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 12rpx;
        line-height: 1.4;
      }

      .medicine-spec {
        color: #999;
        font-size: 24rpx;
        margin-bottom: 16rpx;
      }

      .medicine-price {
        color: #ff4d4f;
        font-size: 32rpx;
        font-weight: 600;
      }
    }

    .medicine-count {
      color: #666;
      font-size: 28rpx;
      padding-left: 24rpx;
    }
  }
}

.prescription-form {
  .main-input {
    height: 88rpx;
    border: 2rpx solid #eee;
    border-radius: 12rpx;
    padding: 0 24rpx;
    margin-bottom: 32rpx;
    font-size: 28rpx;
    transition: all 0.3s;

    &:focus {
      border-color: #ff4d4f;
    }
  }

  .checkbox-group {
    margin-bottom: 32rpx;

    .checkbox-title {
      color: #333;
      font-size: 28rpx;
      margin-bottom: 20rpx;
      font-weight: 500;
    }

    .search-box {
      display: flex;
      gap: 20rpx;

      input {
        flex: 1;
        height: 88rpx;
        border: 2rpx solid #eee;
        border-radius: 12rpx;
        padding: 0 24rpx;
        font-size: 28rpx;
        transition: all 0.3s;

        &:focus {
          border-color: #ff4d4f;
        }
      }

      .search-btn {
        width: 180rpx;
        height: 88rpx;
        line-height: 88rpx;
        background: linear-gradient(135deg, #ff4d4f, #ff7875);
        color: #fff;
        font-size: 28rpx;
        border-radius: 12rpx;
        border: none;
      }
    }
  }
}

.question-list {
  .question-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 24rpx;
    border-bottom: 2rpx solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .radio-group {
      display: flex;
      gap: 48rpx;

      .radio {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        color: #666;

        radio {
          transform: scale(0.8);
          margin-right: 8rpx;
        }
      }
    }
  }
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 2;
  .agreement {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    font-size: 24rpx;
    margin-bottom: 24rpx;
    color: #666;

    checkbox {
      transform: scale(0.8);
      margin-right: 8rpx;
    }

    .link {
      color: #ff4d4f;
      margin: 0 4rpx;
    }
  }

  .submit-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background: linear-gradient(135deg, #ff4d4f, #ff7875);
    color: #fff;
    font-size: 32rpx;
    border-radius: 44rpx;
    border: none;
    font-weight: 500;
    box-shadow: 0 8rpx 16rpx rgba(255, 77, 79, 0.2);
    transition: all 0.3s;

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 4rpx 8rpx rgba(255, 77, 79, 0.2);
    }
  }
}

/* 添加 uni-data-checkbox 相关样式 */
.question-list {
  .question-item {
    .radio-group {
      :deep(.uni-data-checklist) {
        .checklist-group {
          display: flex;
          gap: 24rpx;

          .checklist-box {
            margin: 0;
            padding: 0;
          }

          .checklist-button {
            width: 120rpx;
            height: 64rpx;
            line-height: 64rpx;
            text-align: center;
            border: 2rpx solid #eee;
            border-radius: 8rpx;
            font-size: 28rpx;
            color: #666;
            background: #fff;

            &.is-checked {
              background: #ff4d4f;
              border-color: #ff4d4f;
              color: #fff;
            }
          }
        }
      }
    }
  }
}

/* 添加禁用按钮样式 */
.submit-btn-disabled {
  background: #ccc !important;
  box-shadow: none !important;

  &:active {
    transform: none !important;
  }
}

.add-user {
  color: #999;
  font-size: 28rpx;
}

.empty-user {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .add-user {
    color: #999;
    font-size: 28rpx;
  }

  .arrow {
    color: #999;
  }
}
.msg {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  z-index: 999;
}
