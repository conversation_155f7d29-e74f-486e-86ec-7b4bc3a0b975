<template>
  <view class="search-page">
    <!-- 搜索框 -->
    <view class="search-header">
      <view class="search-box">
        <text class="iconfont">&#xe607;</text>
        <input type="text" v-model="keyword" placeholder="请输入商品名称" confirm-type="search" @input="handleInput"
          @confirm="handleSearch" focus />
        <text class="clear-btn" v-if="keyword" @click="clearKeyword">✕</text>
      </view>
      <text class="search-btn" @click="handleSearch">搜索</text>
    </view>

    <!-- 搜索建议列表 -->
    <view class="suggestion-list" v-if="keyword && suggestionList.length">
      <view class="suggestion-item" v-for="item in suggestionList" :key="item.id" @click="selectSuggestion(item)">
        <rich-text :nodes="highlightKeyword(item.goodsName)"></rich-text>
      </view>
    </view>

    <!-- 搜索历史 -->
    <view class="history-section" v-if="!keyword && searchHistory.length">
      <view class="section-header">
        <text class="title">搜索历史</text>
        <text class="clear-history" @click="clearHistory">
          <text class="iconfont">&#xe6a0;</text>
          清空历史
        </text>
      </view>
      <view class="history-list">
        <view class="history-item" v-for="(item, index) in searchHistory" :key="index" @click="useHistoryKeyword(item)">
          {{ item }}
        </view>
      </view>
    </view>

    <!-- 热门搜索 -->
    <view class="hot-section" v-if="!keyword">
      <view class="section-header">
        <text class="title">热门搜索</text>
      </view>
      <view class="hot-list">
        <view class="hot-item" v-for="(item, index) in hotKeywords" :key="index" :class="{ 'hot': index < 3 }"
          @click="useHistoryKeyword(item.productName)">
          <text v-if="index < 3" class="iconfont hot-icon">&#xe757;</text>
          <text>{{ item.productName }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const req = require('../../utils/request.js')
export default {
  data() {
    return {
      keyword: '',
      searchHistory: [],
      hotKeywords: [],
      suggestionList: [],
      timer: null,
    }
  },
  onLoad() {
    // 获取本地存储的搜索历史
    if (req.getStorage('searchHistory')) {
      this.searchHistory = JSON.parse(req.getStorage('searchHistory'))
    }
    this.getHotKeywords()
  },
  methods: {
    handleInput(e) {
      if (this.timer) {
        clearTimeout(this.timer)
      }

      if (!e.detail.value.trim()) {
        this.suggestionList = [];
        return;
      }

      this.timer = setTimeout(() => {
        req.getRequest("/shopApi/home/<USER>", {
          title: e.detail.value,
          storeId: req.getStorage("currentStore").id,
          pageNum: 1,
          pageSize: 8
        }, res => {
          if (res.code === 200 && res.data.list) {
            this.suggestionList = res.data.list;
          }
        })
      }, 500)
    },
    //热门标签
    getHotKeywords() {
      req.getRequest("/shopApi/shopback/search/list", {
        pageNum: 1,
        pageSize: 10,
        storeId: req.getStorage("currentStore").id ? req.getStorage("currentStore").id : 60,
      }, res => {
        this.hotKeywords = res.data.items
      })
    },
    onUnload() {
      if (this.timer) {
        clearTimeout(this.timer)
      }
    },
    // 处理搜索
    handleSearch() {
      if (!this.keyword.trim()) return

      // 保存搜索历史
      this.saveHistory(this.keyword)

      // 跳转到商品列表页并传递关键词
      uni.navigateTo({
        url: `/product/list/list?searchTitle=${this.keyword}`
      })
    },

    // 清空关键词
    clearKeyword() {
      this.keyword = '';
      this.suggestionList = [];
    },

    // 使用历史关键词
    useHistoryKeyword(keyword) {
      this.keyword = keyword
      this.handleSearch()
    },

    // 保存搜索历史
    saveHistory(keyword) {
      const index = this.searchHistory.indexOf(keyword)
      if (index > -1) {
        this.searchHistory.splice(index, 1)
      }
      this.searchHistory.unshift(keyword)

      // 限制历史记录数量
      if (this.searchHistory.length > 10) {
        this.searchHistory.pop()
      }

      // 保存到本地存储
      req.setStorage('searchHistory', JSON.stringify(this.searchHistory))
    },

    // 清空搜索历史
    clearHistory() {
      uni.showModal({
        title: '提示',
        content: '确定清空搜索历史吗？',
        success: (res) => {
          if (res.confirm) {
            this.searchHistory = []
            req.removeStorage('searchHistory')
          }
        }
      })
    },

    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 添加选择建议项方法
    selectSuggestion(item) {
      this.keyword = item.goodsName;
      this.handleSearch();
    },

    // 添加高亮处理方法
    highlightKeyword(text) {
      if (!this.keyword) return text;

      const keyword = this.keyword.trim();
      const reg = new RegExp(keyword, 'gi');
      return text.replace(reg, match => `<span style="color: #EA1306">${match}</span>`);
    },
  }
}
</script>

<style lang="scss" scoped src="./search.scss"></style>