<template>
    <view class="product-detail">
        <!-- 商品轮播图 -->
        <swiper class="swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000">
            <swiper-item v-for="(item, index) in productInfo.bannerImgs" :key="index">
                <image :src="item" mode="aspectFill"></image>
            </swiper-item>
        </swiper>

        <!-- 商品信息 -->
        <view class="info-section">
            <!-- 分类 -->
            <view class="sort" v-if="productInfoList.child">
                <view class="scroll-content">
                    <view v-for="item in productInfoList.child" :key="item.id" class="sort_item"
                        :class="(activeSort == item.id ? 'active_sort_item' : '')" @click.stop="toggleSort(item)">
                        {{ item.goodsName }}
                    </view>
                </view>
            </view>
            <!-- 普通商品显示原价 -->
            <view class="price">
                <text class="symbol">¥</text>
                <text class="number">{{ productInfoList.comPrice }}</text>
                <text class="original">¥{{ productInfoList.salePrice }}</text>
                <text class="tag-text">组合优惠</text>
            </view>
            <view class="title-row">
                <view class="title">{{ productInfoList.name }}</view>
                <button class="share-button" open-type="share">
                    <text class="iconfont">&#xe626;</text>
                    <text>分享</text>
                </button>
            </view>
        </view>

        <!-- 将三个部分放在一个容器内 -->
        <view class="info-blocks" @click="showMedicationGuide">

            <!-- 主治功能 -->
            <view class="info-block">
                <view class="block-title">主治功能</view>
                <view class="block-content">
                    {{ productInfo.fi ? productInfo.fi : '暂无信息' }}
                </view>
            </view>

            <!-- 用法用量 -->
            <view class="info-block">
                <view class="block-title">用法用量</view>
                <view class="block-content">
                    {{ productInfo.usages ? productInfo.usages : '' }}{{ productInfo.mdf ? ',' + productInfo.mdf : ''
                    }}{{
                        productInfo.sdMed ? ',' + productInfo.sdMed : '' }}
                </view>
            </view>

            <!-- 储存方式 -->
            <view class="info-block">
                <view class="block-title">储存方式</view>
                <view class="block-content">
                    {{ productInfo.stoCond ? productInfo.stoCond : '暂无信息' }}
                </view>
            </view>
        </view>

        <!-- 商品详情富文本 -->
        <view class="detail-content">
            <rich-text :nodes="formatRichText(productInfo.goodsDetail)" class="rich-text"></rich-text>
        </view>
        <!-- 使用购买弹窗组件 -->
        <buy-popup ref="buyPopup" :product="{
            image: productInfo.img,
            price: productInfoList.comPrice,
            maxBuy: productInfoList.maxBuy
        }" :is-cart-action="isCartAction" :cloud-stock="productInfoList.warehouseStock"
            :store-stock="productInfoList.sellStock" @confirm="handleBuyConfirm" />

        <!-- 底部操作栏 -->
        <view class="bottom-bar">
            <view class="left">
                <view class="icon-btn" @click="contactService">
                    <text class="iconfont">&#xec2e;</text>
                    <text>客服</text>
                </view>
                <view class="icon-btn" @click="goToCart">
                    <text class="iconfont">&#xe635;</text>
                    <text>购物车</text>
                </view>
            </view>
            <view class="right">
                <view class="add-cart" @click="addToCart">加入购物车</view>
                <view class="buy-now" @click="buyNow">立即购买</view>
            </view>
        </view>

        <!-- 添加用药指导弹窗 -->
        <uni-popup ref="medicationGuidePopup" type="bottom">
            <view class="medication-guide-popup">
                <view class="popup-title">
                    用药指导
                    <text class="close-btn" @click="closeMedicationGuide">×</text>
                </view>
                <scroll-view scroll-y class="popup-content" :style="{ maxHeight: '60vh' }">
                    <view class="guide-item">
                        <view class="item-title">不良反应</view>
                        <view class="item-content">{{ productInfo.adr || '暂无信息' }}</view>
                    </view>
                    <view class="guide-item">
                        <view class="item-title">禁忌</view>
                        <view class="item-content">{{ productInfo.taboo || '暂无信息' }}</view>
                    </view>
                    <view class="guide-item">
                        <view class="item-title">注意事项</view>
                        <view class="item-content">{{ productInfo.mna || '暂无信息' }}</view>
                    </view>
                    <view class="guide-item">
                        <view class="item-title">规格</view>
                        <view class="item-content">{{ productInfo.doseSpec || '暂无信息' }}</view>
                    </view>
                    <view class="guide-item">
                        <view class="item-title">批准文号</view>
                        <view class="item-content">{{ productInfo.appNum || '暂无信息' }}</view>
                    </view>
                    <view class="guide-item">
                        <view class="item-title">生产厂家</view>
                        <view class="item-content">{{ productInfo.factory || '暂无信息' }}</view>
                    </view>
                </scroll-view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import BuyPopup from '@/components/buy-popup/buy-popup.vue'
const app = getApp()
const req = require("../../utils/request")
export default {
    components: {
        BuyPopup
    },
    data() {
        return {
            id: '',
            currentSpec: 0,
            productInfo: {
            },
            // 评价相关数据
            reviewInfo: {
                total: 128,
                goodRate: 98,
                avgScore: 4.8,  // 添加平均分
                tags: [
                    { name: '全部', count: 128, active: true },
                    { name: '好评', count: 125, active: false },
                    { name: '中评', count: 2, active: false },
                    { name: '差评', count: 1, active: false },
                    { name: '有图', count: 30, active: false }
                ]
            },
            isCartAction: true, // 是否为加入购物车操作
            cloudStock: 999, // 云仓库存
            storeStock: 0, // 门店库存
            isSeckill: false, // 是否为秒杀商品
            seckillStatus: '', // 秒杀状态：notStarted-未开始，ongoing-进行中，ended-已结束
            hours: '00',
            minutes: '00',
            seconds: '00',
            timer: null,
            type: null, // type2-秒杀商品，type4-新人商品
            endTime: null, // 秒杀结束时间
            timer: [],
            bannerImgs: [],
            productInfoList: [],
            activeSort: '',
        }
    },
    onLoad(options) {
        if (options.id) {
            this.id = options.id
            this.type = Number(options.type)
            this.getData()
        }
    },
    onUnload() {
        if (this.timer) {
            clearInterval(this.timer)
        }
    },
    methods: {
        // 切换分类
        toggleSort(val) {
            this.activeSort = val.id
            this.productInfo = val
            this.productInfo.bannerImgs = typeof val.bannerImgs === 'string'
                ? val.bannerImgs.split(',')
                : val.bannerImgs
        },
        goToEvents(val) {
            console.log('val-------', val)
            if (val.actType == 5) {
                uni.navigateTo({
                    url: '/product/timeDiscount/timeDiscount'
                })
            }
        },
        // 获取商品详情
        getData() {
            req.getRequest('/shopApi/shopApi/activity/getDetails', {
                id: this.id,
                merchantId: 2,
                type: this.type
            }, res => {
                if (res.data.child.length > 0) {
                    this.productInfo = res.data.child[0]
                    this.productInfo.bannerImgs = typeof res.data.child[0].bannerImgs === 'string'
                        ? res.data.child[0].bannerImgs.split(',')
                        : res.data.child[0].bannerImgs
                    this.activeSort = res.data.child[0].id
                }
                this.productInfoList = res.data
            })
        },
        // 秒杀购买
        seckillBuy() {
            this.openBuyPopup(false)
        },
        // 选择规格
        selectSpec(index) {
            this.currentSpec = index
        },
        // 联系客服
        contactService() {
            uni.showToast({
                title: '正在接入客服...',
                icon: 'none'
            })
        },
        // 跳转购物车
        goToCart() {
            uni.switchTab({
                url: '/pages/cart/cart'
            })
        },
        // 加入购物车
        addToCart() {
            this.openBuyPopup(true)
        },
        // 立即购买
        buyNow() {
            this.openBuyPopup(false)
        },
        // 打开购买弹窗
        openBuyPopup(isCart) {
            this.isCartAction = isCart
            this.$refs.buyPopup.open()
        },

        // 处理购买确认
        handleBuyConfirm({ quantity, deliveryType, isCartAction }) {
            // 秒杀商品需要传递额外参数
            req.postRequest("/shopApi/purchase/cart", {
                skuId: null,
                merchantId: deliveryType == 1 ? req.getStorage("currentStore").id : 60,
                quantity,
                // productId: this.productInfo.id,
                mode: deliveryType,
                state: 0,
                actId: this.productInfoList.actId,
                activityType: this.productInfoList.actType
            }, res => {
                req.msg('加入购物车成功')
            })
        },

        // 格式化富文本内容
        formatRichText(html) {
            if (!html) return '';
            // 处理图片宽度
            return html.replace(/<img/gi, '<img style="width:100%;height:auto;display:block;"');
        },

        // 显示用药指导弹窗
        showMedicationGuide() {
            this.$refs.medicationGuidePopup.open()
        },

        // 关闭用药指导弹窗
        closeMedicationGuide() {
            this.$refs.medicationGuidePopup.close()
        },

        // 添加分享配置
        onShareAppMessage(res) {
            return {
                title: this.productInfo.goodsName,
                path: `/product/detail/detail?id=${this.id}&type=${this.type}`,
                imageUrl: this.productInfo.img,
                success(res) {
                    uni.showToast({
                        title: '分享成功',
                        icon: 'success'
                    })
                },
                fail(res) {
                    uni.showToast({
                        title: '分享失败',
                        icon: 'none'
                    })
                }
            }
        },
    }
}
</script>

<style lang="scss" scoped src="./assemblyDetail.scss"></style>