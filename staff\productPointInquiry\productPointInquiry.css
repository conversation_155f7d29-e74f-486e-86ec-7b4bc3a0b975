.go_back {
  position: absolute;
  top: 0;
  left: 0;
}

.top {
  background-color: #fff;
  padding: 15px 10px 0;
  flex-shrink: 0;
}

.searchs {
  display: flex;
  background: #fff;
  height: 70rpx;
  line-height: 70rpx;
  border-radius: 35rpx;
  padding: 0 20rpx 0 30rpx;
  align-items: center;
  /* width: 90%; */
  font-size: 28rpx;
  border: 1px solid #E6E6E6;
  margin: auto;
  margin-bottom: 10px;
}

.ssico {
  width: 29rpx;
  height: 32rpx;
  margin-right: 14rpx;
}

.middle {
  background-color: #fff;
  padding: 10px 0;
  flex-shrink: 0;
  height: auto;
}

.horizontal-scroll {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 0 10px;
}

.hidden-overflow {
  overflow: hidden;
}

.scroll-item {
  width: 20%;
  min-width: 20%;
  text-align: center;
  box-sizing: border-box;
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 110px;
  font-size: 14px;
  padding: 0 5px;
}

.scroll-item img {
  width: 48px;
  height: 48px;
  margin-bottom: 8px;
}

.scroll-item-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 1.2;
}

.scroll-item-text .name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.scroll-item-text .size {
  font-size: 12px;
  color: #666;
}

.more {
  display: none;
}

.bottom {
  height: 58px;
  line-height: 58px;
  background-color: #fff;
  position: fixed;
  bottom: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  z-index: 999;
}

.mini-btn {
  border: 0 !important;
  background-color: #0b78ff !important;
  color: #fff !important;
}

.content {
  display: flex;
  flex: 1;
  overflow: hidden;
  padding-bottom: 58px;
}

.con_left {
  background-color: #fff;
  padding: 0 2px;
  width: 100px;
  margin-right: 10px;
  text-align: center;
  overflow-y: auto;
  height: calc(100vh - 250px);
  flex-shrink: 0;
}

.con_left .li {
  line-height: 90rpx;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  transition: 0.5s;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 10px;
  min-height: 45px;
}

.con_left::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

.con_left .item2 .item_name {
  font-size: 14px;
}

.addCart2 {
  width: 20px;
  height: 20px;
}

.addCart2 image {
  display: block;
}

.item2 {
  width: 80%;
}

.badge {
  position: absolute;
  width: 20px;
  height: 20px;
  top: 5px;
  right: 2px;
  background-color: #0B78FF;
  color: #fff;
  border-radius: 50%;
  font-size: 12px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.con_left .li.active {
  font-weight: bold;
  color: #0173FF;
  background: #EDF3FA;
}

.con_right {
  flex: 1;
  overflow: hidden;
  height: calc(100vh - 250px);
}

.con_right .li {
  /* width: 10%; */
  padding: 0 5px;
}

.right_top {
  background-color: #fff;
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  overflow: hidden;
}

.right_topTitle {
  width: 100%;
  display: flex;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}


.right_topTitle .li.active {
  font-weight: bold;
  color: #0173FF;
  background: #EDF3FA;
}

.right_bottom {
  margin-top: 5px;
  background-color: #fff;
  flex: 1;
  overflow: hidden;
}

.tab .li {
  text-align: center;
  width: 33%;
  display: inline-block;
  /* padding-right: 25rpx; */
  line-height: 70rpx;
  transition: 0.3s;
}

.tab .li:last-child {
  margin-right: 0;
}

.tab .li.active {
  color: var(--mina) !important;
  background-color: #fff;
  border-radius: 10rpx;
}

.sort {
  padding: 29rpx 0 0 20rpx;
  box-sizing: border-box;
  height: 100%;
  overflow-y: auto;
}

.price .vip {
  font-size: 24rpx;
  margin-left: 12rpx;
  color: var(--vip);
  border: 1rpx solid;
  padding: 0 8rpx 2rpx 8rpx;
  border-radius: 5px;
  font-weight: normal;
}

.price .vip .members {
  font-size: 24rpx !important;
  font-weight: normal !important;
  margin-left: 2rpx;
}

.price .vip .sale {
  font-size: 24rpx !important;
  font-weight: normal !important;
}

.price text.del {
  font-weight: normal;
  font-size: 24rpx;
  color: #999;
  margin-left: 12rpx;
  text-decoration: line-through;
}

.sort .li {
  /* border-bottom: 2rpx solid #eee; */
  padding-bottom: 39rpx;
  margin-bottom: 39rpx;
  overflow: hidden;
  padding-right: 20rpx;
  display: flex;
}

.sort .li:last-child {
  margin-bottom: 0;
}

.sort .li .flex {
  overflow: hidden;
}

.proimgs {
  /* flex: 1; */
  border-radius: 10rpx;
  position: relative;
  margin-right: 18rpx;
}

.proimg {
  width: 140rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
}

.shouqing {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  position: absolute;
  top: 0;
  left: 0;
}

.proname {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
  /* text-overflow: ellipsis;
      white-space: nowrap; */
  line-height: 36rp;
  /* height: 76rpx; */
  overflow: hidden;
  margin-bottom: 10rpx;
}

.pronames {
  font-size: 24rpx;
  color: #999;
  margin-top: 5rpx;
}

.endtime {
  display: inline-block;
  height: 32rpx;
  line-height: 32rpx;
  border: 2rpx solid #fe0419;
  border-radius: 6rpx;
  padding: 0 8rpx;
  font-size: 24rpx;
  color: #fe0419;
  margin: 15rpx 0 65rpx;
}

.operate {
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
  display: flex;
  align-items: center;
}

.price {
  font-size: 24rpx;
  color: #fe0419;
  align-items: center;
  display: flex;
}

.price text {
  font-size: 36rpx;
  font-weight: bold;
}

.add-cart image {
  width: 42rpx;
}

.box {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}