.pickupList {
  background-color: #f8f8f8;
  height: 100vh;
  .search {
    display: flex;
    background-color: #fff;
    padding: 34rpx;
    margin-bottom: 20rpx;
    .picker {
      justify-content: center;
      align-items: center;
      border: 1px solid #ea1306;
      width: 176rpx;
      height: 72rpx;
      border-radius: 20rpx;
      text-align: center;
      line-height: 72rpx;
      font-family: Alibaba Sans;
      font-size: 28rpx;
      font-weight: 600;
      color: #4e5969;
      box-sizing: border-box;
    }
    .search_input {
      position: relative;
      display: flex;
      .input_ {
        // width: 300rpx;
        font-size: 28rpx;
      }
      .search_btn {
        position: absolute;
        right: -10rpx;
        width: 158rpx;
        height: 72rpx;
        border-radius: 40rpx;
        background: #ea1306;
        font-family: Alibaba Sans;
        font-size: 28rpx;
        font-weight: 500;
        text-align: center;
        line-height: 72rpx;
        color: #ffffff;
      }
    }
  }
  .orientation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 20rpx 40rpx;
    width: 710rpx;
    height: 76rpx;
    border-radius: 16rpx;
    background: #ffffff;
    font-family: PingFang SC;
    font-size: 24rpx;
    font-weight: 600;
    line-height: 76rpx;
    color: #4e5969;
    padding: 0 28rpx;
    box-sizing: border-box;
  }
  .current {
    width: 710rpx;
    height: 174rpx;
    background: #ffffff;
    padding: 26rpx 40rpx;
    box-sizing: border-box;
    display: flex;
    border-radius: 16rpx;
    .text_ {
      font-family: Alibaba Sans;
      font-size: 28rpx;
      font-weight: 600;
      line-height: 48rpx;
      //   text-align: center;
      color: #4e5969;
    }
  }
  /deep/.uni-section-header {
    background-color: #f9f9f9;
  }
  /deep/.uni-section .uni-section-header__decoration {
    background-color: #ea1306;
  }
}
