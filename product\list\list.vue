<template>
    <view class="list-page">
        <view v-if="searchTitle" style="padding:20rpx;">
            <view @click="goBack" class="search">
                <view style="display: flex;align-items: center;">
                    <uni-icons color="#BFBFBF;" type="search" size="20"></uni-icons>
                    <view style="margin-left:10rpx;">{{ searchTitle }}</view>
                </view>
            </view>
        </view>
        <!-- 顶部筛选栏 -->
        <view class="filter-bar">
            <view class="filter-item" :class="{ active: sortType == 1 }" @click="changeSort(1)">
                综合
            </view>
            <view class="filter-item" :class="{ active: sortType == 2 }" @click="changeSort(2)">
                销量
            </view>
            <view class="filter-item price" :class="{ active: sortType == 3 }" @click="changeSort(3)">
                价格
                <text class="iconfont" v-if="sortType == 3" style="font-size:10rpx;">
                    {{ priceOrder == 3 ? '&#xe605;' : '&#xe604;' }}
                </text>
            </view>
        </view>
        <!-- 商品列表 -->
        <view class="recommend">
            <!-- 使用瀑布流组件展示商品列表 -->
            <customWaterfallsFlow ref="waterfallsFlowRef" :value="goodsList" @imageClick="goToDetail">
                <view v-for="(item, index) in goodsList" :key="index" slot="slot{{index}}">
                    <goods-card :data="item"></goods-card>
                </view>
            </customWaterfallsFlow>
        </view>
        <!-- 空状态 -->
        <view class="empty-state" v-if="goodsList.length === 0 && !loading">
            <text>暂无相关商品</text>
        </view>
    </view>
</template>

<script>
const req = require('../../utils/request')
import customWaterfallsFlow from "@/components/custom-waterfalls-flow/custom-waterfalls-flow" //瀑布流
import goodsCard from "@/components/goods-card/goods-card" //商品卡片
export default {
    data() {
        return {
            keyword: '',    // 搜索关键词
            sortType: 1,    // 排序类型：default-综合，sales-销量，price-价格
            priceOrder: 3,  // 价格排序：asc-升序，desc-降序
            pageNum: 1,     // 当前页码
            loading: false, // 是否正在加载
            hasMore: true,  // 是否还有更多数据
            goodsList: [],  // 商品列表
            categoryId: null,
            searchTitle: null
        }
    },
    components: {
        goodsCard,
        customWaterfallsFlow
    },
    onLoad(options) {
        if (options.title) {
            uni.setNavigationBarTitle({
                title: options.title
            })
        }
        this.categoryId = options.categoryId
        this.searchTitle = options.searchTitle
        this.getGoodsList()
    },

    // 添加下拉刷新配置
    onReachBottom() {
        this.pageNum++
        this.getGoodsList()
    },

    methods: {
        // 切换排序方式
        changeSort(type) {
            if (type === this.sortType && type === 3) {
                // 价格排序切换升降序
                this.priceOrder = this.priceOrder === 3 ? 4 : 3
            } else {
                this.sortType = type
                if (type === 3) {
                    this.priceOrder = 3
                }
            }

            // 重新加载数据
            this.page = 1
            this.goodsList = []
            this.$refs.waterfallsFlowRef.refresh();
            this.hasMore = true
            this.getGoodsList()
        },


        getGoodsList() {
            req.getRequest("/shopApi/home/<USER>", {
                sort: this.sortType != 3 ? this.sortType : this.priceOrder,
                storeId: req.getStorage("currentStore").id,
                pageNum: this.pageNum,
                categoryId: this.categoryId,
                title: this.searchTitle,
                pageSize: 10
            }, res => {
                res.data.list.forEach(item => {
                    item.image = item.img
                })
                this.goodsList = [...this.goodsList, ...res.data.list]
            })
        },
        // 加载更多
        loadMore() {
            this.getGoodsList()
        },
        goBack() {
            uni.navigateBack()
        },
        // 跳转到商品详情
        goToDetail(item) {
            if (!item || !item.id) {
                req.msg('商品信息不完整')
                return
            }
            let url = `/product/detail/detail?id=${item.id}`
            if (item.type) {
                url += `&type=${item.type}`
            }
            uni.navigateTo({
                url: url
            })
        },
    }
}
</script>

<style lang="scss" scoped src="./list.scss"></style>