<template>
	<view class="index_view" :style="{ 'padding-top': statusBarHeight + 'px' }">
		<customTopNav :title="'我的'" :color="'#fff'" :bgColor="show_bgcolor ? '#e50101' : 'transparent'">
		</customTopNav>
		<!-- <image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241113/055f065691e74f8bb1b208f50bf342d4.png"
			mode="widthFix" style="width:100%;position: relative;z-index: 1;"></image> -->
		<view style="position: relative;z-index: 2;">
			<view class="avatar">
				<view style="display: flex;align-items: center;">
					<view @click="goUrl('/mine/userInfo/userInfo')" class="avatar-wrapper">
						<image
							:src="userInfo.id ? userInfo.avatar : 'https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240927/6f40d834eed1445b8a4fe3404a800611.png'"
							mode="aspectFill"></image>
					</view>
					<view v-if="userInfo.id" @click="goUrl('/mine/userInfo/userInfo')"
						style="font-size:26rpx;color:#fff;">
						<view class="name" style="display: flex;align-items: center;">
							<view>{{ userInfo.memName || userInfo.nickName }}</view>
							<view class="identification" v-if="userInfo.levels === 0">
								<image
									src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20250121/bb6a74f9feae4d6f85a59b670ca54350.png"
									style="width: 36rpx;height: 42rpx;position: absolute;left: -12rpx;top: -6rpx;">
								</image>
								普通会员
							</view>
							<view class="identification"
								style="background: linear-gradient(90deg, #FBB04B 18%, #FBB04B 23%, #FFA711 100%);"
								v-if="userInfo.levels == 1 || userInfo.levels == 2">
								<image
									src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20250121/ccd2c2861ab4494296a2442582576bcf.png"
									style="width: 36rpx;height: 42rpx;position: absolute;left: -12rpx;top: -6rpx;">
								</image>
								金卡会员
							</view>
						</view>
						<view style="display: flex;align-items: center;">
							<view style="margin-right:10rpx;">{{ userInfo.phone }}</view>
							<text class="iconfont">&#xe7e5;</text>
						</view>
					</view>
					<view v-else style="font-size:26rpx;color:#fff;">
						<button v-if="phone" @click="login" hover-class="none" class="login-btn">点击登录</button>
						<button v-else @click="login" hover-class="none" class="login-btn" open-type="getPhoneNumber"
							@getphonenumber="getphonenumber">点击登录</button>
					</view>
				</view>
				<view @click="goUrl('/mine/memberCode/memberCode')" class="vip-code">
					<text class="iconfont" style="font-size:50rpx;">&#xe6ce;</text>
					<view style="font-size:22rpx;">会员码</view>
				</view>
			</view>
			<!-- <view class="open-vip">
			<view class="vip-title">
				<view style="display: flex;align-items: center;">
					<image
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241114/36e2cb42f812479a8d6bb4e692de394d.png"
						mode="widthFix" style="width:30rpx;"></image>
					<view>集和堂会员<text style="margin:0 7rpx;">|</text>开卡既得20元红包</view>
				</view>
				<view class="vip-btn">开通会员</view>
			</view>
			<view class="vip-content">
				<view class="vip-item">
					<image
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241114/03119d1ab1c54137b0198352306bff64.png"
						mode="widthFix" style="width:60rpx;margin-bottom:10rpx;"></image>
					<view>单单返现</view>
				</view>
				<view class="vip-item">
					<image
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241115/6c861f5ecd2a4e868188b43066afb485.png"
						mode="widthFix" style="width:60rpx;margin-bottom:10rpx;"></image>
					<view>会员券</view>
				</view>
				<view class="vip-item">
					<image
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241115/be83ffdfa7da4dd4b2b3b4f44bf718e7.png"
						mode="widthFix" style="width:60rpx;margin-bottom:10rpx;"></image>
					<view>会员日</view>
				</view>
				<view class="vip-item">
					<image
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241115/51abe9b75448498eb0053612508c558a.png"
						mode="widthFix" style="width:60rpx;margin-bottom:10rpx;"></image>
					<view>App图标</view>
				</view>
				<view class="vip-item">
					<image
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241115/2cd64a2919ee4f4f8db1a2c1d1b5f694.png"
						mode="widthFix" style="width:60rpx;margin-bottom:10rpx;"></image>
					<view>客服特权</view>
				</view>
			</view>
		</view> -->
			<view class="wallet">
				<view @click="goUrl('/mine/recharge/recharge')" class="wallet_item">
					<viwe style="font-weight: 900; margin-bottom:5rpx;">{{ isLogin ? balance.money : '0.00' }}</viwe>
					<view>钱包</view>
				</view>
				<view class="wallet_item" @click="goUrl('/mine/couponList/couponList')">
					<viwe style="font-weight: 900; margin-bottom:5rpx;">{{ isLogin ? balance.coupon : '0' }}</viwe>
					<view>优惠券</view>
				</view>
				<view @click="goUrl('/mine/pointsRecord/pointsRecord')" class="wallet_item">
					<viwe style="font-weight: 900; margin-bottom:5rpx;">{{ isLogin ? balance.points : '0' }}</viwe>
					<view>积分</view>
				</view>
				<view class="wallet_item">
					<viwe style="font-weight: 900; margin-bottom:5rpx;">{{ isLogin ? balance.hb : '0' }}</viwe>
					<view>和币</view>
				</view>
				<view class="wallet_item">
					<viwe style="font-weight: 900; margin-bottom:5rpx;">{{ isLogin ? balance.collect : '0' }}</viwe>
					<view>收藏</view>
				</view>
			</view>
			<view class="order">
				<view style="display: flex;justify-content:space-between;align-items: center;width: 100%;">
					<view class="title">
						<view class="line"></view>
						<view style="font-size:28rpx;">我的订单</view>
					</view>
					<view @click="goUrl('/mine/order/order')"
						style="display: flex;align-items: center;margin-bottom:30rpx;">
						<view>全部订单</view>
						<text class="iconfont" style="font-size:26rpx;">&#xe667;</text>
					</view>
				</view>
				<view style="display: flex;justify-content: space-between;">
					<view @click="goUrl('/mine/order/order?currentTab=1')" class="order_item">
						<uni-badge class="uni-badge-left-margin" :text="userInfo.dfk" absolute="rightTop" size="small">
							<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe668;</text>
						</uni-badge>
						<view>待付款</view>
					</view>
					<view @click="goUrl('/mine/order/order?currentTab=2')" class="order_item">
						<uni-badge class="uni-badge-left-margin" :text="userInfo.yqr" absolute="rightTop" size="small">
							<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe669;</text>
						</uni-badge>
						<view>待发货</view>
					</view>
					<view @click="goUrl('/mine/order/order?currentTab=3')" class="order_item">
						<uni-badge class="uni-badge-left-margin" :text="userInfo.yfh" absolute="rightTop" size="small">
							<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe6ed;</text>
						</uni-badge>
						<view>待收货/自提</view>
					</view>
					<view @click="goUrl('/mine/order/order?currentTab=5')" class="order_item">
						<uni-badge class="uni-badge-left-margin" :text="userInfo.tk" absolute="rightTop" size="small">
							<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe636;</text>
						</uni-badge>
						<view>退货售后</view>
					</view>
					<view class="order_item" v-if="false">
						<uni-badge class="uni-badge-left-margin" :text="0" absolute="rightTop" size="small">
							<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe601;</text>
						</uni-badge>
						<view>门店消费</view>
					</view>
				</view>
			</view>
			<!-- <view class="service">
				<view class="title">
					<view class="line"></view>
					<view style="font-size:28rpx;">就诊服务</view>
				</view>
				<view style="display: flex;flex-wrap: wrap;">
					<view @click="goUrl('/mine/sickPeopleList/sickPeopleList')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe624;</text>
						<view>就诊人</view>
					</view>
				</view>
			</view> -->
			<view class="service">
				<view class="title">
					<view class="line"></view>
					<view style="font-size:28rpx;">我的服务</view>
				</view>
				<view style="display: flex;flex-wrap: wrap;">
					<view @click="fixedPoint" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe67b;</text>
						<view>医保定点</view>
					</view>
					<view class="service_item">
						<button style="font-size:28rpx !important;" class="contact-btn" open-type="contact"
							hover-class="none">
							<text class="iconfont" style="font-size:50rpx;margin-bottom:12rpx;">&#xec2e;</text>
							<view>联系客服</view>
						</button>
					</view>
					<view @click="goUrl('/mine/couponCenter/couponCenter')" class="service_item">
						<text class=" iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe625;</text>
						<view>领券中心</view>
					</view>
					<view @click="goUrl('/mine/addressList/addressList')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe694;</text>
						<view>地址管理</view>
					</view>
					<view @click="goUrl('/mine/about/about')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe655;</text>
						<view>关于我们</view>
					</view>
					<!-- <view class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe649;</text>
						<view>签约医生</view>
					</view>
					<view @click="goUrl('/mine/sickPeopleList/sickPeopleList')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe624;</text>
						<view>就诊人</view>
					</view> -->
					<view @click="goUrl('/mine/feedback/feedback')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe85f;</text>
						<view>意见反馈</view>
					</view>
					<view @click="goUrl('/staff/returnMoney/returnMoney')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe690;</text>
						<view>返现明细</view>
					</view>
				</view>
			</view>
			<view class="service" v-if="false">
				<view class="title">
					<view class="line"></view>
					<view style="font-size:28rpx;">活动推广</view>
				</view>
				<view style="display: flex;flex-wrap: wrap;">
					<view class="service_item" @click="goUrl('/mine/personalCenter/personalCenter')">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe60b;</text>
						<view>个人中心</view>
					</view>
					<view class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe65f;</text>
						<view>更新日志</view>
					</view>
					<view class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe652;</text>
						<view>活动推广</view>
					</view>
				</view>
			</view>
			<view v-if="userInfo.introductionId" class="service">
				<view class="title">
					<view class="line"></view>
					<view style="font-size:28rpx;">员工</view>
				</view>
				<view style="display: flex;flex-wrap: wrap;">
					<view class="service_item" @click="goUrl('/staff/consultationSheet/consultationSheet')">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe60b;</text>
						<view>协助问诊</view>
					</view>
					<view class="service_item" @click="goUrl('/staff/contract/contract')">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe66e;</text>
						<view>个人合同</view>
					</view>
					<view @click="goUrl('/staff/leave/leave')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe639;</text>
						<view>请假申请</view>
					</view>
					<view @click="goUrl('/staff/cardReplace/cardReplace')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe720;</text>
						<view>补卡申请</view>
					</view>
					<view @click="goUrl('/staff/regularization/regularization')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe614;</text>
						<view>转正申请</view>
					</view>
					<view @click="goUrl('/staff/subsidy/subsidy')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe6cc;</text>
						<view>补贴申请</view>
					</view>
					<view @click="goUrl('/staff/salaryDetails/salaryDetails')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe674;</text>
						<view>薪资明细</view>
					</view>
					<view @click="goUrl('/staff/applicationWork/applicationWork')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe6dc;</text>
						<view>加班申请</view>
					</view>
					<view @click="goUrl('/staff/promotion/promotion')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe684;</text>
						<view>晋升申请</view>
					</view>
					<!-- <view @click="goUrl('/staff/transfer/transfer')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe713;</text>
						<view>调职申请</view>
					</view> -->
					<view @click="goUrl('/staff/outwardApply/outwardApply')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe69b;</text>
						<view>外出申请</view>
					</view>
					<view @click="goUrl('/staff/shopPlan/shopPlan')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe79d;</text>
						<view>巡店计划</view>
					</view>
					<view @click="goUrl('/staff/toDoList/toDoList')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe686;</text>
						<view>待办申请</view>
					</view>
					<view @click="goUrl('/staff/exitProcedure/exitProcedure')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe688;</text>
						<view>离职申请</view>
					</view>
					<view @click="goUrl('/mine/joinMember/joinMember')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe68f;</text>
						<view>推荐会员</view>
					</view>
					<view @click="goUrl('/staff/cashier/cashier')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe6a3;</text>
						<view>pos收银</view>
					</view>
					<view @click="goUrl('/staff/verification/verification')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe6b2;</text>
						<view>抖音券核销</view>
					</view>
					<view @click="goUrl('/staff/faceRecording/faceRecording')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe6d4;</text>
						<view>人脸录入</view>
					</view>
					<view @click="goUrl('/staff/fileMaintain/fileMaintain')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe6d5;</text>
						<view>资料维护</view>
					</view>
					<view @click="goUrl('/staff/withdrawal/withdrawal')" class="service_item"
						v-if="userInfo.withdrawal === 1">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe6d8;</text>
						<view>提现申请</view>
					</view>
					<view @click="goUrl('/other/inventoryCheckList/inventoryCheckList')" class="service_item">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe9ac;</text>
						<view>盘点</view>
					</view>
					<!-- faceRecording -->
				</view>
			</view>
			<view v-if="userInfo.introductionId" class="service">
				<view class="title">
					<view class="line"></view>
					<view style="font-size:28rpx;">ERP审核</view>
				</view>
				<view style="display: flex;flex-wrap: wrap;">
					<view class="service_item" @click="goUrl('/erp/orderApproval/orderApproval')">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe6a8;</text>
						<view>订单审核</view>
					</view>
					<view class="service_item" @click="goUrl('/erp/receiveApproval/receiveApproval')">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe60b;</text>
						<view>收货审核</view>
					</view>
					<view class="service_item" @click="goUrl('/erp/invoiceApproval/invoiceApproval')">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe6a7;</text>
						<view>开票审核</view>
					</view>
					<view class="service_item" @click="goUrl('/erp/sellApproval/sellApproval')">
						<text class="iconfont" style="font-size:50rpx;margin-bottom:8rpx;">&#xe6a6;</text>
						<view>销售订单</view>
					</view>
				</view>
			</view>
			<view style="margin:60rpx 20rpx;">
				<image
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241115/0914c525c44243f99ada23e2f354e2a4.png"
					mode="widthFix" style="width:230rpx;display: block;margin:0 auto 30rpx;"></image>
				<customWaterfallsFlow ref="waterfallsFlowRef" :value="goodsList" @imageClick="goToDetail">
					<view v-for="(item, index) in goodsList" :key="index" slot="slot{{index}}">
						<goods-card :data="item"></goods-card>
					</view>
				</customWaterfallsFlow>
			</view>
		</view>
	</view>
</template>

<script>
const app = getApp()
const req = require("../../utils/request")
import customWaterfallsFlow from "@/components/custom-waterfalls-flow/custom-waterfalls-flow" //瀑布流
import goodsCard from "@/components/goods-card/goods-card" //商品卡片
import customTopNav from "@/components/custom-top-nav/custom-top-nav" // 自定义头部
export default {
	data() {
		return {
			goodsList: [],
			openid: '',
			sessionKey: '',
			phone: '',
			isLogin: false,
			userInfo: {},
			pageNum: 1,
			balance: {},
			show_bgcolor: false,
			statusBarHeight: '',
			one: true,
		};
	},
	components: {
		customWaterfallsFlow,
		goodsCard,
		customTopNav
	},
	onPageScroll(e) {
		// console.log(e);
		if (e.scrollTop > 10) { //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色显示
			this.show_bgcolor = true;
		} else {
			this.show_bgcolor = false;
		}
	},
	onLoad() {
		this.getGoodsList()
		req.showLoading()
		app.globalData.getSessionKey(json => {
			uni.hideLoading()
			this.openid = json.openid
			this.sessionKey = json.session_key
			this.phone = json.phone
		});
		this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 50;
	},
	onShow() {
		if (req.getStorage("userInfo")) {
			this.userInfo = req.getStorage("userInfo")
			this.getUserInfo();
		} else {
			this.userInfo = {}
		}
	},
	onReachBottom() {
		this.pageNum++
		this.getGoodsList()
	},
	methods: {
		goToDetail(item) {
			if (!item || !item.id) {
				req.msg('商品信息不完整')
				return
			}
			let url = `/product/detail/detail?id=${item.id}`
			if (item.type) {
				url += `&type=${item.type}`
			}
			uni.navigateTo({
				url: url
			})
		},
		fixedPoint() {
			const WEB_URL =
				'https://card.wecity.qq.com/gzmi/Clinic/InsurancePlace?channel=AAEblUeP34xqYWldD53JEkuz&cityId=440100'
			uni.navigateToMiniProgram({
				appId: 'wx7ec43a6a6c80544d',
				path: `pages/common/webview/main?src=${encodeURIComponent(WEB_URL)}`,
			})
		},
		//钱包
		getBalance() {
			req.getRequest("/shopApi/mp/user/myBalance", {}, res => {
				if (res.code == 200) {
					this.isLogin = true
					this.balance = res.data
				}
			})
		},
		//商品
		getGoodsList() {
			req.getRequest("/shopApi/home/<USER>", {
				storeId: req.getStorage("currentStore").id,
				pageNum: this.pageNum,
				pageSize: 10
			}, res => {
				res.data.list.forEach(item => {
					item.image = item.img
				})
				this.$nextTick(() => {
					this.goodsList = [...this.goodsList, ...res.data.list]
				})
			})
		},
		getMy() {
			req.getRequest("/shopApi/mp/user/my", {}, res => {
				console.log(res);
				this.userInfo = { ...this.userInfo, ...res.data };
				console.log(this.userInfo, 'ggg');
			})
		},
		goUrl(url) {
			if (!req.getStorage("userInfo")) return req.msg("请先登录")
			uni.navigateTo({
				url: url
			})
		},
		login() {
			uni.showLoading({
				title: '登录中...'
			});
			uni.getUserProfile({
				desc: "注册",
				success: res => {
					this.encryptedData = res.encryptedData
					this.iv = res.iv
					if (this.phone) {
						req.postRequest("/shopApi/weixin/login", {
							openid: this.openid,
							encryptedData: this.encryptedData,
							iv: this.iv,
							sessionKey: this.sessionKey
						}, res => {
							uni.hideLoading()
							this.userInfo = res.data;
							req.setStorage("AUTH_TOKEN", res.data.accessToken);
							// req.setStorage("userInfo", res.data);
							this.getUserInfo();
						})
					}
				},
			})
		},
		getphonenumber(e) {
			if (e.target.errMsg == "getPhoneNumber:ok") {
				req.postRequest("/shopApi/weixin/login", {
					openid: this.openid,
					encryptedData: this.encryptedData,
					iv: this.iv,
					sessionKey: this.sessionKey
				}, res => {
					req.setStorage("AUTH_TOKEN", res.data.accessToken)
					setTimeout(() => {
						req.getRequest("/shopApi/weixin/getPhone/" + e.detail.code, {}, res1 => {
							uni.hideLoading();
							this.userInfo = res1.data;
							// req.setStorage("userInfo", res1.data);
							req.setStorage("AUTH_TOKEN", res1.data.accessToken);
							this.getUserInfo();
						})
					}, 500)

				})
			} else {
				uni.hideLoading();
				req.msg("取消登录")
			}
		},
		getUserInfo() {
			req.getRequest("/shopApi/weixin/getUserInfo", {}, res => {
				if (res.code == 401) {
					this.userInfo = {}
					req.removeStorage("userInfo");
					return
				};
				this.userInfo = res.data;
				req.setStorage("userInfo", res.data);
				getApp().globalData.updateCartBadge()
				this.getMy()
				this.getBalance()
				if (this.one && this.userInfo.introductionId) {
					this.one = false
					this.getAuth()
				}
			})
		},
		getAuth() {
			req.getRequest("/shopApi/public/account/getAuth", {
				phone: req.getStorage("userInfo").phone,
			}, res => {
				console.log(111, res);
				if (res.data == 0) {
					const obj = {
						url: `https://staffnew.gzjihetang.com/pages/web/web`,
						phone: req.getStorage("userInfo").phone,
					};
					uni.navigateTo({
						url: "/other/web/web?obj=" + JSON.stringify(obj)
					})
				}
			})
		},
	},
}
</script>


<style lang="scss" scoped src="./user.scss"></style>
