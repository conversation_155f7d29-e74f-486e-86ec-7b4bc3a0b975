<template>
    <view class="map-container">
        <!-- 地图组件 -->
        <map class="map" :latitude="latitude" :longitude="longitude" :markers="markers" :scale="16" show-location>
        </map>

        <!-- 底部信息卡片 -->
        <view class="store-card">
            <view class="store-info">
                <image class="store-logo"
                    src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241119/001451741aa0494f98a3c69511a397a3.png"
                    mode="aspectFit">
                </image>
                <view class="store-detail">
                    <view class="store-name">{{ store.storeName }}</view>
                    <view class="store-address">{{ store.address }}</view>
                </view>
            </view>
            <view class="action-buttons">
                <button class="nav-btn" @click="openLocation">
                    <text class="iconfont">&#xe65e;</text>
                    导航
                </button>
                <button class="call-btn" @click="makePhoneCall">
                    <text class="iconfont">&#xe6b6;</text>
                    电话
                </button>
            </view>
        </view>
    </view>
</template>

<script>
const qqmap = require("../../utils/qqmap.js")
const req = require("../../utils/request.js")

export default {
    data() {
        return {
            store: null,
            latitude: 0,
            longitude: 0,
            markers: []
        }
    },

    onLoad() {
        this.store = req.getStorage("currentStore")
        // 初始化地图SDK
        qqmap.initMap()
        this.initLocation()
    },

    methods: {
        // 初始化位置信息
        initLocation() {
            if (this.store) {
                // 解析门店地址获取经纬度
                qqmap.geocoder(this.store.address, (res) => {
                    if (res) {
                        this.latitude = res.location.lat
                        this.longitude = res.location.lng
                        // 设置标记点
                        this.markers = [{
                            id: 1,
                            latitude: res.location.lat,
                            longitude: res.location.lng,
                            title: this.store.storeName,
                            width: 32,
                            height: 32,
                            callout: {
                                content: this.store.storeName,
                                padding: 10,
                                borderRadius: 5,
                                display: 'ALWAYS'
                            }
                        }]
                    }
                })
            }
        },

        // 打开导航
        openLocation() {
            uni.openLocation({
                latitude: this.latitude,
                longitude: this.longitude,
                name: this.store.storeName,
                address: this.store.address,
                success: () => {
                    console.log('导航打开成功')
                }
            })
        },

        // 拨打电话
        makePhoneCall() {
            if (this.store.phone) {
                uni.makePhoneCall({
                    phoneNumber: this.store.phone,
                    success: () => {
                        console.log('拨打电话成功')
                    }
                })
            } else {
                uni.showToast({
                    title: '暂无联系电话',
                    icon: 'none'
                })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.map-container {
    width: 100%;
    height: 100vh;
    position: relative;

    .map {
        width: 100%;
        height: 100%;
    }

    .store-card {
        position: fixed;
        left: 20rpx;
        right: 20rpx;
        bottom: calc(20rpx + env(safe-area-inset-bottom));
        background: #fff;
        border-radius: 24rpx;
        padding: 30rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

        .store-info {
            display: flex;
            align-items: center;
            margin-bottom: 30rpx;

            .store-logo {
                width: 80rpx;
                height: 80rpx;
                border-radius: 12rpx;
                margin-right: 20rpx;
            }

            .store-detail {
                flex: 1;

                .store-name {
                    font-size: 32rpx;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 8rpx;
                }

                .store-address {
                    font-size: 26rpx;
                    color: #666;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                }
            }
        }

        .action-buttons {
            display: flex;
            gap: 20rpx;

            button {
                flex: 1;
                height: 80rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 28rpx;
                border-radius: 40rpx;

                .iconfont {
                    font-size: 32rpx;
                    margin-right: 8rpx;
                }

                &.nav-btn {
                    background: #ea1306;
                    color: #fff;
                }

                &.call-btn {
                    background: #f8f9fc;
                    color: #333;
                    border: 1px solid;
                }
            }
        }
    }
}
</style>