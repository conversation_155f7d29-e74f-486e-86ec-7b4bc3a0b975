<template>
    <view>
        <web-view :src="url"></web-view>
    </view>
</template>

<script>
const req = require('../../utils/request')
export default {
    data() {
        return {
            url: '',
            timer: null
        }
    },
    onLoad(options) {
        options = JSON.parse(options.obj)
        this.url = options.url + `?phone=${options.phone}`
        console.log(this.url);
        this.timer = setInterval(() => {
            req.getRequest("/shopApi/public/account/getAuth", {
                phone: options.phone
            }, res => {
                if (res.data == 1) {
                    clearInterval(this.timer)
                    uni.navigateBack()
                }
            })
        }, 3000);
        // return
        // setTimeout(() => {
        //     uni.navigateBack()
        // }, 2000);
    },
    onUnload() {
        clearInterval(this.timer)
    }

}
</script>

<style lang="scss" scoped></style>