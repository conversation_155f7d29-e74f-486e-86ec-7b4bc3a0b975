<template>
	<view class="consultation-container">
		<!-- 顶部提示 -->
		<view class="tip-box">
			<view class="tip-content">
				<text class="tip-icon">!</text>
				<text>信息将作为医生诊断和开具处方的重要依据，请如实填写</text>
			</view>
		</view>

		<!-- 风险告知 -->
		<view class="risk-notice">
			<text>根据国家相关规定，只能为患者提供处方服务，若您不是患者，我们只能提供咨询服务。在线咨询不能代替面诊，继续咨询表示已经知晓并同意该条款。</text>
			<text class="link" @click="viewRiskNotice">《风险告知及知情同意书》</text>
		</view>

		<!-- 用药人信息 -->
		<view class="form-section">
			<view class="section-title">用药人</view>
			<view class="user-info">
				<view v-if="patient" @click="goUrl('/mine/sickPeopleList/sickPeopleList?choice=1')">
					<text>{{ patient.name }}</text>
					<view style="display: flex;align-items: center;">
						<text class="gender-age">{{ patient.sex == 1 ? '男' : '女' }}-{{ patient.age }}岁-{{ patient.tel
							}}</text>
						<!-- <text class="main-user">本人</text> -->
						<text class="iconfont arrow">&#xe667;</text>
					</view>
				</view>
				<view v-else @click="goUrl('/mine/sickPeopleDet/sickPeopleDet')">
					<view class="empty-user">
						<text class="add-user">添加用药人</text>
						<text class="iconfont arrow">&#xe667;</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 药品信息 -->
		<view class="form-section">
			<view class="section-title">药品信息</view>
			<view class="medicine-list">
				<view class="medicine-item" v-for="(item, index) in medicineList" :key="index">
					<image class="medicine-img" :src="item.imgUrl" mode="widthFix" style="filter: blur(5px);"></image>
					<image style="position: absolute;top:24rpx;left:24rpx; width: 160rpx;height: 160rpx;"
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240111/a338c782689a421b8e9ab0848c3c5c10.png">
					</image>
					<view class="medicine-info">
						<view class="medicine-name">{{ item.drugName }}</view>
						<view class="medicine-spec">规格: {{ item.spec }}</view>
						<view class="medicine-price">¥{{ item.salePrice }}</view>
					</view>
					<view class="medicine-count">x{{ item.number }}</view>
				</view>
			</view>
		</view>
		<!-- 开方信息 -->
		<view class="form-section">
			<view class="section-title">开方信息</view>
			<view class="prescription-form">
				<input class="main-input" v-model="mainComplaint" placeholder="请输入主诉内容" />
				<view class="checkbox-group">
					<text class="checkbox-title" style="line-height: 56rpx;">*请选择线下已确诊疾病(最多可选3项)</text>
					<view class="search-box">
						<input type="text" v-model="searchKeyword" placeholder="搜索病症" @confirm="searchDisease" />
						<button class="search-btn" @click="searchDisease">搜索</button>
					</view>
					<view style="line-height: 76rpx;font-size: 28rpx;color: #4E5969;">
						<view v-for="(item, index) in IcdCodesList" :key="index" @click="check_type(item.name)"
							style="border-bottom: 1px solid #F2F3F5;">{{
								item.name }}
						</view>
					</view>
					<view style="display:flex;flex-wrap: wrap;padding: 15rpx 0;">
						<view>已确诊疾病：</view>
						<view
							style="border-radius: 12rpx;background: rgba(255, 50, 55, 0.1);font-size: 36rpx;font-weight: normal;line-height: 44rpx;color: #EA1306;text-align:center;padding:0 20rpx;margin:0 10rpx 10rpx 0;"
							v-for="(item, index) in diagnosisList" :key="index">
							{{ item }}<uni-icons type="closeempty" size="15" color="#EA1306" style="margin-left:10rpx;"
								@click="del_type(index)"></uni-icons>
						</view>
					</view>
				</view>
				<view class="question-list">
					<view v-for="(item, index) in questions" :key="index">
						<view class="question-item" style="border-bottom: 1px solid #f5f5f5;">
							<text>{{ item.question }}</text>
							<uni-data-checkbox selectedColor="#EA1306" @change="handleAnswerChange(index, $event)"
								v-model="item.answer" :localdata="radioOptions" class="radio-group" />
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部提交按钮 -->
		<view class="submit-section">
			<label class="agreement">
				<checkbox :checked="agreed" @click="agreed = !agreed" />
				<text>提交即代表同意</text>
				<text class="link"
					@click.stop="viewServiceAgreement('shoppingCart/consultationAgreement/index')">《患者问诊服务协议》</text>
				<text>和</text>
				<text class="link" @click.stop="viewServiceAgreement('shoppingCart/privacyPolicy/index')">《隐私政策》</text>
			</label>
			<button class="submit-btn" @click="submitForm" :disabled="hasYesAnswer"
				:class="{ 'submit-btn-disabled': hasYesAnswer }">提交</button>
		</view>
		<view v-if="msgShow" class="mask"></view>
		<view v-if="msgShow" class="msg">
			<image style="width:80rpx"
				src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240528/d221cf0e1a504fb7a1773b60e6fbe2c9.gif"
				mode="widthFix"></image>
			<view style="margin-top:20rpx;">审核中耐心等待</view>
			<view>请不要离开</view>
		</view>
	</view>
</template>

<script>
const app = getApp();
const req = require('../../utils/request.js')
export default {
	data() {
		return {
			patient: null,
			medicineList: [],
			mainComplaint: '',
			searchKeyword: '',
			radioOptions: [
				{ text: '是', value: '1' },
				{ text: '否', value: '0' }
			],
			questions: [
				{ question: '是否复诊', answer: '1' },
				{ question: '是否肝功能异常', answer: '0' },
				{ question: '肝功能是否异常?', answer: '0' },
				{ question: '您是否有过敏史?', answer: '0' },
				{ question: '有无不良反应?', answer: '0' },
				{ question: '是否有家族病史?', answer: '0' },
				{ question: '是否服用过开具药品?', answer: '0' },
				{ question: '是否做过手术?', answer: '0' },
				{ question: '是否是妊娠?', answer: '0' },
				{ question: '是否哺乳期?', answer: '0' },
				{ question: '是否是备孕?', answer: '0' }
			],
			agreed: false,
			choice: false,
			msgShow: false,
			IcdCodesList: [],
			diagnosisList: [],
			orderId: "",
			payState: null,
			getCode: false,
			authCode: "",
		}
	},
	computed: {
		// 修改计算属性，检查所有问题的答案
		hasYesAnswer() {
			// 第一个问题(是否复诊)为0时禁用
			if (this.questions[0].answer === '0') {
				return true
			}
			// 其他问题为1时禁用
			return this.questions.slice(1).some(q => q.answer === '1')
		}
	},
	onLoad(options) {
		this.orderId = Number(options.orderId);
		this.payState = options.payState;
		this.drugInfolist();
		this.getHntyAndDoctor();
	},
	onShow() {
		if (app.globalData.authCode && this.getCode) {
			console.log("进来了");
			this.authCode = app.globalData.authCode
			this.getCode = false
			req.showLoading()
			req.postRequest("/shopApi/medical/order/accountPayment", {
				orderId: this.orderId,
				qrcode: this.authCode
			}, res => {
				uni.hideLoading()
				if (res.code == 200) {
					uni.redirectTo({
						url: '/shoppingCart/medicalOrder/medicalOrder?orderId=' + this.orderId
					})
				} else {
					uni.redirectTo({
						url: '/shoppingCart/orderDetails/orderDetails?id=' + this.orderId
					})
				}
			}, true)
		}
		this.getUserInfo()
	},
	onUnload() {
		if (this.timer) {
			clearInterval(this.timer);
		}
	},
	methods: {
		goUrl(url) {
			uni.navigateTo({
				url
			})
		},
		handleAnswerChange(index, e) {
			const value = e.detail.value

			if (index === 0 && value === '0') {
				// 是否复诊选否时的提示
				uni.showModal({
					title: '温馨提示',
					content: '根据相关规定，在线问诊仅适合复诊患者，如您是初诊请到就近医院就医。',
					confirmText: '我知道了',
					showCancel: false
				})
			} else if (index > 0 && value === '1') {
				// 其他问题选是时的提示
				uni.showModal({
					title: '温馨提示',
					content: '您当前情况不适合在线问诊，建议您到医院就医。',
					confirmText: '我知道了',
					showCancel: false
				})
			}
		},
		// 获取用医人信息
		getUserInfo() {
			if (this.choice) return
			req.getRequest("/shopApi/userDrugPeople/list", {
				pageNum: 1,
				pageSize: 99
			}, res => {
				this.patient = res.data.list[0]
			})
			// TODO: 实现获取用医人信息
		},
		// 查看风险告知书
		viewRiskNotice() {
			// TODO: 实现查看风险告知书
			app.globalData.openPage('shoppingCart/riskNotification/index');
		},
		// 搜索疾病
		searchDisease() {
			// TODO: 实现疾病搜索
			req.postRequest("/shopApi/mp/orderDrug/getIcdCodes", {
				name: this.searchKeyword
			}, res => {
				this.IcdCodesList = [...res.data];
			});
		},
		// 选择疾病
		check_type(type) {
			console.log(type);
			if (this.diagnosisList.length >= 3) {
				this.IcdCodesList = [];
				this.searchKeyword = "";
				return req.msg("最多可选三项");
			}
			this.diagnosisList.push(type);
			this.IcdCodesList = [];
			this.searchKeyword = "";
		},
		// 删除初步诊断
		del_type(index) {
			this.diagnosisList.splice(index, 1);
		},
		// 查看服务协议 // 查看隐私政策
		viewServiceAgreement(url) {
			// TODO: 实现查看服务协议
			app.globalData.openPage(url);
		},
		// 提交表单
		submitForm() {
			const validations = [
				[this.questions[0].answer === '0', "初诊患者请到就近医院就医"],
				[this.questions.slice(1).some(q => q.answer === '1'), "您当前情况不适合在线问诊，请到医院就医"],
				[!this.patient, "请添加用药人"],
				[!this.mainComplaint, "请输入主诉内容"],
				[!this.diagnosisList.length, "请选择病症"],
				[!this.agreed, "勾选同意协议及隐私政策"]
			];

			for (const [condition, message] of validations) {
				if (condition) {
					req.msg(message);
					return;
				}
			}
			// TODO: 提交表单数据
			let parme = {
				abnormalLiverFunction: false,
				ageStr: this.patient.age,
				allergies: false,   // 是否过敏
				clientOrderNo: this.orderId,
				complaint: this.mainComplaint,   // 主诉
				family: false,  // 家族病史
				gender: this.patient.sex,
				icdCodes: this.diagnosisList,
				isUsed: false, // 是否服用过开具药品
				pastMedical: false,  //是否有过往病史
				patientName: this.patient.name,
				phoneNo: this.patient.tel,
				reaction: false,    // 有无不良反应
				renalDysfunction: false  //肾功能是否异常
			};
			if (this.patient.sex !== 1) {
				parme.gender = 2
				parme.breastfeeding = false  //是否哺乳期？
				parme.gestation = false      //是否是妊娠？
				parme.pegnancy = false       //是否是备孕？
			} else {
				parme.gender = 1
			}
			console.log(parme);
			// return;
			req.showLoading()
			req.postRequest('/shopApi/mp/his/takeOrder', parme, res => {
				if (res.data == '正在开方，请稍候。') {
					this.checkOrder()
				} else {
					uni.showModal({
						title: "提示",
						content: '提交失败，请稍后再尝试开方。',
						success(res) {
							if (res.confirm) {
								// console.log('用户点击确定'); // that.setData({ cancel: true }
							}
							uni.redirectTo({
								url: '/mine/order/order'
							})
						}
					});
				}
			})
		},
		checkOrder() {
			uni.hideLoading()
			this.msgShow = true
			this.timer = setInterval(() => {
				req.getRequest("/shopApi/mp/his/takeState", { orderId: this.orderId }, res => {
					if (res.code == 200 && res.msg == '审核通过') {
						clearInterval(this.timer)
						this.msgShow = false
						if (this.payState == 7) {
							uni.navigateToMiniProgram({
								appId: 'wxe183cd55df4b4369',
								path: `auth/pages/bindcard/auth/index?openType=getAuthCode&bizType=04107&cityCode=440108&channel=30000175&orgChnlCrtfCodg=BqK1kMStlhVDgN2uHf4EsLK/F2LjZPYJ81nK2eYQqxuHgpiS+wkhUBWGPoaYCwz0&orgCodg=P44010600664&orgAppId=1I5K5N9JE1T84460C80A00000ED5F971`,
								envVersion: req.env.NODE_ENV == 'product' ? 'release' : 'trial',
								success: res => {
									this.getCode = true
								},
								fail: () => {
									uni.redirectTo({
										url: '/shoppingCart/orderDetails/orderDetails?id=' + this.orderId
									})
								}
							})
						} else {
							req.payOrder(this.orderId, res => {
								uni.redirectTo({
									url: `/shoppingCart/pymentStatus/pymentStatus?orderId=${this.orderId}`
								});
							});
						}
					} else if (res.code == 500 && res.msg == '审核不通过') {
						clearInterval(this.timer)
						this.msgShow = false
						uni.showModal({
							content: '审核不通过',
							showCancel: false,
							success(val) {
								if (val.confirm) {
									uni.reLaunch({
										url: '/mine/order/order'
									})
								}
							}
						});
					}
				}, true);
			}, 3000);
		},
		// 获取病症和医生
		getHntyAndDoctor() {
			req.postRequest("/shopApi/userDrugPeople/getHntyAndDoctor", {
				isOverallPlanning: 1,
				orderId: this.orderId,
			}, res => {
				// this.medicineList = [...res.data.drugs];
				this.diagnosisList = res.data.icdCodes
				this.mainComplaint = res.data.mainComplaint;
			})
		},
		// 获取处方药信息
		drugInfolist() {
			req.postRequest("/shopApi/mp/orderDrug/drugInfo", {
				orderId: this.orderId,
			}, res => {
				this.medicineList = [...res.data.drugs];
			})
		}
	}
}
</script>

<style lang="scss" scoped>
@import './tcConsultation.scss';
</style>