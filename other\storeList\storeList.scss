.store-list {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: 40rpx;

  .search-box {
    background: #fff;
    padding: 20rpx;

    .search-input {
      display: flex;
      align-items: center;
      background: #f5f5f5;
      padding: 15rpx 20rpx;
      border-radius: 32rpx;

      input {
        flex: 1;
        margin-left: 10rpx;
        font-size: 28rpx;
      }
    }
  }

  .location-info {
    background: #fff;
    padding: 20rpx;
    margin-top: 20rpx;

    .location-title {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 10rpx;

      .iconfont {
        margin-right: 10rpx;
        color: #EA1306;
      }
    }

    .location-address {
      font-size: 26rpx;
      color: #666;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .refresh {
        color: #EA1306;
      }
    }
  }

  .store-container {
    margin-top: 20rpx;

    .store-item {
      position: relative;
      background: #fff;
      padding: 30rpx 20rpx;
      margin-bottom: 20rpx;
      display: flex;
      align-items: center;
      border-left: 6rpx solid transparent;

      &.active-store {
        background: #FFF9F9;
        border-left: 6rpx solid #EA1306;
      }

      &:active {
        background: #f5f5f5;
      }

      .store-image {
        width: 120rpx;
        height: 120rpx;
        margin-right: 20rpx;
        border-radius: 8rpx;
      }

      .store-info {
        flex: 1;
        padding-right: 180rpx;

        .store-name {
          font-size: 30rpx;
          color: #333;
          margin-bottom: 10rpx;
        }

        .store-address {
          font-size: 26rpx;
          color: #666;
          margin-bottom: 10rpx;
        }

        .store-distance {
          font-size: 24rpx;
          color: #999;
          margin-top: 6rpx;
          display: flex;
          align-items: center;

          .iconfont {
            font-size: 24rpx;
            margin-right: 6rpx;
          }
        }
      }

      .selected-tag {
        position: absolute;
        right: 20rpx;
        top: 50%;
        transform: translateY(-50%);
        background: #EA1306;
        color: #fff;
        padding: 10rpx 20rpx;
        border-radius: 30rpx;
        font-size: 24rpx;
        display: flex;
        align-items: center;

        .iconfont {
          margin-right: 6rpx;
          font-size: 24rpx;
        }
      }
    }
  }

  .no-data {
    padding-top: 200rpx;
    display: flex;
    flex-direction: column;
    align-items: center;

    image {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 20rpx;
    }

    text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .current-store {
    background: #fff;
    padding: 20rpx;
    margin-bottom: 20rpx;

    .store-title {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 15rpx;
    }

    .store-content {
      display: flex;
      align-items: center;

      .store-logo {
        width: 80rpx;
        height: 80rpx;
        border-radius: 8rpx;
        margin-right: 15rpx;
      }

      .store-detail {
        flex: 1;

        .store-name {
          font-size: 30rpx;
          color: #333;
          margin-bottom: 8rpx;
        }

        .store-address {
          font-size: 26rpx;
          color: #666;
        }
      }
    }
  }
}