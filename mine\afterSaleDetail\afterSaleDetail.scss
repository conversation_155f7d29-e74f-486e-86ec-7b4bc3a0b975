.after-sale-detail {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 100rpx;

  .status-section {
    background: #fff;
    padding: 40rpx 30rpx;
    margin-bottom: 20rpx;

    .status-text {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }

    .status-desc {
      font-size: 28rpx;
      color: #666;
    }
  }

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    padding: 20rpx 0;
  }

  .goods-section {
    background: #fff;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;

    .goods-item {
      display: flex;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #eee;

      .goods-img {
        width: 160rpx;
        height: 160rpx;
        border-radius: 8rpx;
        margin-right: 20rpx;
      }

      .goods-info {
        flex: 1;

        .goods-name {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 10rpx;
        }

        .goods-price {
          font-size: 30rpx;
          color: #ff4444;
          margin-bottom: 10rpx;
        }

        .goods-quantity {
          font-size: 26rpx;
          color: #999;
        }
      }
    }
  }

  .refund-section {
    background: #fff;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;

    .info-item {
      display: flex;
      justify-content: space-between;
      padding: 20rpx 0;
      font-size: 28rpx;
      border-bottom: 1rpx solid #eee;

      &:last-child {
        border-bottom: none;
      }

      .label {
        color: #666;
      }

      .value {
        color: #333;
      }
    }
  }

  .images-section {
    background: #fff;
    padding: 20rpx 30rpx;

    .image-list {
      display: flex;
      flex-wrap: wrap;
      
      image {
        width: 220rpx;
        height: 220rpx;
        margin-right: 15rpx;
        margin-bottom: 15rpx;
        border-radius: 8rpx;

        &:nth-child(3n) {
          margin-right: 0;
        }
      }
    }
  }

  .bottom-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx;
    background-color: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .cancel-btn {
      width: 100%;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background-color: #fff;
      color: #333;
      border: 2rpx solid #ddd;
      border-radius: 40rpx;
      font-size: 28rpx;
      
      &:active {
        opacity: 0.8;
      }
    }
  }
} 