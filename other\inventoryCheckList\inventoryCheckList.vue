<template>
    <view class="inventory-check">
        <view @click="goUrl(`/other/inventoryCheck/inventoryCheck?id=${item.documentNumber}`)" class="section"
            v-for="(item, index) in list" :key="index">
            <view class="section-header">
                <view class="section-title">单号信息</view>
            </view>
            <view class="batch-info">
                <view class="info-row">
                    <text class="label">单据编号：</text>
                    <text class="value">{{ item.documentNumber }}</text>
                </view>
                <view class="info-row">
                    <text class="label">日期：</text>
                    <text class="value">{{ item.createTime }}</text>
                </view>
                <view class="info-row">
                    <text class="label">门店名称：</text>
                    <text class="value">{{ item.storeName }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
    data() {
        return {
            list: []
        }
    },
    onShow() {
        this.getData()
    },
    methods: {
        goUrl(url) {
            uni.navigateTo({
                url: url
            })
        },
        getData() {
            req.showLoading()
            req.getRequest("/shopApi/inv/getDocumentNumList", {}, res => {
                uni.hideLoading()
                this.list = res.data
            })
        }
    }
}
</script>

<style lang="scss" scoped src="./inventoryCheckList.scss"></style>