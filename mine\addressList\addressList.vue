<template>
    <view>
        <view class="address">
            <view style="display: flex;align-items: center;">
                <text class="iconfont" style="color:red;margin-right:10rpx;">&#xe727;</text>
                <view>我的地址</view>
            </view>
            <view class="address_item" v-for="(item, index) in list" :key="index" @click="upIsDefault(item)">
                <view class="address_item_left">
                    <view class="address_item_left_top" style="align-items: flex-start;margin-right: 20rpx;">
                        <view style="width: 350rpx;">{{ item.address }}{{ item.house }}</view>
                        <view
                            style="background: #F2FAFF; color: #0B78FF;font-size: 24rpx;border:1px solid #0B78FF;border-radius: 30rpx;padding: 0 10rpx;margin-left:15rpx;margin-top: 6rpx;"
                            v-if="item.label">
                            {{ item.label }}</view>
                    </view>
                    <view class="address_item_left_bottom" style="color:#999;font-size:26rpx;margin-top:10rpx;">
                        <view style="margin-right:10rpx;">{{ item.name }}</view>
                        <view>{{ [item.phone] }}</view>
                    </view>
                </view>
                <view style="flex:1;display: flex;justify-content: flex-end;">
                    <text class="iconfont" style="font-size:40rpx;color:#999;margin-right: 20rpx;"
                        @click.stop="goToAddAddress(item.id)">&#xe7e5;</text>
                    <text class="iconfont" style="font-size:40rpx;color:#EA1306;"
                        @click.stop="deleteAddAddress(item.id)">&#xe6a0;</text>
                </view>
            </view>
        </view>
        <view class="add-address">
            <button class="add-btn" @click="goToAddAddress(false)">添加地址</button>
        </view>
    </view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
    data() {
        return {
            choice: false,
            updateAddress: false,
            list: []
        }
    },
    onLoad(options) {
        if (Boolean(options.choice)) {
            this.choice = Boolean(options.choice)
        };
        if (Boolean(options.updateAddress)) {
            this.updateAddress = Boolean(options.updateAddress)
        };
    },
    onShow() {
        this.getlist();
    },
    methods: {
        // 跳转到添加地址页面
        goToAddAddress(id) {
            if (!id) {
                uni.navigateTo({
                    url: `/mine/addressDetail/addressDetail`
                })
            } else {
                uni.navigateTo({
                    url: `/mine/addressDetail/addressDetail?id=${id}`
                })
            }
        },
        getlist() {
            req.showLoading();
            req.getRequest("/shopApi/mp/address/list", {
                page: 1,
                limit: 20
            }, res => {
                console.log(res, "获取收获列表");
                this.list = [...res.data.list];
                uni.hideLoading();
            })
        },
        deleteAddAddress(id) {
            req.msgConfirm('确定删除该收获地址', () => {
                req.postRequest1("/shopApi/mp/address/delete", {
                    id: id,
                }, res => {
                    console.log(res, "删除");
                    this.getlist();
                    req.msg("删除成功");
                });
            });
        },
        // 点击选择地址
        upIsDefault(item) {
            if (!this.choice) return;
            console.log(item);
            let pages = getCurrentPages();
            var prevPage = pages[pages.length - 2];
            console.log(1111, prevPage);
            prevPage.$vm.address = item;
            if (prevPage.$vm.createByShop && prevPage.$vm.mode == 1) {
                //及时达美团校验
                prevPage.$vm.createByShop()
            }
            if (this.updateAddress) {
                prevPage.$vm.showupAddress = true;
            }
            uni.navigateBack();
        },
    }
}
</script>

<style lang="scss" scoped>
.address {
    margin: 20rpx;
    padding: 20rpx;
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
}

.address_item {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #F2F3F5;
    padding: 25rpx 40rpx;

    .address_item_left {

        .address_item_left_top,
        .address_item_left_bottom {
            display: flex;
            align-items: center;
        }
    }

    &:last-child {
        border-bottom: none;
    }
}

.add-address {
    position: fixed;
    bottom: 40rpx;
    left: 0;
    right: 0;
    padding: 0 40rpx;

    .add-btn {
        background: #EA1306;
        color: #fff;
        border-radius: 45rpx;
        height: 90rpx;
        line-height: 90rpx;
        font-size: 32rpx;
    }
}
</style>