.seckill-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
  
  .product-list {
    padding: 20rpx;
    
    .product-item {
      margin-bottom: 20rpx;
      
      .product-card {
        background: #fff;
        border-radius: 16rpx;
        overflow: hidden;
        
        .image-wrapper {
          position: relative;
          width: 100%;
          height: 400rpx;
          
          .product-image {
            width: 100%;
            height: 100%;
            display: block;
            margin:0 auto;
          }
          
          .yibao-tongchou {
            position: absolute;
            top: 20rpx;
            left: 20rpx;
            background: #FF4B4B;
            color: #fff;
            padding: 4rpx 12rpx;
            border-radius: 8rpx;
            font-size: 24rpx;
          }
          
          .date-wrapper {
            position: absolute;
            right: 20rpx;
            bottom: 20rpx;
            background: rgba(255,75,75,0.9);
            padding: 8rpx 16rpx;
            border-radius: 8rpx;
            color: #fff;
            display: flex;
            flex-direction: column;
            align-items: center;
          }
        }
        
        .product-info {
          padding: 20rpx;
          
          .title {
            font-size: 28rpx;
            font-weight: bold;
            margin-bottom: 16rpx;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
          }
          
          .stock-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16rpx;
            
            .stock-item {
              .stock {
                color: #999;
                font-size: 24rpx;
                margin-right: 20rpx;
              }
            }
            
            .limit {
              color: #FF4B4B;
              font-size: 24rpx;
            }
          }
          
          .price-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .price {
              .symbol {
                color: #FF4B4B;
                font-size: 24rpx;
              }
              
              .number {
                color: #FF4B4B;
                font-size: 36rpx;
                font-weight: bold;
                margin: 0 8rpx;
              }
              
              .original {
                color: #999;
                font-size: 24rpx;
                text-decoration: line-through;
              }
            }
            
            .price_button {
              background: linear-gradient(90deg, #FF4B4B, #FF6B6B);
              color: #fff;
              padding: 12rpx 32rpx;
              border-radius: 30rpx;
              font-size: 26rpx;
            }
          }
        }
      }
    }
  }
  
  .empty-tip {
    text-align: center;
    color: #999;
    padding: 40rpx 0;
  }
}
