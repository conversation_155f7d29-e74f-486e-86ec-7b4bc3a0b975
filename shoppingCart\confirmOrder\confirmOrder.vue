<template>
	<!-- 禁止滚动穿透 -->
	<view class="confirmOrder" :style="{ 'padding-top': statusBarHeight + 'px' }">
		<customTopNav :title="'确认订单'" :color="'#fff'" :bgColor="show_bgcolor ? '#e50101' : 'transparent'"
			:backShow="true">
		</customTopNav>
		<view style="padding:40rpx 20rpx 0;background:transparent;">
			<view class="distribution_tab" v-if="mode === 1">
				<view style="display: flex;justify-content: space-between;">
					<view class="tab_item left" :class="tabValue === 3 ? 'tab_item_active' : ''"
						@click="changTabMode(3)" style="left:0;position: absolute;">
						<i class="iconfont" style="margin-right: 10rpx;color: #333333;"
							v-if="tabValue === 3">&#xe603;</i>
						配送到家
					</view>
					<view class="tab_item" :class="tabValue === 1 ? 'tab_item_active' : ''" @click="changTabMode(1)"
						style="right:0;position: absolute;">
						<i class="iconfont" style="margin-right: 10rpx;color: #333333;"
							v-if="tabValue === 1">&#xe602;</i>
						到店自提
					</view>
				</view>
				<!-- <view
					style="display: flex;justify-content: space-between;padding: 48rpx 40rpx;box-sizing: border-box;margin-bottom: 20rpx;"
					@click="goAddress" v-if="tabValue === 3">
					<view v-if="address.id">
						<view>{{ address.address }}{{ address.house }}</view>
						<view style="color: #999999;font-size: 28rpx;">{{ address.name }} {{ address.phone }}</view>
					</view>
					<view class="text_" v-else>请选择收货地址</view>
					<uni-icons type="right" size="15"></uni-icons>
				</view> -->
				<view style="padding: 98rpx 40rpx 48rpx;box-sizing: border-box;margin-bottom: 20rpx;" v-if="mode === 1">
					<view style="display: flex;justify-content: space-between;padding-left: 24rpx;"
						@click="goUrl('/other/storeList/storeList')">
						<view class="address_">
							<view>{{ store.storeName }}<uni-icons type="right" size="15"></uni-icons></view>
							<view style="font-weight: 400;color: #4E5969;font-size: 24rpx;line-height: 30rpx;">
								{{ store.address }}
							</view>
						</view>
						<!-- <view style="width: 148rpx;position: relative;">
							<image
								src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241119/d7dcea4b7912436f9f68224e6e1044cd.png"
								style="width: 148rpx;" mode="widthFix"></image>
							<view
								style="position: absolute;font-size: 18rpx;color: #4E5969;top: 20rpx;left: 55%;transform: translateX(-50%);width:68%;">
								距您{{ formatDistance(store.mDistance) }}</view>
						</view> -->
					</view>
					<view
						style="display: flex;justify-content: space-between;padding: 48rpx 20rpx 0;box-sizing: border-box;margin-bottom: 20rpx;"
						@click="goAddress" v-if="tabValue === 3">
						<view v-if="address.id">
							<view>{{ address.address }}{{ address.house }}</view>
							<view style="color: #999999;font-size: 28rpx;">{{ address.name }} {{ address.phone }}</view>
						</view>
						<view class="text_" v-else>请选择收货地址</view>
						<uni-icons type="right" size="15"></uni-icons>
					</view>
					<view v-if="tabValue === 1" class="phone_">
						<view style="display: flex;height: 56rpx;align-items: center;font-size: 28rpx;">
							自提人 <text v-if="!editName" style="margin-left: 20rpx;">{{ userName }}</text>
							<input v-model="userName" class="phone_input" v-else @blur="editName = false" type="tel" />
							<i class="iconfont" style="font-size: 28rpx;margin-left: 20rpx;"
								@click="editName = true">&#xe61d;</i>
						</view>
						<view style="display: flex;height: 56rpx;align-items: center;font-size: 28rpx;">
							自提电话 <text v-if="!editPhone" style="margin-left: 20rpx;">{{ phone }}</text>
							<input v-model="phone" class="phone_input" v-else @blur="editPhone = false" type="tel" />
							<i class="iconfont" style="font-size: 28rpx;margin-left: 20rpx;"
								@click="editPhone = true">&#xe61d;</i>
						</view>
					</view>
				</view>
			</view>
			<view class="post_mode" v-else-if="mode === 2">
				<view
					style="display: flex;justify-content: center;height: 64rpx;background: rgba(228, 42, 46, 0.04);width: 710rpx;align-items: center;">
					<i class="iconfont" style="color: #E42A2E;margin-right: 8rpx;font-size: 24rpx;">&#xe623;</i>邮寄配送
				</view>
				<image
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241119/3dd9334066b8431193c5d88f3590ff73.png"
					style="height: 88rpx;width: 88rpx;position: absolute;bottom: 28rpx;right: 76rpx;">
				</image>
				<view
					style="padding: 40rpx 20rpx 40rpx 40rpx;display: flex;align-items: center;justify-content: space-between;"
					@click="goAddress">
					<view class="address_">
						<view style="display: flex;" v-if="address.id">
							<view class="tag" v-if="address.label">{{ address.label }}</view>
							<view class="text_">
								<view>{{ address.address }}{{ address.house }}</view>
								<view style="color: #999999;font-size: 24rpx;">{{ address.name }} {{ address.phone }}
								</view>
							</view>
						</view>
						<view v-else style="line-height: 80rpx;">请选择收货地址</view>
					</view>
					<uni-icons type="right" size="20"></uni-icons>
				</view>
			</view>
			<view class="getTime" v-if="(tabValue === 1 || tabValue === 3) && mode == 1 && false">
				<view>{{ tabValue === 3 ? "送达时间" : "自提时间" }}</view>
				<view @click="open">
					<text style="color: #EA1306;font-weight: normal;">
						预计{{ dateName }}{{ time }}
					</text>
					<uni-icons type="right" size="15"></uni-icons>
				</view>
			</view>
			<view style="width: 710rpx;border-radius: 16rpx;background: #FFFFFF;margin-bottom: 20rpx;">
				<uni-section title="商品信息" type="line">
					<view class="goods_info">
						<view class="goods_" v-for="(item, index) in goodsInfo.goodsList" :key="index">
							<view class="goods_img">
								<image :src="item.pic" style="width: 158rpx;height: 136rpx;"
									:style="{ filter: item.prescriptionDrug == 1 ? 'blur(5px)' : '' }" mode="aspectFit">
								</image>
								<image style="position: absolute;top:0;left:0;width: 158rpx;height: 136rpx;"
									v-if="item.prescriptionDrug == 1"
									src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240111/a338c782689a421b8e9ab0848c3c5c10.png">
								</image>
								<!-- <view class="discount">8.5折</view> -->
							</view>
							<view style="width: 430rpx;">
								<view style="margin-bottom: 30rpx;">
									<view style="display: flex;">
										<i class="iconfont icon" v-if="item.isOtc === 1">&#xe606;</i>{{ item.title }}
									</view>
									<view style="color: #4E5969;font-size: 24rpx;" v-if="item.doseSpec">规格:{{
										item.doseSpec }}</view>
								</view>
								<view style="display: flex;justify-content: space-between;width: 430rpx;">
									<view>
										<text style="color: #EA1306;font-weight: 600;font-size: 32rpx;">¥{{ item.price1
										}}</text>
										<text
											style="color: #999999;font-size: 24rpx;text-decoration: line-through;margin-left: 10rpx;">¥{{
												item.salePrice }}</text>
									</view>
									<view style="color: #333333;font-size: 26rpx;">x{{ item.quantity }}</view>
								</view>
							</view>
						</view>
						<view class="details">
							<view>商品金额</view>
							<view style="font-weight: 600;">¥{{ goodsInfo.sumPrice ? goodsInfo.sumPrice.toFixed(2) :
								'0.00' }}</view>
						</view>
						<view v-if="tabValue === 3 || mode === 2" class="details">
							<view>配送费<text class="distribution" v-if="goodsInfo.fullSubtractName">{{
								goodsInfo.fullSubtractName }}</text></view>
							<view style="font-weight: 600;"><text
									style="text-decoration: line-through;color: #999999;margin-right: 12rpx;">¥{{
										goodsInfo.deliveryPrice }}</text>¥{{ chargePrice }}<uni-icons type="right"
									size="14"></uni-icons>
							</view>
						</view>
						<view class="details" v-if="false">
							<view style="display: flex;"><i class="iconfont "
									style="color: #F99B07;margin-right: 8rpx;">&#xe612;</i>积分
							</view>
							<view style="color: #999999;">暂无可用<uni-icons type="right" size="14"></uni-icons></view>
						</view>
						<view class="details" v-if="false">
							<view style="display: flex;"><i class="iconfont "
									style="color: #9B1FFA;margin-right: 8rpx;">&#xe628;</i>立减
							</view>
							<view style="color: #999999;">暂无可用<uni-icons type="right" size="14"></uni-icons>
							</view>
						</view>
						<view class="details" @click="goCoupon">
							<view style="display: flex;"><i class="iconfont "
									style="color: #FD1B26;margin-right: 8rpx;">&#xe613;</i>优惠劵
							</view>
							<view style="color: #FD1B26;">-￥{{ goodsInfo.couponPrice ? goodsInfo.couponPrice.toFixed(2)
								: 0.00
							}}<uni-icons type="right" size="14"></uni-icons>
							</view>
						</view>
						<view class="details"
							style="justify-content: flex-end;border-top: 1px solid #F2F3F5;padding-top: 30rpx;">
							小计:<text style="font-weight: 600;">¥{{ goodsInfo.amountPrice ?
								goodsInfo.amountPrice.toFixed(2) : '0.00' }}</text></view>
					</view>
				</uni-section>
			</view>
			<view class="payMode">
				<uni-section title="支付方式" type="line">
					<view style="padding: 0 16rpx 40rpx 34rpx;">
						<radio-group @change="radioChange">
							<label class="uni-list-cell uni-list-cell-pd" v-for="(item, index) in payitems"
								:key="item.value">
								<view
									style="display: flex;justify-content: space-between;border-top: 1px solid #F2F3F5;align-items: center;padding: 20rpx 14rpx;">
									<view style="display: flex;">
										<view class="text_icon">
											<i class="iconfont" v-if="item.value == 2"
												style="color: #09BB07;">&#xe616;</i>
											<i class="iconfont" v-if="item.value == 7"
												style="color: #007AFF;font-size:50rpx;">&#xe638;</i>
										</view>
										<view class="text_">{{ item.text }}</view>
									</view>
									<radio :value="item.value" :checked="item.value === payType"
										style="transform:scale(0.8)" color="#EA1306" />
								</view>
							</label>
						</radio-group>
					</view>
				</uni-section>
			</view>
			<view style="margin-bottom: 20rpx;" v-if="payType !== 7">
				<uni-section title="余额信息" type="line">
					<view style="display: flex;justify-content: space-between;padding:22rpx 32rpx;">
						<view style="display: flex;align-items: center;">
							<view style="margin-right: 8rpx;">
								<i class="iconfont" style="color: #EA1306;">&#xe61e;</i>
							</view>
							<view class="balance">
								<view>余额抵扣</view>
								<view style="color: #999999;font-size: 24rpx;line-height: 32rpx;">
									可用余额: ¥{{ balance }}
									<text v-if="useBalance" style="margin-left: 10rpx;color: #EA1306;">
										(已抵扣: ¥{{ deductionAmount }})
									</text>
								</view>
							</view>
						</view>
						<switch @change="switchChange" :checked="useBalance"
							style="transform:scale(0.6);color:#EA1306;" />
					</view>
				</uni-section>
			</view>
			<view style="margin-bottom: 80rpx;">
				<uni-section title="订单备注" type="line">
					<template v-slot:right v-if="false">
						<view style="color: #999999;font-size: 28rpx;">更多备注<uni-icons type="right"
								size="12"></uni-icons>
						</view>
					</template>
					<textarea v-model="remarks" auto-height
						style="border: 1px solid #999999;border-radius: 8rpx;margin-left: 30rpx;padding: 10rpx;margin-bottom: 10rpx;"
						placeholder="请输入"></textarea>
					<view style="display: flex;padding: 10rpx 20rpx 30rpx ;">
						<view class="mark" v-for="(item, index) in remarksList" :key="index">
							<!-- <i class="iconfont" style="color: #4E5969;margin-right: 8rpx;">&#xe618;</i> -->
							<view @click="confirmRemarks(item.value)">{{
								item.value }}
							</view>
						</view>
						<!-- <view class="mark">
							<i class="iconfont" style="color: #4E5969;margin-right: 8rpx;">&#xe61b;</i>不要敲门
						</view> -->
					</view>
				</uni-section>
			</view>
			<view class="other" v-if="false">
				<uni-section title="其他" type="line">
					<view style="padding:22rpx 32rpx ;">
						<view style="display: flex;justify-content: space-between;padding: 0 10rpx 20rpx;">
							<view>
								<view>联系不上放门口</view>
								<view class="text_">联系不上时，可直接放门口</view>
							</view>
							<switch @change="switchChange" style="transform:scale(0.6);color:#EA1306;" />
						</view>
						<view
							style="display: flex;justify-content: space-between;border-top:1px solid #F2F3F5;padding:20rpx 10rpx ">
							<view>
								<view style="display: flex;"><i class="iconfont "
										style="color: #09BB07;">&#xe619;</i>环保小票</view>
								<view class="text_">选择后，小票将隐藏商品及价格信息</view>
							</view>
							<switch @change="switchChange" style="transform:scale(0.6);color:#EA1306;" />
						</view>
						<view
							style="display: flex;justify-content: space-between;border-top:1px solid #F2F3F5;padding:20rpx 10rpx; ">
							<view>
								<view>号码保护</view>
								<view class="text_">选择后，将对骑手隐藏您的真实手机号</view>
							</view>
							<switch @change="switchChange" style="transform:scale(0.6);color:#EA1306;" />
						</view>
					</view>
				</uni-section>
			</view>
		</view>
		<view style="position: fixed;bottom: 0;width: 750rpx;z-index: 999;">
			<view v-if="hasPrescriptionDrug" class="prescription_drug">订单中包含处方药，需咨询医生开具处方购买</view>
			<view class="pay_btn">
				<view class="text_">
					<text>应付:</text>
					<text style="color: #EA1306;font-weight: 600;">¥{{ goodsInfo.amountPrice ?
						goodsInfo.amountPrice.toFixed(2)
						: '0.00' }}</text>
					<text style="font-size: 24rpx;color: #4E5969;margin-left: 12rpx;">共优惠¥{{ goodsInfo.discountPrice ?
						goodsInfo.discountPrice.toFixed(2) : '0.00' }}</text>
				</view>
				<view class="btn" :class="{ 'btn-disabled': !canSubmitOrder }" :disabled="!canSubmitOrder"
					@click="canSubmitOrder ? submitOrder() : ''">
					{{ hasPrescriptionDrug ? '去开方' : '去支付' }}
				</view>
			</view>
		</view>
		<uni-popup ref="popup" type="bottom" background-color="#fff" borderRadius="40rpx 40rpx 0px 0px">
			<view class="popup_content" :style="{ 'padding-bottom': 'env(safe-area-inset-bottom)' }">
				<i class="iconfont" style="font-size: 24rpx;position: fixed;right: 40rpx;top: 40rpx;color: #333333;"
					@click="close">&#xeb6a;</i>
				<view class="popup_title">
					<view style="font-weight: 600;">选中送达时间</view>
				</view>
				<view style="display: flex;" class="popup_details">
					<view
						style="width: 250rpx;height: 700rpx;background: #F6F7FB;margin-right: 20rpx;text-align: center;font-weight: 600;font-size: 28rpx;">
						<view style="padding: 20rpx 0rpx;" :class="dateIndex == 0 ? 'active' : ''"
							@click="dateIndex = 0">
							今天 {{
								todayDate.split("-")[1] + "月"
								+
								todayDate.split("-")[2] + '日' }}</view>
						<view style="padding: 20rpx 0rpx;" :class="dateIndex == 1 ? 'active' : ''"
							@click="dateIndex = 1">明天 {{
								tomorrowDate.split("-")[1] + "月" +
								tomorrowDate.split("-")[2] + '日' }}</view>
					</view>
					<scroll-view scroll-y="true" style="width: 430rpx;height: 700rpx;font-weight: 600;">
						<view style="padding: 30rpx 20rpx;" v-for="(item, index) in timePeriod" :key="index"
							:class="tiemIndex === index ? 'active_time' : ''" @click="changeTime(item, index)">{{
								item.time }}
						</view>
					</scroll-view>
				</view>
				<view class="btn_time" @click="confirm_tiem">确认</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
const app = getApp();
const req = require("../../utils/request.js");
import customTopNav from "@/components/custom-top-nav/custom-top-nav" // 自定义头部
export default {
	data() {
		return {
			toform: "",
			mode: "",
			phone: "",
			editPhone: false,
			userName: "",
			editName: false,
			goodslistId: "",
			merchantId: '',
			discountType: "",
			orderId: "",
			tabValue: 1,
			payitems: [
				{ value: 2, text: "微信支付" },
			],
			todayDate: "",
			tomorrowDate: "",
			timePeriod: [],
			dateIndex: 0,
			tiemIndex: 0,
			dateName: "今天",
			time: "",
			date: "",
			goodsInfo: {
				sumPrice: 0,
				amountPrice: 0,
				discountPrice: 0,
				deliveryPrice: 0
			},
			pocketPrice: 0.00,   // 应付
			chargePrice: 0.00,   //配送费
			store: {},  // 当前门店
			address: {},
			hasPrescriptionDrug: false, // 是否包含处方药
			balance: 0, // 用户余额
			useBalance: false, // 是否使用余额抵扣
			deductionAmount: 0, // 余额抵扣金额
			couponId: "", // 使用优惠券id
			remarksList: [],
			remarks: "",
			canSubmitOrder: true, // 添加控制订单提交状态
			payType: 2, // 支付方式,默认2微信支付
			getCode: false, // 获取医保码
			authCode: "", // 医保码
			show_bgcolor: false,
			statusBarHeight: ''
		}
	},
	components: {
		customTopNav
	},
	onPageScroll(e) {
		// console.log(e);
		if (e.scrollTop > 10) { //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色显示
			this.show_bgcolor = true;
		} else {
			this.show_bgcolor = false;
		}
	},
	onLoad(options) {
		console.log(options);
		this.mode = Number(options.mode);
		this.goodslistId = options.goodslist;
		this.merchantId = options.merchantId ? Number(options.merchantId) : req.getStorage("currentStore").id
		this.discountType = Number(options.discountType);
		if (options.tabMode) {
			this.tabValue = Number(options.tabMode);
		};
		if (options.toform) {
			this.toform = Number(options.toform)
		};
		this.couponId = options.couponId ? options.couponId : "";
		this.affirmOrder();
		if (this.tabValue === 3 || this.mode === 2) {
			this.getaddresslist();
		};
		this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 50;
	},
	onShow() {
		if (this.getCode) {
			if (app.globalData.authCode) {
				console.log("进来了");
				this.authCode = app.globalData.authCode
				this.getCode = false
				req.showLoading()
				req.postRequest("/shopApi/medical/order/accountPayment", {
					orderId: this.orderId,
					qrcode: this.authCode
				}, res => {
					uni.hideLoading()
					uni.redirectTo({
						url: '/shoppingCart/medicalOrder/medicalOrder?orderId=' + this.orderId
					})
				})
			} else {
				uni.redirectTo({
					url: '/mine/order/order'
				})
			}

		}
		this.generateTimeSlots();
		if (!req.getStorage("currentStore")) {
			this.getDefaultStore();
		};
		this.store = req.getStorage("currentStore");
		this.merchantId = this.mode == 2 ? 60 : this.store.id;
		let currentDate = new Date();
		// 今天日期
		this.todayDate = currentDate.toISOString().split('T')[0];  // 格式: YYYY-MM-DD
		// 明天日期
		let tomorrow = new Date();
		tomorrow.setDate(currentDate.getDate() + 1);
		this.tomorrowDate = tomorrow.toISOString().split('T')[0];
		// 当前时间的分钟数
		this.currentTimeInMinutes = currentDate.getHours() * 60 + currentDate.getMinutes(); // 转换为分钟数（从午夜开始）
		console.log(this.todayDate, this.tomorrowDate);
		if (req.getStorage("userInfo")) {
			this.phone = req.getStorage("userInfo").phone;
			this.userName = req.getStorage("userInfo").memName;
		};
		this.getBalance(); // 获取余额
		this.showUpcomingTimeSlots();
		this.getRemarks();
	},
	methods: {
		createByShop() {
			req.getRequest("/shopApi/create/order/pre/createByShop", {
				shopId: this.store.shopId,
				receiverName: this.address.name,
				receiverAddress: this.address.address + this.address.house,
				receiverPhone: this.address.phone,
				goodsValue: this.goodsInfo.amountPrice,
				receiverLng: Number(this.address.lng).toFixed(6).split(".").join(""),
				receiverLat: Number(this.address.lat).toFixed(6).split(".").join(""),
			}, res => {
				if (res.code == 200 && res.msg == 'success') {
					this.canSubmitOrder = true; // 在配送范围内
				} else {
					this.canSubmitOrder = false; // 超出配送范围
					uni.showModal({
						title: '提示',
						content: '超出配送范围，请重新选择地址',
						showCancel: false,
						success: function (res) {
							if (res.confirm) {
								console.log('用户点击确定');
							}
						}
					});
				}
			})
		},
		// 送达时间 - 打开
		open() {
			this.$refs.popup.open('bottom');
		},
		// 送达时间 - 关闭
		close() {
			this.$refs.popup.close();
		},
		// 选择时间段
		changeTime(item, index) {
			// console.log(item, "选择时间段");
			if (this.dateIndex === 0 && item.startTimeInMinutes <= this.currentTimeInMinutes) {
				return req.msg("当前时间段不可选择")
			};
			this.tiemIndex = index;
			this.time = item.time;
		},
		// 送达时间确认
		confirm_tiem() {
			this.close();
			if (this.dateIndex === 0) {
				this.date = `${this.todayDate} ${this.time}`
			} else if (this.dateIndex === 1) {
				this.date = `${this.tomorrowDate} ${this.time}`
			};
			this.dateName = this.date.split(" ")[0] == this.todayDate ? "今天" : "明天";
			console.log(this.date, "送达时间");
		},
		// 改变配送方式 - 闪送、自提
		changTabMode(mode) {
			this.tabValue = mode;
			if (this.tabValue === 3) {
				this.getaddresslist().then((res) => {
					console.log(res, "配送----------");
					this.canSubmitOrder = false;
					if (res.data.list.length == 0) return;
					this.createByShop();
				});
			} else {
				this.canSubmitOrder = true;
			}
			this.affirmOrder();
		},
		// 查看优惠券
		goCoupon() {
			// this.goUrl(`/mine/couponList/couponList?formCart=1&cartIds=${this.goodslistId}`);
			uni.navigateTo({
				url: `/mine/couponList/couponList?formCart=1&cartIds=${this.goodslistId}&merchantId=${this.merchantId}`,
				events: {
					// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
					acceptDataFromOpenedPage: (data) => {
						console.log(data, "页面传递");
						this.couponId = data.id && data.id.length > 0 ? data.id.join(',') : data.id;
						this.affirmOrder();
					},
				},
			})
		},
		// 添加备注
		confirmRemarks(value) {
			if (this.remarks) {
				this.remarks = this.remarks + ',' + value;
			} else {
				this.remarks = value;
			};
		},
		// 收货地址
		goAddress() {
			uni.navigateTo({
				url: '/mine/addressList/addressList?choice=true'
			});
		},
		// 自提店列表
		goPickup() {
			uni.navigateTo({
				url: '/shoppingCart/pickupList/pickupList'
			});
		},

		// 提交订单
		submitOrder() {
			req.showLoading();
			console.log(this.date, "预计时间");
			if (this.mode === 1 && this.tabValue === 1) {
				if (!this.userName) return req.msg("请输入你的姓名");
				if (!this.phone || !req.isPhone(this.phone)) return req.msg("请输入正确的手机号码");
			};
			if (this.mode === 2 || (this.tabValue === 3 && this.mode === 1)) {
				if (!this.address.id) return req.msg("请选择你的收货地址");
			}
			let parme = {
				ids: this.goodslistId.split(','), // 商品ID数组
				mode: this.mode == 1 ? this.tabValue : this.mode, // 配送方式 1-自提/配送 2-邮寄
				orderType: 1, // 订单类型 1-普通订单
				phone: this.tabValue == 1 && this.mode === 1 ? this.phone : this.address.phone, // 联系电话
				userName: this.tabValue == 1 && this.mode === 1 ? this.userName : this.address.name, // 用户姓名
				merchantId: this.mode == 2 ? 60 : this.store.id, // 门店ID,快递传60
				addressId: this.address.id, // 收货地址ID
				latitude: req.getStorage("location").lat,
				longitude: req.getStorage("location").lng,
				locationAddress: req.getStorage('location').address,
				distance: this.store.mDistance,
				remarks: this.remarks, // 订单备注
				remark: this.remark, // 订单备注
				node: this.node, // 买家留言
				prescriptionDrug: this.hasPrescriptionDrug ? 1 : 0,
				useBalance: this.useBalance ? 1 : 0, // 是否使用余额
				couponId: '', // 用户优惠券id
				balancePayMoney: this.deductionAmount == 0 ? "" : this.deductionAmount,// 抵扣金额
				payState: this.payType, // 支付方式
				couponUserIds: this.couponId.split(','),
				pid: req.getStorage("pid") || ""
			};
			console.log(parme);
			req.postRequest("/shopApi/mp/order/save", parme, res => {
				uni.hideLoading();
				console.log(res);
				if (res.code == 200) {
					if (!this.toform) {
						let pages = getCurrentPages();
						var prevPage = pages[pages.length - 2];
						console.log(prevPage);
						prevPage.$vm.checked = [];
						prevPage.$vm.checkedAll = false;
						prevPage.$vm.detailInfo = {};
						if (prevPage.$vm.$refs.popup) {
							prevPage.$vm.$refs.popup.close();
						}
					}
					this.orderId = res.data;
					if (this.hasPrescriptionDrug) {
						uni.redirectTo({
							url: `/shoppingCart/tcConsultation/tcConsultation?orderId=${this.orderId}&payState=${this.payType}`
						})
					} else if (this.payType === 7) {
						uni.navigateToMiniProgram({
							appId: 'wxe183cd55df4b4369',
							path: `auth/pages/bindcard/auth/index?openType=getAuthCode&bizType=04107&cityCode=440108&channel=30000175&orgChnlCrtfCodg=BqK1kMStlhVDgN2uHf4EsLK/F2LjZPYJ81nK2eYQqxuHgpiS+wkhUBWGPoaYCwz0&orgCodg=P44010600664&orgAppId=1I5K5N9JE1T84460C80A00000ED5F971`,
							envVersion: req.env.NODE_ENV == 'product' ? 'release' : 'trial',
							success: res => {
								this.getCode = true
							},
							fail: () => {
								uni.redirectTo({
									url: '/shoppingCart/orderDetails/orderDetails?id=' + this.orderId
								})
							}
						})
					} else {
						this.payOrder()
					}
				}
			})
		},
		payOrder() {
			req.payOrder(this.orderId, res => {
				if (this.tabValue == 3) {
					req.getRequest("/shopApi/create/order/createByShop", {
						id: this.orderId
					}, res => {
						if (res.code == 200) {
							uni.redirectTo({
								url: `/shoppingCart/pymentStatus/pymentStatus?orderId=${this.orderId}`
							});
						}
					})
				} else {
					uni.redirectTo({
						url: `/shoppingCart/pymentStatus/pymentStatus?orderId=${this.orderId}`
					});
				}
			});
		},
		// 确认订单信息
		affirmOrder() {
			req.getRequest("/shopApi/mp/order/affirmOrder", {
				ids: this.goodslistId,
				storeId: this.mode == 2 ? 60 : this.merchantId,
				mode: this.mode == 2 ? this.mode : this.tabValue,
				couponUserIds: this.couponId
			}, res => {
				console.log(res, '确认订单信息');
				this.goodsInfo = res.data;

				// 判断是否所有商品都支持医保支付
				const showMedicalInsurance = this.goodsInfo.goodsList.every(item =>
					item.isOverallPlanning == 1 || item.isMedicalInsurance == 1
				);

				// 根据判断结果设置支付方式选项
				this.payitems = showMedicalInsurance ? [
					{ value: 2, text: "微信支付" },
					// { value: 7, text: "移动医保支付" }
				] : [
					{ value: 2, text: "微信支付" }
				];

				// 确保所有金额都是数字类型
				this.goodsInfo.sumPrice = Number(this.goodsInfo.sumPrice || 0);
				this.goodsInfo.amountPrice = Number(this.goodsInfo.amountPrice || 0);
				this.goodsInfo.discountPrice = Number(this.goodsInfo.discountPrice || 0);
				this.goodsInfo.deliveryPrice = Number(this.goodsInfo.deliveryPrice || 0);
				this.goodsInfo.deliverySubtractPrice = Number(this.goodsInfo.deliverySubtractPrice || 0);

				this.chargePrice = (this.goodsInfo.deliveryPrice - this.goodsInfo.deliverySubtractPrice).toFixed(2);
				this.goodsInfo.deliveryPrice = this.goodsInfo.deliveryPrice.toFixed(2);

				this.checkPrescriptionDrug();
			})
		},
		//获取默认门店
		getDefaultStore() {
			req.getRequest("/shopApi/home/<USER>", {}, res => {
				this.store = res.data
				req.setStorage("currentStoreId", res.data.id);
				req.setStorage("currentStore", res.data);
			})
		},
		// 获取收获地址
		getaddresslist() {
			return new Promise((resolve, reject) => {
				req.getRequest("/shopApi/mp/address/list", {
					page: 1,
					limit: 20
				}, res => {
					console.log(res, "获取收获列表");
					if (res.data.list.length) {
						this.address = res.data.list[0];
					}
					resolve(res);
				})
			})
		},
		// 禁止穿透滚动 - 遮罩
		radioChange(e) {
			this.payType = Number(e.detail.value);
			// 如果选择医保支付,关闭余额支付
			if (this.payType === 7) {
				this.useBalance = false;
				this.deductionAmount = 0;
				this.calculateFinalAmount();
			}
		},
		// 获取备注
		getRemarks() {
			req.getRequest("/shopApi/dict/listMap?code=order_remarks", {}, res => {
				console.log(res, "获取备注");
				this.remarksList = [...res.data];
			})
		},
		switchChange(e) {
			// 如果选择了医保支付,不允许开启余额支付
			if (this.payType === 7) {
				uni.showToast({
					title: '医保支付不可使用余额',
					icon: 'none'
				});
				return;
			}

			this.useBalance = e.detail.value;
			if (this.useBalance) {
				// 计算可抵扣金额 - 取余额和订单金额的较小值
				this.deductionAmount = Math.min(this.balance, this.goodsInfo.amountPrice);
			} else {
				this.deductionAmount = 0;
			}
			this.calculateFinalAmount();
		},
		calculateFinalAmount() {
			if (this.useBalance) {
				let newAmount = Number(this.goodsInfo.amountPrice) - Number(this.deductionAmount);
				this.goodsInfo.amountPrice = Number(newAmount);
			} else {
				this.affirmOrder();
			}
		},
		goUrl(url) {
			uni.navigateTo({
				url: url
			})
		},
		formatDistance(distance) {
			if (!distance) return '0m';
			if (distance >= 1000) {
				return (distance / 1000).toFixed(1) + 'km';
			}
			return Math.floor(distance) + 'm';
		},
		// 检查是否包含处方药
		checkPrescriptionDrug() {
			if (this.goodsInfo && this.goodsInfo.goodsList) {
				this.hasPrescriptionDrug = this.goodsInfo.goodsList.some(item => item.prescriptionDrug === 1);
			}
		},
		// 获取用户余额
		getBalance() {
			req.getRequest("/shopApi/mp/user/myBalance", {}, res => {
				if (res.code === 200) {
					this.balance = res.data.money;
				}
			})
		},
		// 生成时间段并将其放入timePeriod数组
		generateTimeSlots() {
			const slots = [];
			const startHour = 9;  // 假设从9点开始
			const endHour = 21;   // 假设到22点结束
			const interval = 30;  // 时间间隔，单位分钟
			let value = 0; // 用于生成 value
			for (let hour = startHour; hour <= endHour; hour++) {
				for (let minute = 0; minute < 60; minute += interval) {
					// 起始时间
					let startFormattedHour = hour < 10 ? `0${hour}` : hour;
					let startFormattedMinute = minute === 0 ? '00' : '30';
					let startTime = `${startFormattedHour}:${startFormattedMinute}`;

					// 结束时间
					let endMinute = minute + interval;
					let endFormattedMinute = endMinute === 60 ? '00' : '30';
					let endHourAdjusted = endMinute === 60 ? hour + 1 : hour;
					let endFormattedHour = endHourAdjusted < 10 ? `0${endHourAdjusted}` : endHourAdjusted;
					let endTime = `${endFormattedHour}:${endFormattedMinute}`;

					// 生成时间段（例如：09:00-09:30）
					// slots.push(`${startTime}-${endTime}`);
					// 将时间段和对应的value放入timePeriod
					this.timePeriod.push({
						time: `${startTime}-${endTime}`,
						value: value,
						startTimeInMinutes: hour * 60 + minute // 转换开始时间为分钟数 - 判断超过当前时间段的时间不可选择的
					});
					value++
				}
			}
			// console.log(this.timePeriod, "-------------");
			return this.timePeriod;
		},
		// 显示超过当前时间的时间段
		showUpcomingTimeSlots() {
			let arr = this.timePeriod.filter(slot => slot.startTimeInMinutes > this.currentTimeInMinutes);
			// console.log(arr, "hhhhhhhhhhh");
			this.tiemIndex = arr[0].value;
			this.time = arr[0].time;
		},
	}
}
</script>

<style lang="scss" scoped src="./confirmOrder.scss"></style>
