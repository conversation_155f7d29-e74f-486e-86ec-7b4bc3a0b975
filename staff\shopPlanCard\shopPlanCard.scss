.punch-card-container {
  padding: 30rpx;

  .time-display {
    text-align: center;
    margin-bottom: 40rpx;

    .current-time {
      font-size: 60rpx;
      font-weight: bold;
      color: #333;
    }

    .current-date {
      font-size: 28rpx;
      color: #666;
      margin-top: 10rpx;
    }
  }

  .location-info {
    background: #f8f8f8;
    padding: 20rpx;
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    margin-bottom: 40rpx;

    .location-icon {
      font-size: 36rpx;
      color: #ea1306;
      margin-right: 10rpx;
    }

    .location-text {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }

    .refresh-btn {
      color: #ff6b35;
      font-size: 24rpx;
      padding: 10rpx;
    }
  }

  .photo-upload {
    margin-bottom: 20rpx;
    background: #fff;
    padding: 20rpx;
    border-radius: 12rpx;

    .photo-title {
      font-size: 32rpx;
      color: #333;
      margin-bottom: 20rpx;
      font-weight: bold;
    }

    .photo-container {
      display: flex;
      gap: 20rpx;

      .section-title {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 16rpx;
        display: block;
      }

      // 左侧参考图
      .photo-left {
        .reference-image {
          width: 200rpx;
          height: 200rpx;
          border-radius: 8rpx;
          background-color: #f8f8f8;
          object-fit: cover;
        }
      }

      // 右侧上传区
      .photo-right {
        flex: 1;

        .upload-content {
          :deep(.uni-file-picker) {
            .uni-file-picker__container {
              display: flex;
              flex-wrap: wrap;
              gap: 16rpx;
            }

            .file-picker__box {
              width: 200rpx !important;
              height: 200rpx !important;
              margin: 0 !important;

              .uni-file-picker__box-content {
                width: 100% !important;
                height: 100% !important;
              }
            }

            .is-add {
              border: 2rpx dashed #ddd !important;
              background-color: #fff !important;

              &:active {
                opacity: 0.8;
              }
            }

            .file-picker__progress {
              width: 200rpx !important;
              height: 200rpx !important;
            }

            .file-image {
              width: 200rpx !important;
              height: 200rpx !important;
              margin: 0 !important;
              border-radius: 8rpx;

              image {
                border-radius: 8rpx;
              }
            }

            .icon-del {
              top: -16rpx !important;
              right: -16rpx !important;
              background-color: rgba(0, 0, 0, 0.6) !important;
              border-radius: 50% !important;
              width: 36rpx !important;
              height: 36rpx !important;
              z-index: 1;

              &::before,
              &::after {
                background-color: #fff !important;
              }
            }
          }
        }
      }
    }
  }

  .punch-btn {
    background: #ea1306;
    color: #fff;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    border-radius: 44rpx;
    font-size: 32rpx;
    margin-top: 60rpx;
    margin-bottom: 40rpx;

    &:active {
      opacity: 0.8;
    }
  }

  .record-list {
    .record-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }

    .record-item {
      background: #fff;
      border-radius: 10rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

      .record-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;

        .record-date {
          font-size: 28rpx;
          color: #333;
        }

        .record-status {
          font-size: 24rpx;
        }
      }

      .record-address {
        display: flex;
        align-items: center;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 10rpx;
      }

      .record-photos {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10rpx;

        image {
          width: 160rpx;
          height: 160rpx;
          margin-right: 10rpx;
          margin-bottom: 10rpx;
          border-radius: 8rpx;
        }
      }
    }

    .empty-record {
      text-align: center;
      padding: 40rpx 0;

      image {
        width: 240rpx;
        height: 240rpx;
        margin-bottom: 20rpx;
      }

      text {
        font-size: 28rpx;
        color: #999;
      }
    }
  }
}

.store-select {
  padding: 20rpx;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;

  :deep(.uni-data-select) {
    .uni-select--mask {
      background-color: rgba(0, 0, 0, 0.4);
    }

    .uni-select__input-box {
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 8rpx;
      border: 1px solid #ebeef5;
    }
  }
}

/deep/.uni-section {
  border-radius: 8rpx;
}

/deep/.uni-section .uni-section-header__decoration {
  background-color: #ea1306;
}

/deep/.uni-section__content-title {
  font-size: 32rpx !important;
}
