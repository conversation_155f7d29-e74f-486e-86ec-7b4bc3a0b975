<template>
	<view class="salaryDetails">
		<view style="height: 100rpx;background-color: #fff;margin-bottom: 20rpx;padding: 26rpx  30rpx 0;" @click="open">
			<!-- <picker mode="date" :value="date" :start="startDate" :end="endDate" @change="bindDateChange"
				class="picker_">
				<view class="uni-input">{{ date }}</view>
			</picker> -->
			<view class="picker_">
				<view
					style="width: 52rpx;background: #F9E9E8;border-right: 1rpx solid #E41206;border-radius: 16rpx 0 0 16rpx;">
					<i class="iconfont" style="color: #E41206;">&#xe685;</i>
				</view>
				<view style="text-align: center;width: 168rpx;">{{ date }}</view>
			</view>
		</view>
		<view class="salary_month">
			<view class="month">{{ info.name || '' }}({{ date.split('-')[1] }}月份)</view>
			<view class="info-row">
				<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe670;</text>
				<text class="label">所属门店:</text>
				<text class="value">{{ info.storeName || '' }}</text>
			</view>
			<view class="info-row">
				<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe64f;</text>
				<text class="label">手机号码:</text>
				<text class="value">{{ info.phone || '' }}</text>
			</view>
			<view style="display: flex;">
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe672;</text>
					<text class="label">应出勤天数:</text>
					<text class="value">{{ info.requiredAttendance || 0 }}天</text>
				</view>
			</view>
			<view style="display: flex;">
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe65d;</text>
					<text class="label">实际出勤天数:</text>
					<text class="value">{{ info.actualAttendance || 0 }}天</text>
				</view>
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe654;</text>
					<text class="label">底薪:</text>
					<text class="value">{{ info.money || 0 }}</text>
				</view>
			</view>
			<view style="display: flex;">
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe665;</text>
					<text class="label">迟到分钟:</text>
					<text class="value">{{ info.lateMinute || 0 }}分钟</text>
				</view>
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe66b;</text>
					<text class="label">迟到次数:</text>
					<text class="value">{{ info.lateNum || 0 }}次</text>
				</view>
			</view>
			<view style="display: flex;">
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe62b;</text>
					<text class="label">星级考评:</text>
					<text class="value">{{ info.starLv || 0 }}分</text>
				</view>
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe66d;</text>
					<text class="label">考核:</text>
					<text class="value">{{ info.score || 0 }}分</text>
				</view>
			</view>
			<view style="display: flex;">
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe64e;</text>
					<text class="label">社保补贴:</text>
					<text class="value">¥{{ info.medicalAllowance || 0 }}</text>
				</view>
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe666;</text>
					<text class="label">岗位补贴:</text>
					<text class="value">¥{{ info.jobSubsidy || 0 }}</text>
				</view>
			</view>
			<view style="display: flex;">
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe64b;</text>
					<text class="label">交通补贴:</text>
					<text class="value">¥{{ info.trafficAllowance || 0 }}</text>
				</view>
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe66f;</text>
					<text class="label">餐补补贴:</text>
					<text class="value">¥{{ info.mealAllowance || 0 }}</text>
				</view>
			</view>
			<view style="display: flex;">
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe65a;</text>
					<text class="label">药师驻店补贴:</text>
					<text class="value">¥{{ info.practiceDoctorAllowance || 0 }}</text>
				</view>
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe673;</text>
					<text class="label">药师证补贴:</text>
					<text class="value">¥{{ info.cardAllowance || 0 }}</text>
				</view>
			</view>
			<view style="display: flex;">
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe66a;</text>
					<text class="label">住宿补贴:</text>
					<text class="value">¥{{ info.stayAllowance || 0 }}</text>
				</view>
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe651;</text>
					<text class="label">司龄奖:</text>
					<text class="value">¥{{ info.ageAllowance || 0 }}</text>
				</view>
			</view>
			<view style="display: flex;">
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe65c;</text>
					<text class="label">其他薪资补充:</text>
					<text class="value">¥{{ info.staffWageSupply || 0 }}</text>
				</view>
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe658;</text>
					<text class="label">社保:</text>
					<text class="value">¥{{ info.socialMoney || 0 }}</text>
				</view>
			</view>
			<view style="display: flex;">
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe661;</text>
					<text class="label">缺卡扣款:</text>
					<text class="value">¥{{ info.missingCardWithhold || 0 }}</text>
				</view>
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe64d;</text>
					<text class="label">迟到扣款:</text>
					<text class="value">¥{{ info.beLateWithhold || 0 }}</text>
				</view>
			</view>
			<view style="display: flex;">
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe66c;</text>
					<text class="label">缺勤次数:</text>
					<text class="value">¥{{ info.absenceNumber || 0 }}</text>
				</view>
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe64c;</text>
					<text class="label">缺卡次数:</text>
					<text class="value">{{ info.missingCardNumber || 0 }}次</text>
				</view>
			</view>
			<view style="display: flex;">
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe653;</text>
					<text class="label">应发工资:</text>
					<text class="value">¥{{ info.payableSalary || 0 }}</text>
				</view>
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe664;</text>
					<text class="label">实发工资:</text>
					<text class="value">¥{{ info.actualSalary || 0 }}</text>
				</view>
			</view>
			<view style="display: flex;">
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe64a;</text>
					<text class="label">应发红包:</text>
					<text class="value">¥{{ info.payable || 0 }}</text>
				</view>
				<view class="info-row" style="width: 50%;">
					<text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe65b;</text>
					<text class="label">工资红包:</text>
					<text class="value">¥{{ info.workPay || 0 }}</text>
				</view>
			</view>
		</view>
		<uni-popup ref="popup" type="bottom" background-color="#fff">
			<view style="border-radius: 20rpx;overflow: hidden;">
				<view style="display: flex;padding: 20rpx 40rpx;justify-content: space-between;">
					<view style="color: #333;padding: 10rpx 20rpx;border-radius: 8rpx;" @click="close">
						取消</view>
					<view style="color: #fff;background: #E41206;padding: 10rpx 20rpx;border-radius: 8rpx;"
						@click="changeDate">确定</view>
				</view>
				<picker-view indicator-style="height: 50px;" class="picker-view" :value="pickerValue"
					@change="onChange">
					<picker-view-column>
						<view class="item" v-for="(item, index) in yearList" :key="index">{{ item }}年</view>
					</picker-view-column>
					<picker-view-column>
						<view class="item" v-for="(item, index) in monthList" :key="index">{{ item }}月</view>
					</picker-view-column>
				</picker-view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
const req = require("../../utils/request");
export default {
	data() {
		// const currentDate = this.getDate({
		// 	format: true
		// })
		const currentYear = new Date().getFullYear();
		const currentMonth = new Date().getMonth() + 1;
		const lastMonth = (currentMonth - 1) || 12;
		const lastYear = currentMonth === 1 ? currentYear - 1 : currentYear;
		const formattedLastMonth = lastMonth > 9 ? String(lastMonth) : '0' + lastMonth;
		// const pickerCurrentValue = this.getDate({
		// 	format: true
		// })
		return {
			// date: currentDate,
			date: lastYear + '-' + formattedLastMonth,
			info: {},
			yearList: Array.from({ length: 20 }, (_, i) => currentYear - i), // 默认显示当前年份前后20年
			monthList: Array.from({ length: 12 }, (_, i) => {
				const month = i + 1;
				return month > 9 ? String(month) : '0' + month;
			}), // 默认显示1-12月
			pickerValue: [], // 默认选中上个月
			year: lastYear,
			month: lastMonth,
		};
	},
	onShow() {
		this.getData();
	},
	methods: {
		onChange(e) {
			this.year = this.yearList[e.detail.value[0]];
			this.month = this.monthList[e.detail.value[1]];
		},
		// bindDateChange: function (e) {
		// 	// console.log(e.detail.value.split('-'));
		// 	let date_ = e.detail.value.split('-');
		// 	this.date = date_[0] + '-' + date_[1];
		// 	this.getData();
		// },
		open() {
			this.$refs.popup.open();
			this.$nextTick(() => {
				let yearIndex = this.yearList.indexOf(this.year);
				let monthIndex = this.monthList.indexOf(this.month);
				console.log(yearIndex, monthIndex);
				return this.pickerValue = [yearIndex, monthIndex];
			})
		},
		close() {
			this.$refs.popup.close()
		},
		changeDate() {
			console.log(this.year, this.month);
			let month = parseInt(this.month) > 9 ? String(this.month) : '0' + parseInt(this.month);
			this.date = this.year + '-' + month;
			this.getData();
			this.close();
		},
		getDate(type) {
			// const date = new Date();
			// let year = date.getFullYear();
			// let month = date.setMonth(date.getMonth() - 1);
			// // let day = date.getDate();
			// let lastMonth = new Date(month);
			// let lastMonthYear = lastMonth.getFullYear();
			// let lastMonthMonth = lastMonth.getMonth() + 1; // getMonth() 返回的是0-11的值，需要加
			// console.log(lastMonth, lastMonthYear, lastMonthMonth);
			// // return
			// if (type === 'start') {
			// 	year = year - 60;
			// } else if (type === 'end') {
			// 	year = year + 2;
			// }
			// lastMonthMonth = lastMonthMonth > 9 ? lastMonthMonth : '0' + lastMonthMonth;
			// // day = day > 9 ? day : '0' + day;
			// return `${lastMonthYear}-${lastMonthMonth}`;
			// console.log(this.yearList.indexOf(this.year));
			this.$nextTick(() => {
				let yearIndex = this.yearList.indexOf(this.year);
				let monthIndex = this.monthList.indexOf(this.month);
				console.log(yearIndex, monthIndex);
				return [yearIndex, monthIndex];
			})
			// return [1, 11]
		},
		getData() {
			// /mp/user/salaryDetails
			req.showLoading();
			req.getRequest('/shopApi/mp/user/salaryDetails', {
				dateTime: this.date,
				staffId: req.getStorage('userInfo').introductionId,
			}, res => {
				console.log(res, ' 薪资明细');
				uni.hideLoading();
				this.info = res.data;
			})
		}
	},
}
</script>

<style lang="scss">
.salaryDetails {
	padding: 20rpx 0;
	background-color: #f8f8f8;

	.picker_ {
		border: 1px solid #E41206;
		width: 220rpx;
		height: 60rpx;
		border-radius: 16rpx;
		line-height: 60rpx;
		text-align: center;
		color: #666666;
		font-weight: 600;
		display: flex;
	}

	.picker-view {
		width: 750rpx;
		height: 500rpx;
	}

	.item {
		line-height: 100rpx;
		text-align: center;
	}

	.salary_month {
		position: relative;
		background-color: #fff;
		border-radius: 12rpx;
		padding: 60rpx 24rpx 35rpx;
		box-sizing: border-box;

		.month {
			position: absolute;
			top: 0;
			left: 0;
			padding: 5rpx 10rpx;
			background-color: #E41206;
			font-family: PingFang SC;
			font-size: 28rpx;
			font-weight: normal;
			line-height: 40rpx;
			text-align: center;
			color: #FBFBFC;
			border-radius: 12rpx 0 12rpx 0;
		}

		.info-row {
			font-family: PingFang SC;
			font-size: 28rpx;
			font-weight: normal;
			line-height: 50rpx;

			// text-align: center;
			.label {
				color: #333333;
				width: 185rpx;
				display: inline-block;
				text-align: justify;
				text-align-last: justify;
				margin-right: 14rpx;
				// display: block;
			}

			.value {
				color: #4E5969;
			}
		}
	}
}
</style>
