<template>
	<view class="index_view" :style="{ 'padding-top': statusBarHeight + 'px' }">
		<customTopNav :title="'集和堂快药'" :color="'#fff'" :bgColor="show_bgcolor ? '#e50101' : 'transparent'">
		</customTopNav>
		<!-- <image style=" width:100%;"
		src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241121/78a2a0eddbf84af987f08200c51b7045.png"
		mode="widthFix">
		</image> -->
		<!-- 门店 -->
		<!-- style="margin-top: 300rpx;" -->
		<view class="store">
			<view @click="goUrl('/other/storeList/storeList')" class="store_name">
				<text class="iconfont">&#xe682;</text>
				<view style="margin:0 7rpx;">{{ store.storeName }}</view>
				<view style="border:1px solid #fff;border-radius: 10rpx;padding:0 5rpx">切换门店</view>
			</view>
			<view style="width:320rpx;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">{{ address }}
			</view>
		</view>
		<!-- 搜索 -->
		<view style="padding:0 20rpx;margin-top:20rpx;">
			<view @click="goUrl('/product/search/search')" class="search">
				<view style="display: flex;align-items: center;">
					<uni-icons color="#BFBFBF;" type="search" size="20"></uni-icons>
					<view style="margin-left:10rpx;">搜索疾病、症状、商品</view>
				</view>
				<uni-icons color="#BFBFBF;" type="scan" size="20"></uni-icons>
			</view>
		</view>
		<!-- 热门搜索 -->
		<view class="histories">
			<view @click="goUrl(`/product/list/list?searchTitle=${item.productName}`)"
				v-for="(item, index) in hotKeywords" :key="index" class="histories_item">
				<text v-if="index < 3" class="iconfont hot-icon">&#xe757;</text>
				<text>{{ item.productName }}</text>
			</view>
		</view>
		<!-- 分类 -->
		<view
			style="margin:20rpx 20rpx 0;background: #fff;position: relative;z-index: 1;border-radius: 20rpx;padding-bottom:20rpx;">
			<image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240122/ca1c0abe421e410f96ee6736d386c6f4.jpg"
				mode="widthFix" style="width:100%;margin:10rpx 0;"></image>
			<view class="categorize">
				<view @click="goUrl(`/product/list/list?categoryId=${item.id}&title=${item.name}`)"
					class="categorize_item" v-for="(item, index) in categoryList" :key="index">
					<image :src="item.icon" mode="widthFix" style="width: 90rpx;"></image>
					<view style="font-size:26rpx;">{{ item.name }}</view>
				</view>
			</view>
		</view>
		<!-- 轮播图 -->
		<uni-swiper-dot :info="bannerList" :current="current" field="content">
			<swiper autoplay :circular="true" class="swiper-box" @change="bannerChange">
				<swiper-item style="display: flex;justify-content: center;" v-for="(item, index) in bannerList"
					:key="index">
					<img :src="item.imageUrl" class="swiper-img" alt="">
				</swiper-item>
			</swiper>
		</uni-swiper-dot>
		<!-- 限时抢购 -->
		<view class="time_limited" v-if="seckillList.length > 0">
			<view style="display: flex;justify-content: space-between;">
				<view style="display: flex;align-items: center;">
					<view class="title">
						<view class="line"></view>
						<view style="font-size:32rpx;">限时秒杀</view>
					</view>
					<view style="display: flex;align-items: center;">
						<uni-countdown :day="countdownTime.days" :hour="countdownTime.hours"
							:minute="countdownTime.minutes" :second="countdownTime.seconds" :font-size="12"
							color="#FFFFFF" background-color="#EA1306"></uni-countdown>
					</view>
				</view>
				<view @click="goUrl('/product/seckill/seckill')"
					style="display: flex;align-items: center;font-size:28rpx;">
					<view>更多</view>
					<text class="iconfont">&#xe667;</text>
				</view>
			</view>
			<scroll-view scroll-x class="scroll-view">
				<view class="time_limited_list scroll-content">
					<view @click="goToDetail({ id: item.productId, type: 2 })" v-for="(item, index) in seckillList"
						:key="index" class="goods_item">
						<image mode="widthFix" style="width:120rpx;" :src="item.img">
						</image>
						<view class="goods_title"> {{ item.productName }}</view>
						<view class="goods_price">
							<view style="margin-right:15rpx;font-size:32rpx;">¥<text style="font-weight: 900;">{{
								item.money }}</text> </view>
							<view style="font-size:18rpx;">60天最低价</view>
						</view>
						<view class="goods_btn">
							<view style="margin-right:10rpx;">立省<text style="font-weight: 900;">{{ (item.orPrice -
								item.money).toFixed(2) }}</text> 元</view>
							<view
								style="display: flex;align-items: center;justify-content: center; background: #EA1306;color:#fff;padding:0 14rpx;border-radius: 30rpx;height:100%;"
								@click.stop="openBuyPopup(item)">
								<text class="iconfont" style="font-size: 20rpx;">&#xe8c5;</text>
								<view style="font-size:24rpx;font-weight: 900;">立抢</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 限时折扣 -->
		<view class="time_limited" v-if="discountList && discountList.length > 0">
			<view style="display: flex;justify-content: space-between;">
				<view style="display: flex;align-items: center;">
					<view class="title">
						<view class="line"></view>
						<view style="font-size:32rpx;">限时折扣</view>
					</view>
				</view>
				<view @click="goUrl('/product/timeDiscount/timeDiscount')"
					style="display: flex;align-items: center;font-size:28rpx;">
					<view>更多</view>
					<text class="iconfont">&#xe667;</text>
				</view>
			</view>
			<scroll-view scroll-x class="scroll-view">
				<view class="time_limited_list scroll-content">
					<view v-for="item in discountList" :key="item.id" class="goods_item"
						@click="goToDetail({ id: item.id, type: item.actType })">
						<image mode="widthFix" style="width:120rpx;" :src="item.img">
						</image>
						<view style="font-size:18rpx;" class="goods_events">{{ item.discount * 10 }}折</view>
						<view class="goods_title">{{ item.goodsName }}</view>
						<view class="goods_price">
							<view style="margin-right:15rpx;font-size:32rpx;">¥<text style="font-weight: 900;">{{
								item.discountPrice }}</text></view>
							<text class="original">/¥{{ item.salePrice }}</text>
						</view>
						<view class="goods_btn">
							<view style="margin-right:10rpx;">立省<text style="font-weight: 900;">{{
								item.favourablePrice
							}}</text>元</view>
							<view
								style="display: flex;align-items: center;justify-content: center; background: #EA1306;color:#fff;padding:0 14rpx;border-radius: 30rpx;height:100%;"
								@click.stop="openBuyPopup(item)">
								<text class="iconfont" style="font-size:20rpx;">&#xe8c5;</text>
								<view style="font-size:24rpx;font-weight: 900;">立抢</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 组合优惠 -->
		<view class="time_limited" v-if="assembly && assembly.length > 0">
			<view style="display: flex;justify-content: space-between;">
				<view style="display: flex;align-items: center;">
					<view class="title">
						<view class="line"></view>
						<view style="font-size:32rpx;">组合优惠</view>
					</view>
				</view>
				<view @click="goUrl('/product/assembly/assembly')"
					style="display: flex;align-items: center;font-size:28rpx;">
					<view>更多</view>
					<text class="iconfont">&#xe667;</text>
				</view>
			</view>
			<scroll-view scroll-x class="scroll-view">
				<view class="time_limited_list scroll-content">
					<view v-for="item in assembly" :key="item.id" class="goods_item"
						@click="goToAssemblyDetail({ id: item.actId, type: item.actType })">
						<image mode="widthFix" style="width:180rpx;" :src="item.child[0].img">
						</image>
						<view class="assembly_button">组合</view>
						<view class="goods_title">{{ item.name }}</view>
						<view class="goods_title goods_product"><span v-for="(product, productIndex) in item.child"
								:key="product.id">{{ product.goodsName }} <span
									v-if="productIndex < (item.child.length - 1)">+</span></span></view>
						<view class="goods_price">
							<view style="font-size:28rpx;">¥{{ item.comPrice }}</view>
							<text class="original">/¥{{ item.salePrice }}</text>
						</view>
						<view class="goods_btn">
							<view style="margin-right:10rpx;">立省{{ item.favourablePrice }}元</view>
							<view
								style="display: flex;align-items: center;justify-content: center; background: #EA1306;color:#fff;padding:0 14rpx;border-radius: 30rpx;height:100%;"
								@click.stop="openBuyPopup(item)">
								<text class="iconfont" style="font-size:22rpx;">&#xe8c5;</text>
								<view style="font-size:24rpx;">立抢</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 新人专区 -->
		<view v-if="newUserList.length > 0 && isNew" class="time_limited">
			<view style="display: flex;justify-content: space-between;">
				<view class="title">
					<view class="line"></view>
					<view style="font-size:32rpx;">新人特价</view>
				</view>
				<view @click="goUrl('/product/newUser/newUser')"
					style="display: flex;align-items: center;font-size:28rpx;">
					<view>更多</view>
					<text class="iconfont">&#xe667;</text>
				</view>
			</view>
			<scroll-view scroll-x class="scroll-view">
				<view class="scroll-content">
					<view v-for="(item, index) in newUserList" :key="index" class="goods_item"
						@click="goToDetail({ id: item.productId, type: 4 })">
						<image mode="widthFix" style="width:180rpx;" :src="item.img">
						</image>
						<view class="goods_title">{{ item.productName }}</view>
						<view class="goods_price"
							style="width:100%;justify-content: space-between;align-items: flex-end;">
							<view>
								<view style="margin-right:15rpx;font-size:24rpx;">¥{{ item.money }}</view>
								<view v-if="item.orPrice > item.money"
									style="font-size:24rpx;color:#999;text-decoration: line-through;">
									¥{{ item.orPrice }}
								</view>
							</view>
							<image
								src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241113/a9c6fe546b5c4b339a8c99ec8740d4eb.png"
								mode='widthFix' style="width:50rpx;margin-right: 19rpx;"
								@click.stop="openBuyPopup(item)">
							</image>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<!-- 分类 -->
		<view class="classify">
			<view v-if="healthList.length > 0" class="classify_item">
				<view @click="goUrl(`/product/hotSale/hotSale?type=health&title=节气健康`)" class="title">
					<view class="line"></view>
					<view>节气健康</view>
					<view
						style="background: linear-gradient(90deg, #1FE5AD 0%, #03DB9D 100%);margin-left:10rpx;padding:4rpx 10rpx;border-radius: 10rpx;color:#fff;text-align: center;font-size:18rpx;">
						春分</view>
				</view>
				<view style="display: flex;justify-content: space-between;">
					<view @click="goToDetail({ id: item.id })" v-for="(item, index) in healthList" :key="index"
						style="display: flex;flex-direction: column;align-items: center;width: 45%;">
						<image mode="widthFix" style="width:160rpx;" :src="item.pic"></image>
						<view style="color:#EA1306;font-size:16px;font-weight:900;">¥{{ item.level1 }}</view>
					</view>
				</view>
			</view>
			<view v-if="hotSaleList.length > 0" class="classify_item">
				<view @click="goUrl(`/product/hotSale/hotSale?type=hot&title=热卖榜`)" class="title">
					<view class="line"></view>
					<view>热卖榜</view>
					<view
						style="background: linear-gradient(90deg, #FF9853 0%, #FD4E1F 100%);margin-left:10rpx;padding:4rpx 10rpx;border-radius: 10rpx;color:#fff;text-align: center;font-size:18rpx;">
						每日更新</view>
				</view>
				<view style="display: flex;justify-content: space-between;">
					<view @click="goToDetail({ id: item.id })" v-for="(item, index) in hotSaleList" :key="index"
						style="display: flex;flex-direction: column;align-items: center;width: 45%;">
						<image mode="widthFix" style="width:160rpx;" :src="item.pic">
						</image>
						<view style="color:#EA1306;font-size:16px;font-weight:900;">¥{{ item.level1 }}</view>
					</view>
				</view>
			</view>
			<view class="classify_item">
				<view class="title">
					<view class="line"></view>
					<view>健康宝典</view>
					<view
						style="background: linear-gradient(90deg, #00C9FF 0%, #0099FF 100%);margin-left:10rpx;padding:4rpx 10rpx;border-radius: 10rpx;color:#fff;text-align: center;font-size:18rpx;">
						守护健康</view>
				</view>
				<view style="display: flex;justify-content: space-around;margin-top:10rpx;">
					<view @click="goToMiniProgram" style="display: flex;flex-direction: column;align-items: center;">
						<image
							src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241112/de31c002ab21497a9acfceb21095807c.png"
							mode="widthFix" style="width:100rpx;margin-bottom:10rpx;"></image>
						<view style="color:#4E5969;">问医生</view>
					</view>
					<view @click="msg" style="display: flex;flex-direction: column;align-items: center;">
						<image
							src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241112/a62b129f53024dec9604b1e883f94968.png"
							mode="widthFix" style="width:100rpx;margin-bottom:10rpx;"></image>
						<view style="color:#4E5969;">查症状</view>
					</view>
				</view>
			</view>
			<view class="classify_item">
				<view class="title">
					<view class="line"></view>
					<view>药品分类</view>
					<view
						style="background: linear-gradient(90deg, #7B81F6 0%, #676EFC 100%);margin-left:10rpx;padding:4rpx 10rpx;border-radius: 10rpx;color:#fff;text-align: center;font-size:18rpx;">
						安全有效</view>
				</view>
				<view style="display: flex;justify-content: space-around;margin-top:10rpx;">
					<view style="display: flex;flex-direction: column;align-items: center;" @click="goToClassify(1)">
						<image
							src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241112/196a568e682c43569e86f455211ccbd8.png"
							mode="widthFix" style="width:100rpx;margin-bottom:10rpx;"></image>
						<view style="color:#4E5969;">处方药</view>
					</view>
					<view style="display: flex;flex-direction: column;align-items: center;" @click="goToClassify(0)">
						<image
							src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241112/05c25a42d6f54bec8149f5b5f175168a.png"
							mode="widthFix" style="width:100rpx;margin-bottom:10rpx;"></image>
						<view style="color:#4E5969;">非处方药</view>
					</view>
				</view>
			</view>
		</view>
		<view style="display: flex;justify-content: space-between;margin-left:20rpx;">
			<view style="display: flex;align-items: center;">
				<view class="title">
					<view class="line"></view>
					<view style="font-size:32rpx;">猜你喜欢</view>
				</view>
			</view>
		</view>
		<view class="recommend">
			<!-- 使用瀑布流组件展示商品列表 -->
			<customWaterfallsFlow imageKey="img" ref="waterfallsFlowRef" :value="goodsList" @imageClick="goToDetail">
				<view v-for="(item, index) in goodsList" :key="index" slot="slot{{index}}">
					<goods-card :data="item"></goods-card>
				</view>
			</customWaterfallsFlow>
		</view>
		<buy-popup ref="buyPopup" :is-cart-action="isCartAction" :product="{
			image: data.img || '',
			price: data.money || 0,
			originalPrice: data.salePrice || 0,
			name: data.goodsName || '',
			id: data.productId || '',
			maxBuy: data.maxBuy || 0,
			isMedicalInsurance: data.isMedicalInsurance,
			isOverallPlanning: data.isOverallPlanning
		}" @confirm="handleBuyConfirm" :cloud-stock="Number(data.warehouseStock) || 0"
			:store-stock="Number(data.sellStock) || 0" @click.stop />
		<!-- 登录弹窗 -->
		<view class="login-modal" v-if="showLoginModal">
			<view class="login-container">
				<view class="login-header">
					<text class="login-title">登录</text>
					<text class="login-close" @click="closeLoginModal">×</text>
				</view>
				<view class="login-content">
					<image class="login-avatar" mode="aspectFit"
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240927/6f40d834eed1445b8a4fe3404a800611.png">
					</image>
					<view class="login-tip">登录后可用更多功能</view>
					<view class="login-btn">
						<button v-if="phone" @click="login" hover-class="none" class="btnphone">快捷登录</button>
						<button v-else @click="login" hover-class="none" class="btnphone" open-type="getPhoneNumber"
							@getphonenumber="getphonenumber">快捷登录</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import customWaterfallsFlow from "@/components/custom-waterfalls-flow/custom-waterfalls-flow" //瀑布流
import customTopNav from "@/components/custom-top-nav/custom-top-nav" // 自定义头部
import goodsCard from "@/components/goods-card/goods-card" //商品卡片
import buyPopup from "@/components/buy-popup/buy-popup" //商品卡片
const req = require('../../utils/request.js')
const app = getApp()
export default {
	data() {
		return {
			current: 0,
			data: {},
			bannerList: [], // 轮播图列表
			goodsList: [], // 商品列表
			seckillList: [], // 秒杀列表 
			newUserList: [], // 新用户专享列表
			categoryList: [], // 分类列表
			hotKeywords: [], // 热门搜索关键词
			healthList: [], // 健康专区列表
			hotSaleList: [], // 热销商品列表
			discountList: [], // 限时折扣
			assembly: [], // 组合优惠
			isNew: false,
			pageNum: 1,
			pageSize: 10,
			store: null,
			selectedProduct: {},
			buyType: '',
			address: "",
			isCartAction: true,
			countdownTime: {
				days: 0,
				hours: 0,
				minutes: 0,
				seconds: 0
			},
			timer: null,
			show_bgcolor: false,
			statusBarHeight: '',
			openid: "",
			sessionKey: "",
			phone: "",
			scene: "",
			showLoginModal: false,
			encryptedData: "",
			iv: ""
		}
	},
	components: {
		customWaterfallsFlow,
		goodsCard,
		buyPopup,
		customTopNav
	},
	onPageScroll(e) {
		// console.log(e);
		if (e.scrollTop > 10) { //当页面滚动到某个位置时（这里设置为50），将导航栏背景颜色显示
			this.show_bgcolor = true;
		} else {
			this.show_bgcolor = false;
		}
	},
	onLoad(options) {
		if (options.scene) {
			this.scene = options.scene;
		};
		// 获取sessionKey等信息
		app.globalData.getSessionKey(json => {
			this.openid = json.openid
			this.sessionKey = json.session_key
			this.phone = json.phone
			// 获取完sessionKey后检查登录状态并进行相应处理
			this.getCartCode();
		});
		// console.log(options, 'hhh');
		req.getLocationInfo(res => {
			this.address = res.address
			if (!req.getStorage("currentStore").mDistance) {
				req.getRequest('/shopApi/home/<USER>', {
					pageNum: 1,
					pageSize: 5,
					lat: res.location.lat,
					lng: res.location.lng,
				}, res => {
					this.store = res.data.list[0]
					req.setStorage("currentStore", this.store)
				})
			}
		});
		this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 50;

	},
	onShow() {
		if (!req.getStorage("currentStore")) {
			this.getDefaultStore()
		};
		this.store = req.getStorage("currentStore")
		if (req.getStorage("userInfo")) {
			this.isNewUser()
			getApp().globalData.updateCartBadge()
		}
		this.getSeckillList()
		this.newGoodsList()
		this.getCategoryList()
		this.timeDiscount()
		this.getApiCompose()
		this.getHotKeywords()
		this.getBanner()
		this.getHotSaleList()
	},
	onReachBottom() {
		this.pageNum++
		this.getGoodsList()
	},
	onShareAppMessage() {
		return {
			title: '集和堂药房',
			path: '/pages/index/index',
			imageUrl: 'https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241213/db17b30e316c42ceaca6b068f6742ba0.jpg'
		}
	},
	methods: {
		getCartCode() {
			if (!req.getStorage("userInfo") && this.scene) {
				this.showLoginModal = true
				return
			};
			if (this.scene) {
				// 用户已登录，进行正常初始化
				req.showLoading();
				req.getRequest("/shopApi/goldCard/programCode", {
					codeId: this.scene
				}, res => {
					res = JSON.parse(res.data);
					console.log(res, "hhhhhhhhhhh");
					if (res.tabValue == 1) {
						req.postRequest('/shopApi/goldCard/updateUser', {
							pid: res.pid, // 推荐人 id
							id: req.getStorage("userInfo").id
						}, res => {
							this.scene = "";
							uni.hideLoading();
							req.msg(res.msg);
						})
					} else {
						this.payOrder(res);
					}
				});
			}
		},
		closeLoginModal() {
			this.showLoginModal = false
		},
		login() {
			uni.showLoading({
				title: '登录中...'
			});
			uni.getUserProfile({
				desc: "注册",
				success: res => {
					this.encryptedData = res.encryptedData
					this.iv = res.iv
					if (this.phone) {
						req.postRequest("/shopApi/weixin/login", {
							openid: this.openid,
							encryptedData: this.encryptedData,
							iv: this.iv,
							sessionKey: this.sessionKey
						}, res => {
							uni.hideLoading();
							req.setStorage("AUTH_TOKEN", res.data.accessToken);
							req.setStorage("userInfo", res.data);
							this.showLoginModal = false;
							this.getCartCode();
							req.msg('登录成功');
						})
					}
				},
			})
		},
		getphonenumber(e) {
			if (e.target.errMsg == "getPhoneNumber:ok") {
				req.postRequest("/shopApi/weixin/login", {
					openid: this.openid,
					encryptedData: this.encryptedData,
					iv: this.iv,
					sessionKey: this.sessionKey
				}, res => {
					req.setStorage("AUTH_TOKEN", res.data.accessToken)
					setTimeout(() => {
						req.getRequest("/shopApi/weixin/getPhone/" + e.detail.code, {}, res1 => {
							uni.hideLoading();
							req.setStorage("userInfo", res1.data);
							req.setStorage("AUTH_TOKEN", res1.data.accessToken);
							this.showLoginModal = false;
							this.getCartCode();
						})
					}, 500);
				})
			} else {
				uni.hideLoading();
				req.msg("取消登录");
			}
		},
		payOrder(data) {
			req.postRequest("/shopApi/goldCard/crateOrder", {
				memId: req.getStorage("userInfo").id, // 会员id
				payState: 0, // 0 微信 1 余额 
			}, res1 => {
				// console.log(res1);
				req.postRequest('/shopApi/goldCard/wxPay', {
					id: res1.data.id, // 订单 id
					money: res1.data.money // 金额
				}, json => {
					json = JSON.parse(json.data);
					console.log(json, '支付');
					if (json.miniPayRequest) {
						console.log('走银联');
						wx.requestPayment({
							package: json.miniPayRequest.package,
							appId: json.miniPayRequest.appId,
							paySign: json.miniPayRequest.paySign,
							nonceStr: json.miniPayRequest.nonceStr,
							timeStamp: json.miniPayRequest.timeStamp,
							signType: json.miniPayRequest.signType,
							success: () => {
								req.postRequest('/shopApi/goldCard/updateOrder', {
									id: res1.data.id,
									payNum: json.merOrderId,
									memId: req.getStorage("userInfo").id,
									pid: data.pid
								}, res => {
									this.scene = "";
									uni.hideLoading();
									req.msg('开通成功');
								})
							},
							fail: () => {
								this.scene = "";
								uni.hideLoading();
							}
						})
					} else {
						console.log('走余额');
						uni.requestPayment({
							timeStamp: json.timeStamp,
							nonceStr: json.nonceStr,
							package: json.packages,
							signType: json.signType,
							paySign: json.sign,
							success: () => {
								console.log('支付成功')
								req.postRequest('/shopApi/goldCard/updateOrder', {
									id: res1.data.id,
									payNum: json.merOrderId,
									memId: req.getStorage("userInfo").id,
									pid: data.pid
								}, res => {
									this.scene = "";
									uni.hideLoading();
									req.msg('开通成功');
								})
							},
							fail: () => {
								this.scene = "";
								uni.hideLoading();
							}
						})
					}
				})
			});
		},
		getBanner() {
			req.getRequest("/shopApi/home/<USER>", {}, res => {
				this.bannerList = res.data
				console.log('bannerList', res)
			})
		},
		getData() {
			this.getGoodsList()
			this.newGoodsList()
		},
		//获取默认门店
		getDefaultStore() {
			return new Promise((resolve, reject) => {
				req.getRequest("/shopApi/home/<USER>", {}, res => {
					this.store = res.data
					req.setStorage("currentStore", res.data)
					resolve()
				})
			})
		},
		//跳转其他小程序
		goToMiniProgram() {
			uni.navigateToMiniProgram({
				appId: 'wx017bcf0b1797fef4',
				path: 'pages/index/index',
				envVersion: req.env.NODE_ENV == 'product' ? 'release' : 'trial',
				success(res) {
					console.log('跳转成功');
				},
				fail(err) {
					console.log('跳转失败', err);
				}
			})
		},

		//分类
		getCategoryList() {
			req.getRequest("/shopApi/home/<USER>", {
				pid: 0
			}, res => {
				console.log(res);
				this.categoryList = res.data.filter(item => item.name != '统筹专区')
			})
		},
		//商品
		getGoodsList() {
			req.getRequest("/shopApi/home/<USER>", {
				storeId: this.store.id,
				pageNum: this.pageNum,
				pageSize: 10
			}, res => {
				this.goodsList = [...this.goodsList, ...res.data.list]
			})
		},

		msg() {
			req.msg("功能暂未开放,尽情期待")
		},
		//热卖
		getHotSaleList() {
			req.getRequest("/shopApi/product/hotSaleAndHealth", {
				merchantId: this.store.id ? this.store.id : 60
			}, res => {
				this.hotSaleList = res.data.hotSaleList?.slice(0, 2) || res.data.hotSaleList
				this.healthList = res.data.HealthList?.slice(0, 2) || res.data.HealthList
			})
		},
		openBuyPopup(item) {
			this.data = item
			this.data.img ? this.data.img : this.data.child[0].img
			this.data.goodsName ? this.data.goodsName : this.data.name
			this.$refs.buyPopup.open()
		},
		handleBuyConfirm({ quantity, deliveryType, isCartAction }) {
			req.postRequest("/shopApi/purchase/cart", {
				skuId: null,
				merchantId: deliveryType == 1 ? req.getStorage("currentStore").id : 60,
				quantity,
				productId: this.data.productId,
				mode: deliveryType,
				state: 0,
				actId: this.data.actId ? this.data.actId : "",
				activityType: this.data.actType ? this.data.actType : ""
			}, res => {
				req.msg('加入购物车成功')
				getApp().globalData.updateCartBadge()
			})
		},
		goToDetail(item) {
			if (!item || !item.id) {
				req.msg('商品信息不完整')
				return
			}
			let url = `/product/detail/detail?id=${item.id}`
			if (item.type) {
				url += `&type=${item.type}`
			}
			uni.navigateTo({
				url: url
			})
		},
		goToAssemblyDetail(item) {
			if (!item || !item.id) {
				req.msg('商品信息不完整')
				return
			}
			let url = `/product/assemblyDetail/assemblyDetail?id=${item.id}`
			if (item.type) {
				url += `&type=${item.type}`
			}
			uni.navigateTo({
				url: url
			})
		},
		goUrl(url) {
			uni.navigateTo({
				url: url
			})
		},
		bannerChange(e) {
			this.current = e.detail.current;
		},
		//是否新用户
		isNewUser() {
			req.getRequest("/shopApi/shopApi/activity/isnew", {
				userId: req.getStorage("userInfo").id
			}, res => {
				if (res.data == 'true') this.isNew = true
			})
		},
		//限时秒杀专区
		getSeckillList() {
			req.getRequest("/shopApi/shopApi/activity/list", {
				pageNum: 1,
				pageSize: 99,
				merchantId: req.getStorage("currentStore").id ? req.getStorage("currentStore").id : 60,
				type: 2,
			}, res => {
				this.seckillList = res.data.items
				if (this.seckillList.length > 0 && this.seckillList[0].endTime) {
					this.calculateCountdown(this.seckillList[0].endTime)
				}
			})
		},
		//新人专区
		newGoodsList() {
			req.getRequest("/shopApi/shopApi/activity/list", {
				pageNum: 1,
				pageSize: 99,
				merchantId: req.getStorage("currentStore").id ? req.getStorage("currentStore").id : 60,
				type: 4,
			}, res => {
				this.newUserList = res.data.items
			})
		},

		// 限时折扣商品
		timeDiscount() {
			req.getRequest("/shopApi/discount/getDiscountList", {
				isMore: 1,
				storeId: req.getStorage("currentStore").id ? req.getStorage("currentStore").id : 60,
			}, res => {
				this.discountList = res.data
			})
		},
		// 组合优惠产品
		getApiCompose() {
			req.getRequest("/shopApi/compose/getApiComposeList", {
				storeId: req.getStorage("currentStore").id ? req.getStorage("currentStore").id : 60,
			}, res => {
				this.assembly = res.data
			})
		},
		//热门标签
		getHotKeywords() {
			req.getRequest("/shopApi/shopback/search/list", {
				pageNum: 1,
				pageSize: 4,
				storeId: req.getStorage("currentStore").id ? req.getStorage("currentStore").id : 60,
			}, res => {
				console.log(res);
				this.hotKeywords = res.data.items
			})
		},
		// 计算倒计时
		calculateCountdown(endTime) {
			// 清除之前的定时器
			if (this.timer) {
				clearInterval(this.timer)
			}

			// 使用 req.startCountdown 替代原有的倒计时逻辑
			this.timer = req.startCountdown(endTime, (countdown) => {
				this.countdownTime = {
					days: countdown.days,
					hours: countdown.hours,
					minutes: countdown.minutes,
					seconds: countdown.seconds
				}
			})
		},

		// 在组件销毁时清除定时器
		beforeDestroy() {
			console.log('销毁');
			if (this.timer) {
				clearInterval(this.timer)
			}
		},
		goToClassify(type) {
			uni.switchTab({
				url: '/pages/classify/classify'
			});
			// 使用全局变量或本地存储来保存状态
			uni.setStorageSync('classifyTabValue', type);
		}
	},
}
</script>

<style lang="scss" scoped src="./index.scss"></style>