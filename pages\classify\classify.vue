<template>
  <view>
    <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
    <view>
      <!-- 禁止滚动穿透 -->
      <view class="classify">
        <view class="header">
          <view style="position: relative;">
            <image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241113/aa02af4309b4409f8eb8925b36d99ef5.png"
              class="search_icon" style="left:48rpx;" />
            <!-- <input type="text" placeholder="搜索疾病、症状、商品" class="search" v-model="search_val"
              @click="goUrl('/product/search/search')"> -->
            <view class="search" @click="goUrl('/product/search/search')">搜索疾病、症状、商品</view>
            <view style="position: absolute;width: 70rpx;height: 70rpx;top: 2rpx;right: 50rpx;" @click.stop="scanCode">
              <image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241112/faaec3e1db604501bd4e36272ffce43e.png"
                class="search_icon" style="right: 20rpx;" />
            </view>
          </view>
          <view class="tab_">
            <view class="tab_item" :class="tabValue === 0 ? 'tab_active' : ''" @click="changTabVable(0)">非处方药</view>
            <view class="tab_item" :class="tabValue === 1 ? 'tab_active' : ''" @click="changTabVable(1)">处方药</view>
          </view>
        </view>
        <view class="body">
          <scroll-view class="left_tab" scroll-y enhanced :show-scrollbar="false">
            <view class="left_item" v-for="(item, index) in classifyList" :key="index"
              :class="leftTab === index ? 'left_item_active' : ''" @click="classifyConfirm(index, item.id)">
              {{ item.name }}
            </view>
            <view class="card_" v-if="false">
              <!-- <image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241113/bf569644719c4ea39213c7a612604c92.png"
            style="width:40rpx;height:36rpx;" /> -->
              <i class="iconfont" style="color: #4E5969;">&#xe60d;</i>
              <view class="cornermark">1</view>
            </view>
          </scroll-view>
          <view class="right_tab">
            <!-- 统筹专区 -->
            <view style="padding-bottom: 20rpx;margin-bottom: 14rpx;background-color: #fff;" v-if="leftTab === 0">
              <view class="tc_view">
                <view class="tc_item" @click="searchGood(-1, '全部')">
                  <image
                    src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/87fc01116fa145f297b445640b8d7504.png"
                    style="width: 96rpx;height: 96rpx;border-radius: 50%;" />
                  <view :class="topTab === -1 ? 'tc_item_active' : ''">全部</view>
                </view>
                <view class="tc_item" v-for="(item, index) in list" :key="index"
                  @click="searchGood(index, item.name, item.id)">
                  <image :src="item.icon" style="width: 96rpx;height: 96rpx;border-radius: 50%;" mode="aspectFit" />
                  <view :class="topTab === index ? 'tc_item_active' : ''">{{ item.name }}</view>
                </view>
              </view>
              <view style="text-align: center;font-size: 24rpx;font-weight:700;color: #4e5969;margin-top: 12rpx;"
                @click="showMore" v-if="showMore_btn">{{ show_more ? '收起'
                  : '显示更多' }}</view>
            </view>
            <!-- 药品 -->
            <view class="right_bottom">
              <uni-swiper-dot :current="current" field="content" v-if="leftTab !== 0" :autoplay="true" :interval="3000"
                :duration="1000">
                <swiper autoplay :circular="true" class="swiper-box" @change="bannerChange">
                  <swiper-item style="display: flex;justify-content: center;" v-for="(item, index) in bannerList"
                    :key="index">
                    <view style="height:100%;width:100%;border-radius: 20rpx;">
                      <image :src="item" mode="aspectFill" style="height:100%;width:100%;"></image>
                    </view>
                  </swiper-item>
                </swiper>
              </uni-swiper-dot>
              <view style="padding: 40rpx 26rpx 0 12rpx;">
                <view style="margin-bottom: 20rpx;" v-if="leftTab !== 0">
                  <view class="screen_tag">
                    <view style="margin-right:60rpx;display: flex;align-items: center;" @click="selectSort(1)">
                      <view style="margin-right: 10rpx;" :style="sort == 4 || sort == 3 ? 'color:#EA1306;' : ''">价格
                      </view>
                      <view>
                        <i class="iconfont" style="font-size: 10rpx;color: #D9D9D9;margin-bottom: 5rpx;"
                          :style="sort == 3 ? 'color:#EA1306;' : ''">&#xe605;</i>
                        <i class="iconfont" style="font-size: 10rpx;color: #D9D9D9;"
                          :style="sort == 4 ? 'color:#EA1306;' : ''">&#xe604;</i>
                      </view>
                    </view>
                    <view @click="selectSort(2)" :style="sort === 2 ? 'color:#EA1306;' : ''">销量</view>
                  </view>
                  <view class="screen_radio">
                    <view style="display: flex;">
                      <view class="text_radio " :class="topTab === -1 ? 'text_radio_active' : ''"
                        @click="searchGood(-1, '全部')">全部
                      </view>
                      <view class="text_radio" v-for="(item, index) in list_copy" :key="index"
                        @click="searchGood(index, item.name, item.id)"
                        :class="topTab === index ? 'text_radio_active' : ''">{{
                          item.name }}
                      </view>
                    </view>
                    <uni-icons type="down" size="14" color="#999999" @click="showRadio"></uni-icons>
                  </view>
                  <view class="classify_tag ">{{ selectName }}</view>
                </view>
                <scroll-view scroll-y enhanced :show-scrollbar="false" class="goods_view"
                  :style="{ height: `calc(100vh - ${scrollHeight}rpx)` }" lower-threshold="30"
                  @scrolltolower="scrolltolower">
                  <view class="restoratives">
                    <view class="res_item" v-for="(item, index) in goodsList" :key="index" @click="goToDetail(item)">
                      <view class="item_img">
                        <image :src="item.img" style="width: 156rpx;height: 156rpx;border-radius: 8rpx;"
                          mode="aspectFit" :style="{ filter: item.isPrescription == 1 ? 'blur(5px)' : '' }" />
                        <image style="position: absolute;top:0;left:0;width: 156rpx;height: 156rpx;"
                          v-if="item.isPrescription == 1"
                          src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240111/a338c782689a421b8e9ab0848c3c5c10.png">
                        </image>
                        <view class="tag" v-if="item.isMedicalInsurance === 1">医保</view>
                        <view class="tag" style="background-color: #08BF8B;" v-if="item.isOverallPlanning === 1">统筹
                        </view>
                      </view>
                      <view>
                        <view style="display: flex;margin: 10rpx 0;">
                          <view
                            style="background-color: #0B78FF;width: 50rpx;height: 30rpx;text-align: center;border-radius: 6rpx; margin-right: 12rpx;margin-top: 4rpx;line-height: 32rpx;"
                            v-if="item.isOtc === 1">
                            <i class="iconfont" style="color: #fff;font-size: 28rpx;">&#xe606;</i>
                          </view>
                          <view
                            style="background-color: #08BF8B;width: 50rpx;height: 30rpx;line-height: 24rpx;text-align: center;border-radius: 6rpx; margin-right: 12rpx;margin-top: 4rpx;"
                            v-else-if="item.isPrescription === 1">
                            <text style="color: #fff;font-size: 20rpx;">RX</text>
                            <!-- <i class="iconfont" style="color: #fff;font-size: 28rpx;">&#xe606;</i> -->
                          </view>
                          <view class="title">{{ item.goodsName }}</view>
                        </view>
                        <view style="display: flex;align-items: center;" v-if="false">
                          <text class="red_view"
                            style=" background: #ea1306;border-radius: 8rpx 0rpx 0rpx 8rpx;color: #fff;">促</text>
                          <text class="red_view"
                            style="width: 108rpx;border-radius: 0rpx 8rpx 8rpx 0rpx;color: #EA1306;border: 1rpx solid #EA1306;box-sizing: border-box;line-height: 22rpx;">积分换</text>
                        </view>
                        <view>
                          <view
                            style="display: flex;align-items: center;justify-content: space-between;margin: 12rpx 0;width: 340rpx;">
                            <view class="text_pay">
                              <text style="color: #E91306;font-weight: 600;">¥{{ item.level1Price }} </text>
                              <text style="text-decoration: line-through;" v-if="item.salePrice > item.level1Price">
                                /¥{{ item.salePrice }}</text>
                            </view>
                            <view
                              style="border-radius: 50%;width: 44rpx;height: 44rpx;text-align: center;line-height: 44rpx;"
                              @click.stop="openBuyPopup(item, true)">
                              <i class="iconfont"
                                style="color: #fff; font-size: 19px;background: #E91306; border-radius: 44rpx;">&#xe60c;</i>
                            </view>
                          </view>
                        </view>
                        <!-- 添加金卡价格展示 -->
                        <view v-if="!levels && item.level2Price < item.level1Price"
                          style="display: flex;align-items: center;position: relative;padding: 8rpx;background: linear-gradient(90deg, #FDF6E1 0%, rgba(250, 235, 192, 0.2) 97%);border-radius: 15rpx;">
                          <i class="iconfont" style="color: #EA8F06;">&#xe6b0;</i>
                          <view
                            style="padding:3rpx 10rpx;background: linear-gradient(90deg, #FF5F00 0%, #EA3202 99%);color:#fff;border-radius: 20rpx;font-size:20rpx;margin:0 8rpx;">
                            金卡价</view>
                          <view style="color:#EA1306;font-weight: 900;font-size:32rpx;">¥{{ item.level2Price }}</view>
                        </view>
                        <view v-else-if="levels && item.level2Price < item.level1Price"
                          style="display: flex;align-items: center;position: relative;padding: 8rpx;background: linear-gradient(90deg, #FDF6E1 0%, rgba(250, 235, 192, 0.2) 97%);border-radius: 15rpx;">
                          <image src="@/static/jinka.png" mode="heightFix"
                            style="height:80rpx;position: absolute;left:0;top:-50%;transform: translateY(25%);"></image>
                          <view style="color:#EA1306;font-weight: 900;font-size:32rpx;margin-left:90rpx;">¥{{
                            item.level2Price }}</view>
                        </view>
                        <view class="volume" v-if="false">
                          <image
                            src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241116/a99be970bf7543138a7b201102d13449.png"
                            style="width: 28rpx;height: 28rpx;">
                          </image>
                          <view class="text_">
                            <view>儿童专区热销榜<text style="color:#F09C20;">.TOP1</text></view>
                            <uni-icons type="right" size="10" color="#F09C20"></uni-icons>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view v-if="goodsList.length === 0" style="text-align: center;color: #999;">
                      <image mode=""
                        src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png"
                        style="width: 372rpx;height: 312rpx;margin-top: 50rpx;">
                      </image>
                      <view style="margin-top: 10rpx;font-size: 28rpx;">暂无药品</view>
                    </view>
                  </view>
                </scroll-view>
              </view>
            </view>
          </view>
        </view>
        <uni-popup ref="popup" :type="center" :animation="false" border-radius="10px 10px 0 0" @change="change">
          <view
            style="margin-bottom: 20rpx;background-color: #fff;width: 540rpx;padding: 20px 13px 20rpx 6px;border-radius: 16rpx 16rpx 0 16rpx;position: fixed;right: 0;top: 450rpx;">
            <view class="screen_tag">
              <view style="margin-right:60rpx;display: flex;align-items: center;" @click="selectSort(1)">
                <view style="margin-right: 10rpx;" :style="sort == 4 || sort == 3 ? 'color:#EA1306;' : ''">价格
                </view>
                <view>
                  <i class="iconfont" style="font-size: 10rpx;color: #D9D9D9;margin-bottom: 5rpx;"
                    :style="sort == 3 ? 'color:#EA1306;' : ''">&#xe605;</i>
                  <i class="iconfont" style="font-size: 10rpx;color: #D9D9D9;"
                    :style="sort == 4 ? 'color:#EA1306;' : ''">&#xe604;</i>
                </view>
              </view>
              <view @click="selectSort(2)" :style="sort === 2 ? 'color:#EA1306;' : ''">销量</view>
            </view>
            <view class="screen_radio">
              <view style="display: flex;flex-wrap: wrap;">
                <view class="text_radio " :class="topTab === -1 ? 'text_radio_active' : ''"
                  @click="searchGood(-1, '全部')">全部
                </view>
                <view class="text_radio" v-for="(item, index) in listAll" :key="index"
                  @click="searchGood(index, item.name, item.id)" :class="topTab === index ? 'text_radio_active' : ''">{{
                    item.name }}
                </view>
              </view>
              <uni-icons :type="radio_pop ? 'up' : 'down'" size="14" color="#999999" @click="showRadio"></uni-icons>
            </view>
          </view>
        </uni-popup>
        <!-- 加入购物车 -->
        <buy-popup ref="buyPopup" :is-cart-action="true" :product="{
          image: data.img || '',
          price: levels == 2 || levels == 1 ? data.level2Price : data.level1Price || 0,
          originalPrice: data.salePrice || 0,
          name: data.goodsName || '',
          id: data.id || '',
          type: data.type || '',
          isMedicalInsurance: data.isMedicalInsurance,
          isOverallPlanning: data.isOverallPlanning
        }" @confirm="handleBuyConfirm" :cloud-stock="Number(data.warehouseStock) || 0"
          :store-stock="Number(data.saleStock) || 0" @click.stop />
      </view>
    </view>
  </view>

</template>

<script>
import buyPopup from "@/components/buy-popup/buy-popup"
const req = require("../../utils/request.js");
export default {
  data() {
    return {
      levels: req.getStorage("userInfo").levels,
      search_val: "",
      tabValue: 0,
      current: 0,
      bannerList: ["https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241210/86b69553d66745fdaffc3f2cfadc4e84.jpg", "https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/9d7f88c526c64e80bc10019fbbf2e7c8.jpg", "https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/7fa7653053ab413188ef3199ce4bb3b1.jpg"],
      radio_pop: false,
      show: false,
      classifyList: [],
      topTab: -1,
      leftTab: 0,
      listAll: [],
      list: [],
      list_copy: [],
      showMore_btn: false,  // 显示更多的按钮
      show_more: false,  // 显示更多
      pageNum: 1,
      pageSize: 10,
      total: 0,
      categoryId: "",
      categoryItemId: "",
      goodsList: [], // 商品列表
      scrollHeight: 0,
      data: {},
      selectName: '',
      sort: 1,  // 1：默认、2：销量、3：价格正序、4：价格降序
    }
  },
  onShow() {
    if (req.getStorage("userInfo")) {
      getApp().globalData.updateCartBadge()
    }
    // 获取全局变量或本地存储中的状态
    let tabValue = uni.getStorageSync('classifyTabValue');
    if (tabValue != null) {
      this.tabValue = Number(tabValue);
      console.log(this.tabValue, 'this.tabValue');
      // 清除状态,避免影响下次进入
      uni.removeStorageSync('classifyTabValue');
    }

    if (this.categoryId) {
      this.searchGood(this.topTab, this.selectName, this.categoryId);
    } else {
      this.getList();
    }
  },
  components: {
    buyPopup
  },
  methods: {
    // 
    scanCode() {
      // 允许从相机和相册扫码
      uni.scanCode({
        success: res => {
          console.log(res, "扫码扫码扫码");
          // uni.navigateTo({
          //   url: '/' + res.path
          // });
          // this.scene = '1290926887903780865';
          // this.loadCodeParams();
        }
      });
    },
    selectSort(type) {
      if (type == 1) {
        switch (this.sort) {
          case 1:
            this.sort = 3;
            break;
          case 2:
            this.sort = 3;
            break;
          case 3:
            this.sort = 4;
            break;
          default:
            this.sort = 1;
        }
      } else if (type == 2) {
        if (this.sort !== 2) {
          this.sort = 2
        } else {
          this.sort = 1
        }
      };
      this.searchGood(this.topTab, this.selectName, this.categoryId);
    },
    scrolltolower() {
      if (this.pageNum * this.pageSize >= this.total) return req.msg("没有更多数据了")
      this.pageNum++;
      // console.log("触底", this.pageNum);
      // this.getHealthRecord();
      this.searchGood(this.topTab, this.selectName, this.categoryId);
    },
    bannerChange(e) {
      this.current = e.detail.current;
    },
    showRadio() {
      this.radio_pop = !this.radio_pop;
      if (this.radio_pop) {
        this.$refs.popup.open('center');
      } else {
        this.$refs.popup.close();
      };
    },
    // openBuyPopup(item) {
    //   console.log(item);
    //   this.data = item;
    //   this.$refs.buyPopup.open();
    // },
    openBuyPopup(item, isCartAction) {
      this.isCartAction = isCartAction;
      console.log('item-------', item)
      this.data = item;
      this.data.img ? this.data.img : this.data.child[0].img;
      this.data.goodsName ? this.data.goodsName : this.data.name;
      this.$refs.buyPopup.open();
    },
    // 添加购物车
    handleBuyConfirm({ quantity, deliveryType, isCartAction }) {
      console.log(this.data);
      req.postRequest("/shopApi/purchase/cart", {
        skuId: null,
        merchantId: req.getStorage("currentStore").id,
        quantity,
        productId: this.data.id,
        mode: deliveryType,
        state: 0,
        actId: this.data.actId ? this.data.actId : ""
      }, res => {
        req.msg('加入购物车成功')
        getApp().globalData.updateCartBadge()
      })
    },
    change(e) {
      // console.log(e);
      this.radio_pop = e.show;
      this.show = e.show;
    },
    // 处方药跟非处方药 - 切换
    changTabVable(value) {
      this.tabValue = value;
      this.pageNum = 1;
      this.searchGood(this.topTab, this.selectName, this.categoryId);
    },
    // 显示更多
    showMore() {
      this.show_more = !this.show_more;
      if (this.show_more) {
        this.list = [...this.listAll];
        let num = Math.ceil((this.list.length + 1) / 4) * 150;
        this.scrollHeight = 306 + num;
        // console.log(Math.ceil((this.list.length + 1) / 4) * 150);
      } else {
        this.list = this.listAll.slice(0, 3);
        if (this.showMore_btn) {
          this.scrollHeight = 456;
        } else {
          this.scrollHeight = 416;
        };
      }
    },
    // 左边商品分类切换获取
    classifyConfirm(index, id) {
      // console.log(index, id);
      this.topTab = -1;
      this.leftTab = index;
      this.categoryId = id;
      this.pageNum = 1;
      this.sort = 1;
      this.getList(id);
    },
    // 查询商品分类列表
    getList(id) {
      this.goodsList = [];
      req.showLoading();
      req.getRequest("/shopApi/home/<USER>", {
        pid: id
      }, res => {
        console.log(res, '查询商品分类列表');
        if (!id) {
          this.classifyList = [...res.data];
          this.categoryId = this.classifyList[0].id;
          this.getList(this.categoryId);
        } else {
          this.listAll = [...res.data];
          if (this.listAll.length > 3) {
            this.showMore_btn = true;
            this.scrollHeight = 456;
          } else {
            this.showMore_btn = false;
            this.scrollHeight = 416;
          };
          if (this.show_more) {
            this.list = [...this.listAll];
            let num = Math.ceil((this.list.length + 1) / 4) * 150;
            this.scrollHeight = 306 + num;
          } else {
            this.list = this.listAll.slice(0, 3);
            if (this.showMore_btn) {
              this.scrollHeight = 456;
            } else {
              this.scrollHeight = 416;
            };
          }
          if (this.leftTab != 0) {
            this.list_copy = this.listAll.slice(0, 2);
            this.scrollHeight = 664;
          }
          this.searchGood(-1, '全部', this.categoryId);
        };
      })
    },
    // 搜索商品
    searchGood(index, name, id) {
      this.topTab = index;
      this.selectName = name;
      this.categoryId = id;
      if (!this.categoryId) {
        this.categoryId = this.classifyList[this.leftTab].id
      };
      req.getRequest("/shopApi/home/<USER>", {
        sort: this.sort,
        storeId: req.getStorage("currentStore").id,
        categoryId: this.categoryId,
        title: this.search_val,
        isPrescription: this.tabValue,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }, res => {
        console.log(res, "搜索商品");
        if (this.pageNum == 1) {
          this.goodsList = [...res.data.list];
        } else {
          this.goodsList = [...this.goodsList, ...res.data.list];
        };
        this.total = res.data.total;
        uni.hideLoading();
        this.$refs.popup.close();
      })
    },
    goToDetail(item) {
      console.log(item, "item.............");
      if (!item || !item.id) {
        req.msg('商品信息不完整')
        return
      }
      let url = `/product/detail/detail?id=${item.id}`
      if (item.type) {
        url += `&type=${item.type}`
      }
      // console.log(url);
      this.goUrl(url);
    },
    goUrl(url) {
      uni.navigateTo({
        url: url
      })
    },
  },
  onLoad(options) {
    if (options.tabValue) {
      this.tabValue = Number(options.tabValue);
      this.searchGood(this.topTab, this.selectName, this.categoryId);
    }
  }
}
</script>

<style lang="scss" scoped src="./classify.scss"></style>