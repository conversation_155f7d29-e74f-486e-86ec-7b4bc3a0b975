<template>
    <view class="seckill-page">
        <image style="width:100%;"
            src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241121/78a2a0eddbf84af987f08200c51b7045.png"
            mode="widthFix"></image>
        <!-- 分类 -->
        <view class="sort">
            <scroll-view scroll-x class="scroll-view">
                <view class="scroll-content">
                    <view v-for="item in categoryList" :key="item.id" class="sort_item"
                        :class="(activeSort == item.id ? 'active_sort_item' : '')" @click.stop="toggleSort(item)">
                        <img class="sort_img"
                            src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241122/a1d83efc6c344631a4b4c9c8a86e5a7d.png"
                            alt="">
                        {{ item.name }}
                    </view>
                </view>
            </scroll-view>
        </view>
        <!-- 商品列表 -->
        <view class="product-list">
            <view class="product-item" v-for="(item, index) in productList" :key="index" @click="goToDetail(item)">
                <view class="product-card">
                    <view class="image-wrapper">
                        <view class="yibao-tongchou" v-if="item.isOverallPlanning === 1">医保</view>
                        <view class="yibao-tongchou" v-if="item.isMedicalInsurance === 1">统筹</view>
                        <image class="product-image" v-if="item.child.length>0" :src="item.child[0].img" mode="aspectFill"></image>
                        <view class="date-wrapper"> 
                            <view style="font-size:28rpx;font-weight: 600;">秒杀套餐</view>
                            <image mode="widthFix" style="width:130rpx;"
                                src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241119/5cd2785fdfed412d9d0af37502fe7bbe.png">
                            </image>
                        </view>
                    </view>
                    <view class="product-info">
                        <text class="title">{{ item.name }}{{ item.remark }}</text>
                        <view class="stock-info">
                            <view class="stock-item">
                                <text class="stock" v-if="item.child.length>0">门店库存: {{ item.child[0].sellStock }}件</text>
                                <text class="stock" v-if="item.child.length>0">云仓库存: {{ item.child[0].warehouseStock }}件</text>
                            </view>
                            <text class="limit" v-if="item.limitNum">限购{{ item.limitNum }}件</text>
                        </view>
                        <view class="price-row" @click.stop="openBuyPopup(item)">
                            <view class="price">
                                <text class="symbol">抢购价¥</text>
                                <text class="number">{{ item.comPrice }}</text>
                                <text class="original">¥{{ item.salePrice }}</text>
                            </view>
                            <view class="price_button">
                                立即抢购
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 无数据提示 -->
        <view class="empty-tip" v-if="productList.length === 0">
            <text>暂无折扣商品</text>
        </view>

        <!-- 添加购买弹窗组件 -->
        <buy-popup ref="buyPopup" v-if="selectedProduct.comPrice" :product="{
            image: selectedProduct.img ? selectedProduct.img : selectedProduct.child[0].img,
            price: selectedProduct.comPrice || 0,
            maxBuy: selectedProduct.limitNum
        }" :cloud-stock="selectedProduct.discountPrice" :store-stock="selectedProduct.saleStock" :is-cart-action="true"
            @confirm="confirmBuy" />
    </view>
</template>

<script>
import buyPopup from '@/components/buy-popup/buy-popup.vue'
const req = require('../../utils/request.js')
export default {
    components: {
        buyPopup
    },
    data() {
        return {
            currentDate: '',
            currentSession: 0,
            productList: [],
            selectedProduct: {}, // 添加选中的商品数据
            currentCategory: 0,
            categoryList: [],
            activeSort: '',
        }
    },
    onLoad() {
        this.getCategoryList()
    },
    onPullDownRefresh() {
        this.getProductList()
    },
    onUnload() {

    },
    methods: {
        // 获取分类
        getCategoryList() {
            req.getRequest("/shopApi/home/<USER>", {
                pid: 0
            }, res => {
                if (res.data.length > 0) {
                    this.categoryList = res.data.filter(item => item.name != '统筹专区')
                    this.activeSort = this.categoryList[0].id
                    this.getProductList()
                }
            })
        },
        // 切换分类
        toggleSort(val) {
            this.activeSort = val.id
            this.getProductList()
        },
        // 获取商品列表
        getProductList() {
            // TODO: 调用接口获取商品列表
            req.getRequest("/shopApi/compose/getApiComposeListPage", {
                storeId: 2,
                categoryId: this.activeSort,
                pageNum: this.pageNum,
                pageSize: this.pageSize,
            }, res => {
                this.productList = res.data.items
            })
            setTimeout(() => {
                uni.stopPullDownRefresh()
            }, 500)
        },

        // 跳转商品详情
        goToDetail(item) {
            if (item.saleStock < 1 || item.cloudStock < 1) {
                uni.showToast({
                    title: '商品已抢完',
                    icon: 'none'
                })
                return
            }
            uni.navigateTo({
                url: `/product/assemblyDetail/assemblyDetail?id=${item.actId}&type=6`
            })
        },

        // 打开购买弹窗
        openBuyPopup(item) {
            this.selectedProduct = item
            this.$nextTick(() => {
                this.$refs.buyPopup.open()
            })
        },

        // 确认购买
        confirmBuy(data) {
            // TODO 立即购买
            console.log('购买数据：', data)
        },


    }
}
</script>

<style lang="scss" scoped>
@import './assembly.scss';
</style>