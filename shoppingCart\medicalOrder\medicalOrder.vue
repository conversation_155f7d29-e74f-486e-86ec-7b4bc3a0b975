<template>
	<view v-if="show">
		<view class="titleBg"></view>
		<view class="merchants_box">
			<view>
				<view style="color:#a6c1ff;font-size:28rpx;">付款给</view>
				<view style="color:#fff;font-size:36rpx;">广州集和堂大药房连锁有限公司伟诚分店</view>
			</view>
			<image src="@/static/hospitals.png" mode="widthFix" style="width:80rpx;"></image>
		</view>
		<view class="info_box">
			<view class="totalCost">
				<view>费用总额</view>
				<view>{{ detail.feeSumamt }}元</view>
			</view>
			<view class="detailedCosts">
				<view class="detailedCosts_item">
					<view>医保基金支付</view>
					<view>{{ detail.fundPay }}元</view>
				</view>
				<view class="detailedCosts_item">
					<view>个人账户支付</view>
					<view>{{ detail.selfAcctPay }}元</view>
				</view>
				<view style="margin-bottom:33rpx;" class="detailedCosts_item">
					<view>其他抵扣金额</view>
					<view>{{ detail.othFeeAmt }}元</view>
				</view>
				<view class="cashCosts">
					<view>现金支付</view>
					<view>{{ detail.ownPayAmt }}元</view>
				</view>
			</view>

			<view style="margin:54rpx 40rpx 54rpx;border-top:1px dashed #eff0f4;position:relative;">
				<view
					style="background: #fff;position: absolute;line-height:2px;transform: translate(-50%,-50%);left: 50%;font-size: 26rpx;color:#909399">
					以下费用不支持医保支付</view>
			</view>
			<view class="distributionCosts">
				<view>配送费/打包费等</view>
				<view>{{ detail.delvFee }}元</view>
			</view>
			<view @click="checkDetails" class="viewDetails">
				查看明细
			</view>
		</view>
		<view class="persons" v-if="detail.orderState == 0">
			<view style="color:#666;font-size:28rpx;">个人账户支付</view>
			<view class="persons_btn">
				<view @click="clickPersons(1)" :class="personsIndex == 1 ? 'persons_active' : ''"
					style="margin-right:30rpx">使用</view>
				<view @click="clickPersons(0)" :class="personsIndex == 0 ? 'persons_active' : ''">不使用</view>
			</view>
		</view>
		<view v-if="personsIndex == 2" class="pay_mode">
			<view style="margin-bottom:30rpx;color:#666;font-size:28rpx;">支付方式</view>
			<view class="pay_mode_item">
				<view style="display: flex;align-items: center;">
					<image
						src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240805/e0ae66f5c28d40768337566f4068a026.png"
						mode="widthFix" style="width:50rpx;margin:0 24rpx 0 30rpx"></image>
					<view>微信支付</view>
				</view>
				<image
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240805/d251e595595242469d873d3fddeefa92.png"
					mode="widthFix" style="width:40rpx;margin-right:30rpx;"></image>
			</view>
		</view>
		<view style="display: flex;align-items: center;justify-content: center;padding-bottom:198rpx;">
			<image src="@/static/medical.png" mode="widthFix" style="width:80rpx;margin-right:12rpx;">
			</image>
			<view style="color:#666;font-size:28rpx;">医保移动支付</view>
		</view>
		<view class="pay_box">
			<view class="pay_money">
				<view style='color:#666;font-size:28rpx;margin-bottom:12rpx;'>{{ detail.orderState == 0 ? '您还需支付：' :
					'您已支付：' }}<text style="color:#3b71e8;font-size:44rpx;">¥{{ detail.orderState == 0 ?
						(Number(detail.ownPayAmt) + Number(detail.delvFee)).toFixed(2) : detail.feeSumamt }}</text>
				</view>
				<view style="color:#303133;font-size:24rpx;">含配送费打包费¥{{ detail.delvFee }}</view>
			</view>
			<view v-if="detail.orderState == 0" @click="pay" class="pay_btn">去支付</view>
			<view v-if="detail.orderState == 2" @click="backPay" class="pay_btn">申请退款</view>
			<view v-if="detail.orderState == 4" class="pay_btn" style="background-color: #909399;">退款中</view>
			<view v-if="detail.orderState == 5" class="pay_btn" style="background-color: #909399;">已退款</view>
		</view>
		<uni-popup ref="details" type="bottom">
			<view class="details">
				<view class="details_title">
					<view style="color:#303133;font-size:40rpx;margin-left:40rpx;">处方明细</view>
					<image @click="closeDetails" src="@/static/medicalClear.png" mode="widthFix"
						style="width:80rpx;margin-right:28rpx"></image>
				</view>
				<view class="details_info" style="margin-top:150rpx;">
					<view class="details_info_title" style="border-radius: 12rpx 12rpx 0 0;">
						<view class="line"></view>
						<view class="text">就诊信息</view>
					</view>
					<view class="details_content">
						<view class="details_content_item">
							<view>门诊类型</view>
							<view>门(急)诊</view>
						</view>
						<view class="details_content_item">
							<view>门诊科室</view>
							<view>普通内科</view>
						</view>
						<view class="details_content_item">
							<view>医生姓名</view>
							<view>邹国新</view>
						</view>
						<view class="details_content_item">
							<view>处方时间</view>
							<view>{{ detail.createTime }}</view>
						</view>
						<view class="details_content_item">
							<view>费用总额</view>
							<view style="color:#3b71e8">{{ detail.feeSumamt }}元</view>
						</view>
					</view>
				</view>
				<view class="details_info">
					<view class="details_info_title" style="border-radius: 12rpx 12rpx 0 0;">
						<view class="line"></view>
						<view class="text">诊断信息</view>
					</view>
					<view v-for="(item, index) in detail.icdCode" :key="index" class="details_content">
						<view class="details_content_item">
							<view>诊断名称</view>
							<view>{{ item.name }}</view>
						</view>
						<view class="details_content_item">
							<view>诊断编号</view>
							<view>{{ item.code }}</view>
						</view>
					</view>
				</view>
				<view class="details_info">
					<view class="details_info_title" style="border-radius: 12rpx 12rpx 0 0;">
						<view class="line"></view>
						<view class="text">费用信息</view>
					</view>
					<view v-for="(item, index) in detail.list" :key="index" class="details_content">
						<view class="details_content_item">
							<view>{{ item.productName }}</view>
							<view>{{ item.payMoney }}元</view>
						</view>
					</view>
				</view>
				<view class="details_info">
					<view class="details_info_title" style="border-radius: 12rpx 12rpx 0 0;">
						<view class="line"></view>
						<view class="text">其他抵扣金额</view>
					</view>
					<view class="details_content">
						<view class="details_content_item">
							<view>住院押金抵扣</view>
							<view>{{ detail.deposit }}元</view>
						</view>
						<view class="details_content_item">
							<view>医院负担金额抵扣</view>
							<view>{{ detail.hospPartAmt }}元</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<uni-popup ref="loading" type="center" :is-mask-click="false">
			<view class="loading">
				<image
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240805/33ebfee6d1774e2abfbb6937757fbc83.gif"
					mode="widthFix" style="width:82rpx;margin-top:68rpx;"></image>
				<view
					style="color:#3b71e8;font-size:28rpx;width:258rpx;margin-top:30rpx;display: flex;justify-content: center;">
					加载中</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import req from "../../utils/request"
const app = getApp()
export default {
	data() {
		return {
			personsIndex: 1,
			orderId: "",
			show: false,
			detail: {},
			getCode: false,
			authCode: null,
		}
	},
	onLoad(options) {
		this.orderId = options.orderId
		this.getData()
	},
	onShow() {
		if (app.globalData.authCode && this.getCode) {
			this.getCode = false
			this.authCode = app.globalData.authCode
			req.showLoading()
			req.postRequest("/shopApi/medical/order/refund", {
				orderId: this.orderId,
				qrcode: this.authCode
			}, res => {
				console.log("退款", res);
				this.getData()
			})
		}
		// 检查是否需要获取订单信息
		const needCheckOrder = uni.getStorageSync('needCheckOrder')
		if (needCheckOrder && needCheckOrder == this.orderId) {
			// 清除标记，确保只调用一次
			uni.removeStorageSync('needCheckOrder')
			req.showLoading()
			setTimeout(() => {
				// 调用获取订单信息接口
				req.getRequest('/shopApi/medical/order/getOrderInfoById', {
					orderId: this.orderId
				}, res => {
					uni.hideLoading()
					console.log('订单信息:', res)
					if (res.data.hisStatus == 2) {
						uni.reLaunch({
							url: '/mine/order/order'
						})
					}
				})
			}, 3000);

		}
	},
	methods: {
		getData() {
			req.showLoading()
			req.getRequest("/shopApi/medical/order/getMedicalFeeInfo", {
				orderId: this.orderId
			}, res => {
				uni.hideLoading();
				if (!this.show) this.show = true
				res.data.deposit = Number(res.data.deposit).toFixed(2)
				this.personsIndex = res.data.acctUsedFlag
				this.detail = res.data
				console.log(res.data);
			})
		},
		pay() {
			this.$refs.loading.open()
			req.postRequest("/shopApi/medical/order/unifiedPlaceOrder", {
				orderId: this.orderId,
				returnUrl: '/pages/index/index'
			}, res => {
				console.log(res);
				this.$refs.loading.close()
				if (res.code == 500) {
					req.msg(res.msg)
					return
				}
				uni.navigateToMiniProgram({
					appId: res.data.pay_appid,
					path: res.data.pay_url,
					success: res => {
						uni.setStorageSync('needCheckOrder', this.orderId)
					}
				})
			}, true)
			// return
		},
		closeDetails() {
			this.$refs.details.close()
		},
		checkDetails() {
			this.$refs.details.open()
		},
		clickPersons(index) {
			if (this.personsIndex == index) return
			this.personsIndex = index
			req.showLoading()
			req.postRequest('/shopApi/medical/order/whetherUseAcctUsedFlag', {
				orderId: this.orderId,
				acctUsedFlag: this.personsIndex
			}, res => {
				this.getData()
			})
		},
		backPay() {
			uni.navigateToMiniProgram({
				appId: 'wxe183cd55df4b4369',
				path: `auth/pages/bindcard/auth/index?openType=getAuthCode&bizType=04107&cityCode=440108&channel=30000175&orgChnlCrtfCodg=BqK1kMStlhVDgN2uHf4EsLK/F2LjZPYJ81nK2eYQqxuHgpiS+wkhUBWGPoaYCwz0&orgCodg=P44010600664&orgAppId=1I5K5N9JE1T84460C80A00000ED5F971`,
				envVersion: req.env.NODE_ENV == 'product' ? 'release' : 'trial',
				success: res => {
					this.getCode = true
				}
			})
		},
	}
}
</script>

<style lang="scss" src="./medicalOrder.scss"></style>
