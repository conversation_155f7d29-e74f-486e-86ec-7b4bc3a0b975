<template>
    <view>
        <view class="content">
            <view class="title">
                <view class="df" style="margin-bottom:10px;">
                    <img class="shu"
                        src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240829/75327e7fe6e7454b84903e036ae475a1.png"
                        alt="">
                    <text class="Copay">基本信息</text>
                </view>
                <view class="infoDet df">
                    <view class="leftInfo"><text style="color:red;">*</text> 姓名:</view>
                    <view class="rightInfo">
                        <input name="realName" placeholder="请输入姓名" v-model="userInfo.name" />
                    </view>
                </view>
                <view class="infoDet df">
                    <view class="leftInfo">证件类型:</view>
                    <view class="rightInfo">居民身份证</view>
                </view>
                <view class="infoDet df">
                    <view class="leftInfo"><text style="color:red;">*</text>证件号码:</view>
                    <view class="rightInfo">
                        <input name="realName" v-model="userInfo.idCard" maxlength="18" placeholder="请输入身份证号码"
                            @input="checkId" />
                    </view>
                </view>
                <view class="infoDet df">
                    <view class="leftInfo"><text style="color:red;">*</text>性别:</view>
                    <view class="rightInfo">
                        <input name="realName" placeholder="请输入性别" disabled v-model="userInfo.gender" />
                    </view>
                </view>
                <view class="infoDet df">
                    <view class="leftInfo"><text style="color:red;">*</text>出生日期:</view>
                    <view class="rightInfo">
                        <input name="realName" placeholder="请输入出生日期" disabled v-model="userInfo.birthday" />
                    </view>
                </view>
                <view class="infoDet df">
                    <view class="leftInfo"><text style="color:red;">*</text>联系方式:</view>
                    <view class="rightInfo">
                        <input name="realName" maxlength="11" placeholder="请输入手机号码" v-model="userInfo.tel" />
                    </view>
                </view>
                <view class="infoDet df" style="align-items: center;">
                    <view class="leftInfo"><text style="color:red;">*</text>设为默认:</view>
                    <checkbox-group @change="change">
                        <checkbox :checked="userInfo.isDefault == 1 ? true : false" :value="userInfo.isDefault"
                            color="#0b78ff" style="transform:scale(0.8)" />
                    </checkbox-group>
                </view>
                <!-- <view class="infoDet df">
					<view class="leftInfo"><text style="color:red;">*</text>参保地址(仅广东):</view>
					<view style="position: relative;display: flex;align-items: center;">
						<input class="flex" placeholder="请输入参保地址" @input="select"
							v-model="userInfo.placeCodeName"></input>
						<view v-if="regionList" class="region">
							<view @click="selectRegion(item)" v-for="item in regionList" :key="item">
								{{ item }}
							</view>
						</view>
					</view>
				</view> -->
            </view>
        </view>

        <view class="content">
            <view class="title">
                <view class="df" style="margin-bottom:10px;">
                    <img class="shu"
                        src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240829/75327e7fe6e7454b84903e036ae475a1.png"
                        alt="">
                    <text class="Copay">基本信息</text>
                </view>

                <view class="infoDet df">
                    <view class="leftInfo">工作单位:</view>
                    <view class="rightInfo">
                        <input name="realName" placeholder="请输入工作单位" v-model="userInfo.workUnit" />
                    </view>
                </view>
                <view class="infoDet df">
                    <view class="leftInfo">职业:</view>
                    <view class="rightInfo">
                        <input name="realName" placeholder="请输入职业" v-model="userInfo.occupation" />
                    </view>
                </view>
                <view class="infoDet df">
                    <view class="leftInfo">常住地址:</view>
                    <view class="rightInfo">
                        <input name="realName" placeholder="请输入常住地址" v-model="userInfo.address" />
                    </view>
                </view>
                <view class="infoDet df">
                    <view class="leftInfo">婚姻状况:</view>
                    <view style="flex:1;" class="rightInfo">
                        <uni-data-select v-model="userInfo.maritalStatus" :localdata="maritalStatusList"
                            placeholder="请选择婚姻状况" clear></uni-data-select>
                    </view>
                </view>
            </view>
        </view>
        <view v-if="id" class="modify_box">
            <view @click="deletePatient" class="modify_item" style="margin-right:50rpx;">删除</view>
            <view @click=upSubmit class="modify_item" style=" background: #EA1306;border:none;color:#fff;">修改患者信息</view>
        </view>
        <view v-else class="submit" @click="upSubmit">提交就诊人</view>
    </view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
    data() {
        return {
            maritalStatusList: [
                { value: 1, text: "已婚" },
                { value: 0, text: "未婚" },
            ],
            id: "",
            userInfo: {
                address: '',
                birthday: "",
                gender: "男",
                name: "",
                idCard: "",
                sex: null,
                tel: "",
                workUnit: '',
                occupation: '',
                maritalStatus: null,
                placeCode: "",
                placeCodeName: '',
                uid: 3,
                isDefault: 1
            },
            regionList: [],
            timer: null,
        }
    },
    onLoad(options) {
        if (options.id) {
            this.id = options.id
            req.showLoading()
            req.getRequest("/shopApi/userDrugPeople/get", {
                id: options.id
            }, res => {
                uni.hideLoading();
                this.userInfo = res.data
                // this.userInfo.placeCodeName = res.placeCodeName
                this.checkId(res.data.idCard);
            })
        }
    },
    methods: {
        change(e) {
            !this.userInfo.isDefault ? this.userInfo.isDefault = 1 : this.userInfo.isDefault = 0
            console.log(this.userInfo.isDefault);
        },
        selectRegion(item) {
            const match = item.match(/^\d+/);
            console.log(match[0]);
            this.userInfo.placeCodeName = item
            this.userInfo.placeCode = match[0]
            this.regionList = []
        },
        select(e) {
            this.userInfo.placeCode = ""
            clearTimeout(this.timer)
            if (!e.detail.value) {
                this.regionList = []
                return
            }
            this.timer = setTimeout(() => {
                req.getRequest("/api/userDrugPeople/Insuredplace", {
                    name: e.detail.value
                }, res => {
                    console.log(res);
                    if (res.code == 0) {
                        this.regionList = res.data
                    } else {
                        req.msg("搜索不到参保地址")
                    }
                }, false, true)
            }, 1000);
        },
        upSubmit() {
            if (!this.userInfo.name) return req.msg("请输入姓名")
            if (this.userInfo.sex === null) return req.msg("请输入正确的身份证")
            if (!this.userInfo.tel) return req.msg("请输入手机号码")
            // if (!this.userInfo.placeCode) return req.msg("请选择参保地址")
            this.userInfo.uid = req.getStorage("userInfo").id
            req.showLoading()
            console.log(JSON.stringify(this.userInfo));
            req.postRequest("/shopApi/userDrugPeople/save", this.userInfo, res => {
                console.log(res, '确定');
                uni.hideLoading()
                if (this.userInfo.id) {
                    req.msg("修改成功", () => { }, 2000)
                } else {
                    req.msg("添加成功", () => { }, 2000)
                }
                setTimeout(() => {
                    uni.navigateBack();
                }, 2000);
            })
        },
        deletePatient() {
            uni.showModal({
                content: '确定删除就诊人吗？',
                success: (res) => {
                    if (res.confirm) {
                        req.showLoading()
                        req.postRequest("/shopApi/userDrugPeople/delete/" + this.id, {}, res => {
                            uni.hideLoading()
                            req.msg("删除成功", () => { }, 2000)
                            setTimeout(() => {
                                uni.navigateBack();
                            }, 2000);
                        })
                    }
                }
            });

        },
        checkId(e) {
            if ((e.detail && e.detail.value.length === 18) || e.length == 18) {
                let { id, birthday, gender, sex } = this.getDetailsFromID((e.detail && e.detail.value.length === 18) ? e.detail.value : e)
                this.userInfo.idCard = id
                this.userInfo.birthday = birthday
                this.userInfo.gender = gender
                this.userInfo.sex = sex
            } else {
                // this.userInfo.idCard = ''
                this.userInfo.birthday = ''
                this.userInfo.gender = ''
                this.userInfo.sex = null
            }
        },
        getDetailsFromID(id) {
            // 检查身份证号码是否有效（包括15位和18位身份证）
            const validIDPattern = /^(^[1-9]\d{7}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}$)|(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}(\d|X|x)$)$/;
            if (!validIDPattern.test(id)) {
                req.msg("无效的身份证号码")
                // this.userInfo.idCard = ''
                this.userInfo.birthday = ''
                this.userInfo.gender = ''
            }
            // 18位身份证号码
            else if (id.length === 18) {
                // 获取出生年月日
                const birthYear = id.substring(6, 10);
                const birthMonth = id.substring(10, 12);
                const birthDay = id.substring(12, 14);
                const birthday = `${birthYear}-${birthMonth}-${birthDay}`;

                // 获取性别
                const genderCode = id.charAt(16);
                const gender = genderCode % 2 === 0 ? '女' : '男';
                const sex = genderCode % 2 === 0 ? 0 : 1;

                return {
                    id,
                    birthday,
                    gender,
                    sex
                };
            } else {
                req.msg("无效的身份证号码")
            }
        }
    }
}
</script>

<style>
@import "./sickPeopleDet.css";
</style>