<template>
    <view class="collection-container">

        <!-- 商品列表 -->
        <scroll-view scroll-y class="product-list">
            <view class="product-item" v-for="(item, index) in collectionList" :key="index">
                <!-- 左侧标签 -->
                <view class="tag-container">
                    <view class="tag">滋补养生</view>
                    <view class="medical-tag">医保</view>
                </view>

                <!-- 商品信息 -->
                <view class="product-content">
                    <image class="product-image" :src="item.img" mode="aspectFill"></image>
                    <view class="product-info">
                        <view class="product-title">
                            <text class="otc-tag">OTC</text>
                            <text class="name">{{ item.name }}</text>
                        </view>
                        <view class="usage">{{ item.usage }}</view>
                        <view class="price">¥{{ item.price }}</view>
                    </view>
                </view>

                <!-- 添加按钮 -->
                <view class="add-btn" @click="addToCart(item)">
                    <text class="iconfont" style="font-size:22rpx;">&#xe634;</text>
                </view>
            </view>
        </scroll-view>

        <!-- 底部购物车栏 -->
        <view class="cart-bar">
            <view class="cart-info">
                <text class="cart-icon iconfont">&#xe635;</text>
                <text class="selected-text">已选 ({{ selectedCount }})</text>
            </view>
            <view class="price-info">
                <text class="price-symbol">¥</text>
                <text class="total-price">{{ totalPrice }}</text>
            </view>
            <view class="checkout-btn" @click="goToCart">
                去购物车
            </view>
        </view>
        <buy-popup ref="buyPopup" :product="{
            image: data.img || '',
            price: data.money || 0,
            originalPrice: data.salePrice || 0,
            name: data.goodsName || '',
            id: data.productId || '',
            maxBuy: data.maxBuy || 0,
            isMedicalInsurance: data.isMedicalInsurance,
            isOverallPlanning: data.isOverallPlanning
        }" @confirm="handleBuyConfirm" :cloud-stock="Number(data.warehouseStock) || 0"
            :store-stock="Number(data.sellStock) || 0" @click.stop />
    </view>
</template>

<script>
import buyPopup from "@/components/buy-popup/buy-popup"
export default {
    components: {
        buyPopup
    },
    data() {
        return {
            data: {},
            collectionList: [
                {
                    id: 1,
                    name: '东阿阿胶复方阿胶浆(无蔗糖)20ml*48支',
                    img: 'https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240802/0b85084dea364f62844e552d59a3b959.png',
                    price: '298.56',
                    usage: '口服 | 一日三次 | 每次1支'
                },
                {
                    id: 2,
                    name: '东阿阿胶复方阿胶浆(无蔗糖)20ml*48支',
                    img: 'https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240802/0b85084dea364f62844e552d59a3b959.png',
                    price: '298.56',
                    usage: '口服 | 一日三次 | 每次1支'
                }
            ],
            selectedCount: 2,
            totalPrice: '27.83'
        }
    },
    methods: {
        navigateBack() {
            uni.navigateBack()
        },
        addToCart(item) {
            this.data = item
            this.$refs.buyPopup.open()
        },
        goToCart() {
            // 跳转到购物车页面
            uni.navigateTo({
                url: '/pages/cart/cart'
            })
        }
    }
}
</script>

<style lang="scss">
.collection-container {
    min-height: 100vh;
    background-color: #f5f5f5;
    padding-bottom: 100rpx;
}

.nav-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    background-color: #fff;
    padding: 0 30rpx;
    position: relative;

    .title {
        font-size: 32rpx;
        font-weight: 500;
    }

    .back-icon,
    .right-icon {
        width: 44rpx;
        height: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.product-list {
    padding: 0;
}

.product-item {
    background-color: #fff;
    margin-bottom: 2rpx;
    padding: 30rpx 20rpx;
    display: flex;
    position: relative;
    border-radius: 0;
}

.tag-container {
    display: none;
}

.product-content {
    flex: 1;
    display: flex;
    margin-left: 0;

    .product-image {
        width: 180rpx;
        height: 180rpx;
        border-radius: 8rpx;
        margin-right: 20rpx;
    }

    .product-info {
        flex: 1;

        .product-title {
            display: flex;
            align-items: flex-start;
            margin-bottom: 10rpx;

            .otc-tag {
                background-color: #1890ff;
                color: #fff;
                font-size: 24rpx;
                padding: 2rpx 8rpx;
                border-radius: 4rpx;
                margin-right: 10rpx;
                height: 32rpx;
                line-height: 32rpx;
            }

            .name {
                font-size: 28rpx;
                color: #333;
                flex: 1;
                line-height: 1.4;
            }
        }

        .usage {
            font-size: 24rpx;
            color: #999;
            margin-bottom: 20rpx;
        }

        .price {
            font-size: 32rpx;
            color: #ff4d4f;
            font-weight: bold;
        }
    }
}

.add-btn {
    position: absolute;
    right: 20rpx;
    bottom: 30rpx;
    width: 44rpx;
    height: 44rpx;
    background-color: #ff4d4f;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 28rpx;
}

.cart-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

    .cart-info {
        display: flex;
        align-items: center;

        .cart-icon {
            font-size: 44rpx;
            margin-right: 10rpx;
            color: #666;
        }

        .selected-text {
            font-size: 28rpx;
            color: #666;
        }
    }

    .price-info {
        flex: 1;
        text-align: right;
        padding-right: 20rpx;

        .price-symbol {
            font-size: 24rpx;
            color: #ff4d4f;
        }

        .total-price {
            font-size: 36rpx;
            color: #ff4d4f;
            font-weight: bold;
        }
    }

    .checkout-btn {
        width: 180rpx;
        height: 80rpx;
        background-color: #ff4d4f;
        color: #fff;
        font-size: 28rpx;
        border-radius: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>