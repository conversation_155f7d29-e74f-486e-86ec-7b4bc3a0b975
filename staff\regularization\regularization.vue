<template>
    <view class="card-replace">
        <!-- 顶部日期选择和筛选 -->
        <view class="header">
            <uni-datetime-picker v-model="date" type="daterange" @change="dateChange" />
            <uni-data-select v-model="state" :clear="false" :localdata="stateList"
                @change="stateChange"></uni-data-select>
            <view @click=" $refs.popup.open()" class="add-btn">
                <text style="font-size: 28rpx;">新增</text>
            </view>
        </view>

        <!-- 申请列表 -->
        <view class="application-list">
            <view class="application-item" v-for="(item, index) in applications" :key="index">
                <view v-if="item.state == 0" class="status-tag" style="background: #ff6b35;">审批中</view>
                <view v-if="item.state == 1" class="status-tag" style="background: #67c23a;">已通过</view>
                <view v-if="item.state == 2 || item.state == 3" class="status-tag" style="background: #f23030;">已拒绝
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe62b;</text>
                    <text class="label">申请人：</text>
                    <text class="value">{{ item.userId }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe77c;</text>
                    <text class="label">转正时间：</text>
                    <text class="value">{{ item.applyTime }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe648;</text>
                    <text class="label">转正薪资：</text>
                    <text class="value" style="color: #f23030;font-weight: 600;">￥{{ item.fullMoney }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                    <text class="label">申请原因：</text>
                    <text class="value">{{ item.reason }}</text>
                </view>
                <!-- <view class="info-row" v-if="item.state == 2">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe615;</text>
                    <text class="label">拒绝原因：</text>
                    <text class="value">{{ item.rejectReason }}</text>
                </view> -->
                <view class="info-row" v-if="item.state == 0">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                    <text class="label">审批进度：</text>
                    <view @click="open(item)"
                        style="background-color: #ea1306;color: #fff;padding: 10rpx 20rpx;border-radius: 12rpx;">
                        查看审批进度</view>
                </view>
            </view>
            <view v-if="applications.length === 0" style="text-align: center;color: #999;">
                <image mode=""
                    src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png"
                    style="width: 372rpx;height: 312rpx;margin-top: 50rpx;">
                </image>
                <view style="margin-top: 10rpx;font-size: 28rpx;">暂无数据</view>
            </view>
        </view>
        <uni-popup :is-mask-click="false" ref="popup" type="bottom" border-radius="10px 10px 0 0">
            <view class="popup-content">
                <view class="popup-header">
                    <text>转正申请</text>
                    <text class="close-icon" @click="$refs.popup.close()">×</text>
                </view>
                <view class="popup-body">
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">申请人：</text>
                        <input type="text" disabled v-model="formData.name" class="input"
                            style="background: #f5f5f5;" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">转正薪资：</text>
                        <input type="text" v-model="formData.fullMoney" placeholder="请输入转正薪资" class="input" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">转正时间：</text>
                        <uni-datetime-picker v-model="formData.date" type="date">
                            <view class="date-input">
                                <view
                                    style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
                                    <text class="iconfont"
                                        style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
                                </view>
                                <text>{{ formData.date.length != 0 ? formData.date + ' 00:00:00' : '请选择日期' }}</text>
                            </view>
                        </uni-datetime-picker>
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">部门领导：</text>
                        <uni-data-select v-model="value" :localdata="range" @change="leaderChange"
                            class="selectleader"></uni-data-select>
                        <!-- <input type="text" v-model="leaderInputValue" placeholder="请输入部门领导" class="input"
                            @input="leaderChange" />
                        <view class="leader-list" v-if="leaderList.length > 0">
                            <view class="leader-item" v-for="(item, index) in leaderList" :key="index"
                                @click="selectLeader(item)">
                                {{ item.name }}
                            </view>
                        </view> -->
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">申请原因：</text>
                        <textarea v-model="formData.reason" placeholder="请输入申请原因" class="textarea" maxlength="-1" />
                    </view>
                    <!-- <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">备注：</text>
                        <textarea v-model="formData.reason" placeholder="请输入申请原因" class="textarea" />
                    </view> -->
                    <view class="form-item" style="align-items: flex-start;">
                        <text class="required"></text>
                        <text class="label label_1">附件：</text>
                        <view style="display: flex;flex-wrap: wrap;">
                            <view
                                style="margin-right: 20rpx;padding: 0 20rpx;line-height: 46rpx;border: 1px solid #F2F3F5; border-radius: 8rpx;color: #333;position: relative;margin-bottom: 10rpx;"
                                v-for="(item, index) in fileUrl" :key="index" @click="previewFile(index)">附件{{ index + 1
                                }}<i style="display: inline-block;width: 24rpx;height: 24rpx;border-radius: 50%;position: absolute;top: -10rpx;right: -10rpx;border: 1px solid #EA1306;background-color: #fff;text-align: center;line-height: 20rpx;font-size: 24rpx;color: #EA1306;"
                                    @click.stop="del_file(index)">x</i>
                            </view>
                            <view @click="uploadFile"
                                style="padding: 0 20rpx;line-height: 46rpx;border: 1px solid #F2F3F5; border-radius: 8rpx;color: #333;">
                                上传附件</view>
                        </view>
                    </view>
                    <view class="form-footer">
                        <view class="btn save" @click="saveForm">提交</view>
                        <view class="btn cancel" @click="$refs.popup.close()">取消</view>
                    </view>
                </view>
            </view>
        </uni-popup>
        <uni-popup :is-mask-click="false" ref="approve" type="bottom" border-radius="10px 10px 0 0">
            <view class="popup-content1">
                <view class="popup-header">
                    <text>审批进度</text>
                    <text class="close-icon" @click="$refs.approve.close()">×</text>
                </view>
                <view class="popup-body">
                    <view class="step-container">
                        <view class="step" v-for="(item, index) in stepList" :key="index">
                            <view class="step-number"></view>
                            <view class="step-title">
                                <view>开始时间:{{ item.startTime }}</view>
                                <view>{{ item.assignee }}：{{ item.taskName }}</view>
                                <view v-if="item.comment">审批意见：{{ item.comment }}</view>
                                <view v-if="item.endTime">结束时间:{{ item.endTime }}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const req = require("../../utils/request");

export default {
    data() {
        return {
            date: [],
            leaderList: [],
            leaderInputValue: '',
            state: 4,
            stateList: [
                { value: 4, text: "全部" },
                { value: 0, text: "审核中" },
                { value: 1, text: "已通过" },
                { value: 2, text: "已拒绝" },
                { value: 3, text: "已取消" },
            ],
            selectedStatus: '',
            applications: [],
            formData: {
                name: req.getStorage('userInfo').staffName,
                date: [],
                reason: '',
                leader: ''
            },
            pageNum: 1,
            total: 0,
            timer: null,
            is_headquarters: '',   // 是否总部人
            fileUrl: [],
            stepList: [],
            value: 0,
            range: [],
        }
    },
    onLoad() {
        this.getData();
        this.getBusiness();
    },
    onReachBottom() {
        this.pageNum++
        this.getData()
    },
    methods: {
        open(item) {
            req.showLoading();
            req.getRequest('/shopApi/translate/getDetails', {
                id: item.id
            }, res => {
                let data = [...res.data];
                let list = data.filter(item => item.processDefinitionId.includes('applyexamine'));
                let taskId = list.length > 0 ? list[0].taskId : ""
                req.getRequest(`/shopApi/translate/history/${taskId}`, {}, res => {
                    console.log(res);
                    this.stepList = res;
                    uni.hideLoading();
                    this.$refs.approve.open();
                })
            })
        },
        // 上传附件
        uploadFile() {
            console.log("上传附件");
            wx.chooseMessageFile({
                // count: 1, // 选择文件的个数
                type: 'all', // 文件类型，可以是图片、视频、文档等
                success: (res) => {
                    req.showLoading('上传中');
                    console.log(res);
                    const tempFilePaths = res.tempFiles; // 获取文件路径
                    this.fileUrl = [...this.fileUrl, ...tempFilePaths];
                    this.fileUrl.forEach((item, index) => {
                        req.uploadImg('/shopApi/translate/upload', item.path, res => {
                            this.fileUrl[index] = res;
                        });
                        // console.log(this.fileUrl);
                    });
                    uni.hideLoading();
                },
                fail(err) {
                    console.log('选择文件失败', err);
                }
            });
        },
        // 图片右上角删除角标事件
        del_file(index) {
            this.fileUrl.splice(index, 1);
        },
        // 预览文件
        previewFile(index) {
            const fileExtension = this.fileUrl[index].split('.').pop().toLowerCase();
            console.log(fileExtension);
            if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                // 预览图片
                uni.previewImage({
                    current: this.fileUrl[index], // 当前显示图片的http链接
                    urls: [this.fileUrl[index]]    // 需要预览的图片http链接列表
                });
                // fileExtension === 'pdf'
            } else if (['pdf', 'xls', 'ppt', 'docx', 'xlsx', 'pptx'].includes(fileExtension)) {
                uni.downloadFile({
                    url: this.fileUrl[index],
                    success: function (res) {
                        var filePath = res.tempFilePath;
                        uni.openDocument({
                            filePath: filePath,
                            showMenu: true,
                            success: function (res) {
                                console.log('打开文档成功');
                            }
                        });
                    }
                });
            } else {
                // 其他文件类型可以使用外部查看或下载方式
                wx.showModal({
                    title: '文件格式不支持预览',
                    content: '您可以下载该文件查看。',
                    confirmText: '下载',
                    success(res) {
                        if (res.confirm) {
                            wx.downloadFile({
                                url: this.jobInfo.annex,
                                success(downloadRes) {
                                    wx.openDocument({
                                        filePath: downloadRes.tempFilePath,
                                        success: function () {
                                            console.log('文件打开成功');
                                        }
                                    });
                                }
                            });
                        }
                    }
                });
            }
        },
        getBusiness() {
            req.getRequest('/shopApi/goOut/business', {
                name: req.getStorage('userInfo').staffName
            }, res => {
                this.is_headquarters = res.data;
                this.getReviewer();
            });
        },
        getReviewer() {
            req.getRequest('/shopApi/translate/getReviewer', {
                uid: req.getStorage('userInfo').introductionId,
                node: 'addleave',
                isHeadquarters: this.is_headquarters
            }, res => {
                if (res.data.length != 0) {
                    this.range = res.data.map(item => ({
                        value: item.id,
                        text: item.name
                    }))
                }
            });
        },
        leaderChange(e) {
            this.formData.leader = e ? this.range.find(item => item.value === e).text : "";
            // const value = e.detail.value.trim();
            // this.leaderInputValue = value;
            // this.formData.leader = ""
            // if (this.timer) {
            //     clearTimeout(this.timer);
            //     this.timer = null;
            // }
            // if (!value) {
            //     this.leaderList = [];
            //     return;
            // }
            // this.timer = setTimeout(() => {
            //     let params = {
            //         name: value
            //     };
            //     let url = this.is_headquarters == 1 ? "/shopApi/replacement/getStaff" : "/shopApi/goOut/getManager";
            //     if (this.is_headquarters != 1) {
            //         params.uid = req.getStorage('userInfo').introductionId;
            //     }
            //     req.getRequest(url, params, res => {
            //         this.leaderList = res.data.items;
            //     });
            // }, 500);
        },
        // selectLeader(item) {
        //     this.formData.leader = item.name;
        //     this.leaderInputValue = item.name;
        //     this.leaderList = [];
        // },
        getData() {
            req.getRequest("/shopApi/fullTime/list", {
                pageNum: this.pageNum,
                pageSize: 10,
                state: this.state == 4 ? null : this.state,
                startTime: this.date[0],
                endTime: this.date[1],
                staffId: req.getStorage('userInfo').introductionId,
            }, res => {
                this.total = res.total
                if (this.pageNum === 1) {
                    this.applications = res.data.items
                } else {
                    this.applications = [...this.applications, ...res.data.items]
                }
            })
        },
        stateChange(e) {
            this.state = e
            this.pageNum = 1
            this.getData()
        },
        dateChange(e) {
            this.date = e
            this.pageNum = 1
            this.$nextTick(() => {
                this.getData();
            })
        },
        onDateChange(e) {
            // 处理日期选择
        },
        saveForm() {
            if (!this.formData.name) {
                req.msg('请输入申请人')
                return;
            }
            if (!this.formData.fullMoney) {
                req.msg('请输入转正薪资')
                return;
            }
            if (this.formData.date.length == 0) {
                req.msg('请选择转正时间')
                return;
            }
            if (!this.formData.reason) {
                req.msg('请输入申请原因')
                return;
            }
            if (!this.formData.leader) {
                req.msg('请选择部门领导')
                return;
            }

            // TODO: 调用接口保存数据
            req.showLoading()
            req.postRequest("/shopApi/fullTime/add", {
                userId: req.getStorage('userInfo').staffName,
                fullMoney: this.formData.fullMoney,
                reason: this.formData.reason,
                state: 1,
                deptleader: this.formData.leader,
                applyTime: this.formData.date + ' 00:00:00',
                staffId: req.getStorage('userInfo').introductionId,
                attachment: JSON.stringify(this.fileUrl),//附件
            }, res => {
                uni.hideLoading();
                this.pageNum = 1;
                this.getData();
                this.$refs.popup.close();
                req.msg('提交成功');
                this.formData = {
                    name: req.getStorage('userInfo').staffName,
                    date: [],
                    reason: '',
                    leader: ''
                };
            })
        }
    }
}
</script>
<style lang="scss" scoped src="./regularization.scss"></style>
