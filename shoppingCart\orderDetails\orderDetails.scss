.orderDetails {
  background-color: #f8f8f8;
  padding: 20rpx;
  padding-bottom: 130rpx;

  .goods_stock {
    width: 710rpx;
    border-radius: 8rpx;
    background: #ffffff;
    box-shadow: 0px 8rpx 20rpx 0px rgba(0, 0, 0, 0.02);
    font-family: Alibaba Sans;
    font-size: 36rpx;
    font-weight: 600;
    line-height: 48rpx;
    color: #3d3d3d;
    padding: 30rpx 20rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;
  }

  .addGoods {
    width: 710rpx;
    height: 156rpx;
    border-radius: 8rpx;
    background: #ffffff;
    box-shadow: 0px 8rpx 20rpx 0px rgba(0, 0, 0, 0.02);
    padding: 30rpx 20rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Alibaba Sans;
    font-size: 32rpx;
    font-weight: 600;
    line-height: 48rpx;
    color: #3d3d3d;
    padding: 30rpx 20rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;

    .add_ {
      width: 136rpx;
      height: 48rpx;
      border-radius: 80rpx;
      background: #ea1306;
      color: #fff;
      text-align: center;
      font-weight: 400;
      font-size: 30rpx;
    }
  }

  .delivery_info {
    margin-bottom: 20rpx;

    .right_ {
      width: 96rpx;
      height: 48rpx;
      border-radius: 80rpx;
      box-sizing: border-box;
      border: 1px solid #ea1306;
      font-family: Alibaba Sans;
      font-size: 30rpx;
      font-weight: normal;
      line-height: 46rpx;
      text-align: center;
      color: #ea1306;
    }

    .info_ {
      padding: 0 20rpx 30rpx;
      font-family: Alibaba Sans;
      font-size: 30rpx;
      font-weight: normal;
      line-height: 36rpx;
      color: #4e5969;
    }
  }

  .goods_info {
    margin-bottom: 20rpx;

    .goods_ {
      border-bottom: 1px solid #f2f3f5;

      .goods_item {
        display: flex;

        // align-items: center;
        .goods_img {
          width: 160rpx;
          height: 136rpx;
          border-radius: 8rpx;
          background: #edf3fa;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20rpx;
          margin-bottom: 20rpx;
        }

        .goods_info {
          width: 356rpx;
          margin-right: 10rpx;
          font-family: Alibaba Sans;
          font-size: 30rpx;
          font-weight: normal;
          line-height: 28rpx;
          color: #4e5969;
        }

        .pay_ {
          width: 156rpx;
          font-size: 30rpx;
          font-weight: 600;
          line-height: 28rpx;
          color: #333333;
          text-align: right;
        }
      }
    }

    .goods_details {
      font-family: Alibaba Sans;
      font-size: 30rpx;
      font-weight: normal;
      line-height: 48rpx;
      color: #3d3d3d;
    }
  }

  .order_info {
    margin-bottom: 20rpx;

    .info_ {
      padding: 0 20rpx 20rpx;
      font-family: Alibaba Sans;
      font-size: 30rpx;
      font-weight: normal;
      line-height: 48rpx;
      color: #000000;

      .btn_ {
        width: 96rpx;
        height: 40rpx;
        border-radius: 20rpx;
        box-sizing: border-box;
        border: 1px solid #f2f3f5;
        font-family: Alibaba Sans;
        font-size: 30rpx;
        font-weight: normal;
        line-height: 38rpx;
        text-align: center;
        color: #3d3d3d;
        margin-left: 8rpx;
      }
    }
  }

  .bottom-btns {
    .btn {
      padding: 15rpx 30rpx;
      border-radius: 30rpx;
      background: #ea1306;
      color: #fff;
    }

    .btn2 {
      padding: 15rpx 30rpx;
      border-radius: 30rpx;
      border: 1px solid #ddd;
      margin-right: 20rpx;
    }
  }

  .single-line-text {
    white-space: nowrap;
    /* 防止换行 */
    overflow: hidden;
    /* 隐藏超出部分 */
    text-overflow: ellipsis;
    /* 使用省略���表示超出部分 */
    max-width: 400rpx;
    /* 设置宽度，可以根据需求调整 */
  }

  /deep/.uni-section {
    border-radius: 8rpx;
  }

  /deep/.uni-section .uni-section-header__decoration {
    background-color: #ea1306;
  }

  /deep/.uni-section__content-title {
    font-size: 32rpx !important;
    font-weight: 600;
  }

  .logistics {
    display: flex;
    align-items: center;
    padding: 30rpx 24rpx;
    background: #fff;
    margin: 20rpx 0;

    .logistics-icon {
      color: #EA1306;
      font-size: 40rpx;
      margin-right: 20rpx;
    }

    .logistics-content {
      flex: 1;
      margin-right: 20rpx;

      .logistics-text {
        font-size: 28rpx;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .logistics-time {
        font-size: 24rpx;
        color: #999;
        margin-top: 10rpx;
      }
    }

    .arrow-icon {
      color: #999;
      font-size: 32rpx;
    }
  }
}

.merchant-dialog {
  .dialog-content {
    background-color: #fff;
    height: 70vh;
    border-radius: 20rpx;
    padding: 40rpx;
    box-sizing: border-box;

    .dialog-title {
      text-align: center;
      font-size: 32rpx;
      font-weight: 600;
      line-height: 50rpx;
      margin-bottom: 20rpx;
    }

    .scroll-box {
      height: calc(70vh - 170rpx);

      .merchant-item {
        text-align: center;
        font-size: 30rpx;
        line-height: 56rpx;
      }
    }

    .dialog-cancel {}
  }
}

.popup-content {
  width: 690rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-sizing: border-box;

  .popup-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .delivery-options {
    display: flex;
    margin-bottom: 40rpx;

    .delivery-item {
      flex: 1;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2rpx solid #dcdfe6;
      margin: 0 20rpx;
      border-radius: 10rpx;

      &.active {
        border-color: #f00;

        .radio-wrap {
          .radio-inner {
            background: #f00;
          }
        }
      }

      .radio-wrap {
        width: 32rpx;
        height: 32rpx;
        border: 2rpx solid #dcdfe6;
        border-radius: 50%;
        margin-right: 10rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .radio-inner {
          width: 20rpx;
          height: 20rpx;
          border-radius: 50%;
        }
      }
    }
  }

  .delivery-info {
    .info-item {
      display: flex;
      align-items: center;
      padding: 20rpx;

      .label {
        width: 100%;
        font-size: 28rpx;
        color: #333;
      }
    }

    .area {
      display: flex;
      align-items: center;
      padding: 0 20rpx;

      .dzico {
        width: 32rpx;
        height: 32rpx;
        margin-right: 10rpx;
      }

      .address {
        font-size: 28rpx;
        color: #333;
      }

      .iconfont {
        font-size: 32rpx;
      }
    }
  }

  .popup-buttons {
    display: flex;
    margin-top: 40rpx;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      border-radius: 10rpx;
      font-size: 28rpx;
    }

    .cancel-btn {
      background: #f5f5f5;
      color: #666;
      margin-right: 20rpx;
    }

    .confirm-btn {
      background: #f00;
      color: #fff;
    }
  }
}