<template>
    <view class="coupon-list">
        <!-- 顶部状态切换 -->
        <view class="tab-bar">
            <view v-for="(item, index) in tabList" :key="index" class="tab-item"
                :class="{ 'active': couponType === index }" @click="switchTab(index)">
                {{ item.text }}
            </view>
        </view>
        <!-- 优惠券列表 -->
        <view class="coupon-container" :style="{paddingLeft:formCart?'0':'20rpx'}">
            <view v-for="(item, index) in couponList" :key="index" style="display: flex;">
                <view
                    style="width: 80rpx;margin-bottom: 20rpx;display: flex;justify-content: center;align-items: center;" v-if="formCart">
                    <view class="iconfont selected-icon" 
                        :class="{ 'selected': selectedCoupons.includes(item.id) }"  @click="selectCoupon(item.id,item.are,item.couponState)" v-if="item.couponState == 1 && item.are&&item.couponBrief=='金卡会员返现'">{{ selectedCoupons.includes(item.id) ?  '&#xe610;' : '&#xe611;'  }}</view>
                       <uni-icons type="circle-filled" size="28" color="#8F8F8F" v-else></uni-icons>
                </view>
                <view class="coupon-item" style="flex: 1;">
                    <!-- 优惠券主体内容 -->
                    <view class="coupon-main" @click="toggleCoupon(index)">
                        <view class="left" v-if="item.couponType != 3">
                            <view class="price">
                                <text class="symbol">¥</text>
                                <text class="num">{{ item.couponAmount }}</text>
                            </view>
                            <view class="condition">满 {{ item.couponFull }} 元可用</view>
                        </view>
                        <view class="left" v-else-if="item.couponType == 3">
                            <view class="price">
                                <text class="num">
                                    {{ item.couponAmount * 10 }}折
                                </text>
                            </view>
                            <view class="condition">满 {{ item.couponFull }} 元可用</view>
                        </view>
                        <view class="right">
                            <view class="title">{{ item.couponTitle }}</view>
                            <view class="time">有效期至{{ item.couponEnd }}</view>
                            <!-- <view class="desc">{{ item.useAction }}</view> -->
                            <view class="btn" @click.stop="useCoupon(item.id, item.are, item.couponState)"
                                :class="{ 'used': item.couponState === 2, 'expired': item.couponState === 3 }">
                                {{ getBtnText(item.couponState) }}
                            </view>
                            <view style="font-size: 24rpx;color: #EA1306;line-height: 56rpx;"
                                v-if="item.couponState == 1">
                                {{ item.are ? '' : '暂不满足使用条件' }}
                            </view>
                            <view class="arrow" :class="{ 'expanded': item.isExpanded }">
                                <text class="iconfont">&#xe61c;</text>
                            </view>

                        </view>
                    </view>
                    <!-- 移除 transition 组件，直接使用 v-show -->
                    <view class="coupon-detail" :class="{ 'expanded': item.isExpanded }"
                        :style="{ height: item.isExpanded ? 'auto' : '0' }">
                        <view class="detail-content">
                            <view class="detail-item">
                                <text class="label">编号：</text>
                                <text class="content">{{ item.couponId }}</text>
                            </view>
                            <view class="detail-item">
                                <text class="label">领取时间：</text>
                                <text class="content">{{ item.createDate }}</text>
                            </view>
                            <view class="detail-item">
                                <view class="label">使用说明：</view>
                                <view class="content">
                                    <view v-for="(val, ind) in item.useActionList" :key="ind">
                                        {{ (ind + 1) + '.' + val }}
                                    </view>
                                </view>
                            </view>
                            <view class="detail-item">
                                <text class="label">使用门槛：</text>
                                <text class="content">订单满{{ item.couponFull }}元可用</text>
                            </view>
                            <view class="detail-item">
                                <text class="label">有效期限：</text>
                                <text class="content">{{ item.couponStart }} 至 {{ item.couponEnd }}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="add-coupon" v-if="selectedCoupons.length > 0">
            <view style="font-size: 24rpx;color: #666;line-height: 28rpx;margin-bottom: 20rpx;">只限金卡返现代金券叠加使用，其他券不参与叠加使用</view>
            <view class="add-coupon-btn" @click="useCoupon('')">叠加使用</view>
        </view>
        <!-- 无数据展示 -->
        <view class="empty" v-if="couponList.length === 0">
            <image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png"
                mode="" style="width: 372rpx;height: 312rpx;"></image>
            <text>暂无优惠券~</text>
        </view>
    </view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
    data() {
        return {
            formCart: "",
            tabList: [{ text: '全部', value: 0 }, { text: '满减劵', value: 1 }, { text: '代金券', value: 2 }, { text: '折扣劵', value: 3 }],
            currentTab: 0,
            couponType: 0,
            pageNum: 1,
            pageSize: 10,
            total: 0,
            couponList: [],
            cartIds: "",
            merchantId: "",
            selectedCoupons:[]
        }
    },
    onLoad(options) {
        console.log(options, "我的优惠券页面");
        this.formCart = options.formCart ? Number(options.formCart) : 0;
        this.cartIds = options.cartIds ? options.cartIds : "";
        this.merchantId = options.merchantId ? Number(options.merchantId) : req.getStorage("currentStore").id
    },
    onShow() {
        // if (this.formCart) {
        //     this.getListCart();
        // } else {
        //     this.getList();
        // }
        this.getList();
    },
    methods: {
        switchTab(value) {
            // this.currentTab = value;
            this.pageNum = 1;
            this.couponType = value;
            // TODO: 根据状态获取对应的优惠券列表
            // if (this.formCart) {
            //     this.getListCart();
            // } else {
            //     this.getList();
            // }
            this.getList();
        },
        selectCoupon(id,are,couponState) {
            console.log(id,'多选');
            if (are && couponState == 1) {
                const indexInSelected = this.selectedCoupons.indexOf(id);
                if (indexInSelected > -1) {
                    this.selectedCoupons.splice(indexInSelected, 1); // 取消选择
                } else {
                    this.selectedCoupons.push(id); // 添加选择
                }
            }
        },
        useCoupon(id, are, couponState) {
            if (id) {
                if (this.formCart) {
                    if (are && couponState == 1) {
                        const eventChannel = this.getOpenerEventChannel();
                        eventChannel.emit('acceptDataFromOpenedPage', { id });
                        uni.navigateBack();
                    } else {
                        req.msg("该优惠券暂不可用");
                    };
                };  
            } else {
                const eventChannel = this.getOpenerEventChannel();
                eventChannel.emit('acceptDataFromOpenedPage', { id: this.selectedCoupons });
                uni.navigateBack();
            }
        },
        getBtnText(status) {
            const textMap = {
                1: '立即使用',
                2: '已使用',
                3: '已过期'
            }
            return textMap[status]
        },
        toggleCoupon(index) {
            this.$set(this.couponList[index], 'isExpanded', !this.couponList[index].isExpanded)
        },
        //   下拉刷新
        onPullDownRefresh() {
            this.pageNum = 1;
            this.couponList = [];
            // if (this.formCart) {
            //     this.getListCart();
            // } else {
            //     this.getList();
            // }
            this.getList();
            uni.stopPullDownRefresh();
        },
        //   上拉触底
        onReachBottom() {
            if (this.pageNum * this.pageSize >= this.total) return req.msg("没有更多数据了")
            this.pageNum++;
            // console.log("触底", this.pageNum);
            // if (this.formCart) {
            //     this.getListCart();
            // } else {
            //     this.getList();
            // }
            this.getList();
        },
        // 获取我的优惠券
        getList() {
            req.showLoading();
            req.getRequest("/shopApi/coupon/user", {
                couponType: this.couponType ? this.couponType : "",
                ids: this.cartIds,
                merchantId: this.merchantId,
                pageNum: this.pageNum,
                pageSize: this.pageSize,
            }, res => {
                console.log(res, "我的优惠券");
                if (this.pageNum === 1) {
                    this.couponList = [...res.data.list];
                } else {
                    this.couponList = [...this.couponList, ...res.data.list];
                };
                this.total = res.data.total;
                uni.hideLoading();
            })
        },
        // 获取我的优惠券 - 来自购物车入口
        getListCart() {
            req.getRequest("/shopApi/coupon/verificationCoupon", {
                couponType: this.couponType ? this.couponType : "",
                pageNum: this.pageNum,
                pageSize: this.pageSize
            }, res => {
                console.log(res, "我的优惠券");
                if (this.pageNum === 1) {
                    this.couponList = [...res.data.list];
                } else {
                    this.couponList = [...this.couponList, ...res.data.list];
                };
                this.total = res.data.total;
            })
        },
    }
}
</script>

<style lang="scss" scoped src="./couponList.scss"></style>