<template>
	<view class="coupon-list">
		<!-- 优惠券列表 -->
		<view class="coupon-container">
			<block v-for="(item, index) in couponList" :key="index">
				<view class="coupon-item" style="height: 200rpx;">
					<!-- 优惠券主体内容 -->
					<view class="coupon-main" @click="toggleCoupon(index)" style="height: 200rpx;">
						<view class="left" v-if="item.couponType != 3">
							<view class="price">
								<text class="symbol">¥</text>
								<text class="num">
									{{ item.couponAmount }}
								</text>
							</view>
							<view class="condition">满 {{ item.couponFull }} 元可用</view>
						</view>
						<view class="left" v-else-if="item.couponType == 3">
							<view class="price">
								<text class="num">
									{{ item.couponAmount * 10 }}折
								</text>
							</view>
							<view class="condition">满 {{ item.couponFull }} 元可用</view>
						</view>
						<view class="right">
							<view class="title single-line-text" style="width: 380rpx;">{{ item.couponTitle }}
							</view>
							<view class="time">有效期至{{ item.couponEnd }}</view>
							<!-- <view class="desc">{{ item.useAction }}</view> -->
							<!-- <view class="btn" @click.stop="useCoupon"
								:class="{ 'used': item.couponState === 2, 'expired': item.couponState === 3 }">
								{{ getBtnText(item.couponState) }}
							</view> -->
							<view class="btn" @click.stop="useCoupon(item.id)">
								立即领取
							</view>
							<view class="arrow" :class="{ 'expanded': item.isExpanded }">
								<text class="iconfont">&#xe61c;</text>
							</view>
						</view>
					</view>

					<!-- 移除 transition 组件，直接使用 v-show -->
					<view class="coupon-detail" :class="{ 'expanded': item.isExpanded }"
						:style="{ height: item.isExpanded ? 'auto' : '0' }">
						<view class="detail-content">
							<view class="detail-item" v-if="item.createDate">
								<text class="label">领取时间：</text>
								<text class="content">{{ item.createDate }}</text>
							</view>
							<view class="detail-item">
								<text class="label">编号：</text>
								<text class="content">{{ item.id }}</text>
							</view>
							<view class="detail-item">
								<text class="label">使用说明：</text>
								<text class="content">{{ item.couponBrief }}</text>
							</view>
							<!-- <view class="detail-item">
								<view class="label">使用说明：</view>
								<view class="content">
									<view v-for="(val, ind) in item.useActionList" :key="ind">
										{{ (ind + 1) + '.' + val }}
									</view>
								</view>
							</view> -->
							<view class="detail-item">
								<text class="label">使用门槛：</text>
								<text class="content">订单满{{ item.couponFull }}元可用</text>
							</view>
							<view class="detail-item">
								<text class="label">有效期限：</text>
								<text class="content">{{ item.couponStart }} 至 {{ item.couponEnd }}</text>
							</view>
						</view>
					</view>
				</view>
			</block>
		</view>
		<!-- 无数据展示 -->
		<view class="empty" v-if="couponList.length === 0">
			<image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png"
				mode="aspectFit"></image>
			<text>暂无优惠券~</text>
		</view>
		<view @click="goUrl('/mine/couponList/couponList')" class="myCopon">我的优惠劵</view>
	</view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
	data() {
		return {
			tabList: [{ text: '全部', value: 0 }, { text: '满减劵', value: 1 }, { text: '兑换劵', value: 2 }, { text: '配送劵', value: 3 }],
			currentTab: 0,
			couponType: 0,
			pageNum: 1,
			pageSize: 10,
			total: 0,
			couponList: []
		}
	},
	onShow() {
		this.getList();
	},
	methods: {
		// switchTab(value) {
		// 	// this.currentTab = value;
		// 	this.couponType = value;
		// 	// TODO: 根据状态获取对应的优惠券列表
		// 	this.getList();
		// },
		// 立即领取
		useCoupon(id) {
			console.log(1);
			req.postRequest1("/shopApi/coupon/receive", {
				id: id
			}, res => {
				console.log(res, "优惠");
				req.msg("领取成功");
				// this.pageNum = 1;
				// this.getList();
			})
		},
		// getBtnText(status) {
		// 	const textMap = {
		// 		1: '立即使用',
		// 		2: '已使用',
		// 		3: '已过期'
		// 	}
		// 	return textMap[status]
		// },
		// 优惠详情
		toggleCoupon(index) {
			this.$set(this.couponList[index], 'isExpanded', !this.couponList[index].isExpanded)
		},
		//   下拉刷新
		onPullDownRefresh() {
			this.pageNum = 1;
			this.pageSize = 10;
			this.couponList = [];
			this.getList();
			uni.stopPullDownRefresh();
		},
		//   上拉触底
		onReachBottom() {
			if (this.pageNum * this.pageSize >= this.total) return req.msg("没有更多数据了")
			this.pageNum++;
			// console.log("触底", this.pageNum);
			this.getList();
		},
		// 获取领券中心
		getList() {
			req.getRequest("/shopApi/coupon/list", {
				// isShow: 1,
				couponType: this.couponType ? this.couponType : "",
				pageNum: this.pageNum,
				pageSize: this.pageSize
			}, res => {
				console.log(res, "领券中心");
				if (this.pageNum === 1) {
					this.couponList = [...res.data.list];
				} else {
					this.couponList = [...this.couponList, ...res.data.list];
				};
				this.total = res.data.total;
			})
		},
		goUrl(url) {
			uni.navigateTo({
				url: url
			});
		},
	}
}
</script>

<style lang="scss" src="./couponCenter.scss" scoped></style>
