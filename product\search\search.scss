.search-page {
  min-height: 100vh;
  background: #fff;
  
  .search-header {
    display: flex;
    align-items: center;
    padding: 20rpx 30rpx;
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 100;
    
    .search-box {
      flex: 1;
      height: 72rpx;
      // background: #F5F5F5;
      border-radius: 36rpx;
      display: flex;
      align-items: center;
      padding: 0 30rpx;
      margin-right: 20rpx;
      border: 1px solid #EA1306;
      
      .iconfont {
        font-size: 32rpx;
        color: #999;
        margin-right: 10rpx;
      }
      
      input {
        flex: 1;
        height: 100%;
        font-size: 28rpx;
      }
      
      .clear-btn {
        padding: 10rpx;
        color: #999;
        font-size: 24rpx;
      }
    }
    
    .search-btn {
      padding: 0 15rpx;
      font-size: 28rpx;
      color: #333;
      
      &:active {
        opacity: 0.7;
      }
    }
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    
    .title {
      font-size: 30rpx;
      color: #333;
      font-weight: bold;
    }
    
    .clear-history {
      font-size: 26rpx;
      color: #999;
      display: flex;
      align-items: center;
      
      .iconfont {
        font-size: 28rpx;
        margin-right: 6rpx;
      }
    }
  }
  
  .history-list,
  .hot-list {
    display: flex;
    flex-wrap: wrap;
    padding: 0 20rpx;
    gap: 20rpx;
    
    .history-item,
    .hot-item {
      padding: 12rpx 30rpx;
      background: #F5F5F5;
      border-radius: 30rpx;
      font-size: 26rpx;
      color: #666;
      
      &.hot {
        color: #EA1306;
        background: rgba(234, 19, 6, 0.1);
      }
    }
  }
  
  .search-result {
    padding: 20rpx;
    
    .result-item {
      display: flex;
      padding: 20rpx;
      border-bottom: 2rpx solid #f5f5f5;
      
      .product-img {
        width: 160rpx;
        height: 160rpx;
        border-radius: 12rpx;
        margin-right: 20rpx;
      }
      
      .product-info {
        flex: 1;
        
        .title {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 16rpx;
          line-height: 1.4;
        }
        
        .price {
          color: #EA1306;
          margin-bottom: 12rpx;
          
          .symbol {
            font-size: 24rpx;
          }
          
          .number {
            font-size: 32rpx;
            font-weight: bold;
          }
        }
        
        .sales {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
  
  .empty-result {
    padding-top: 200rpx;
    text-align: center;
    
    image {
      width: 240rpx;
      height: 240rpx;
      margin-bottom: 20rpx;
    }
    
    text {
      font-size: 28rpx;
      color: #999;
    }
  }
  
  .suggestion-list {
    background: #fff;
    padding: 0 30rpx;
    
    .suggestion-item {
      padding: 20rpx 0;
      font-size: 28rpx;
      border-bottom: 1px solid #f5f5f5;
      
      &:active {
        background-color: #f8f8f8;
      }
      
      rich-text {
        line-height: 1.5;
      }
    }
  }
} 