<template>
	<view class="schedule-container">
		<view class="schedule-grid">
			<scroll-view scroll-x class="scroll-container" @scroll="onScroll" show-scrollbar="false">
				<view class="grid-content">
					<!-- 表头 -->
					<view class="grid-header">
						<view class="name-cell fixed-column">员工姓名</view>
						<view class="dates-container">
							<view v-for="(date, index) in monthDates" :key="index" class="date-cell">
								<text class="date-text">{{ formatDate(date) }}</text>
								<text class="week-text">{{ getWeekDay(date) }}</text>
							</view>
						</view>
					</view>

					<!-- 表格内容 -->
					<view class="grid-body">
						<view v-for="(staff, staffIndex) in staffSchedule" :key="staffIndex" class="grid-row">
							<view class="name-cell fixed-column">{{ staff.name }}</view>
							<view class="shifts-container">
								<view v-for="(shift, dateIndex) in staff.shifts" :key="dateIndex" class="schedule-cell"
									:class="{ 'weekend': isWeekend(monthDates[dateIndex]) }">
									{{ shift || '' }}
								</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
const req = require("../../utils/request");
export default {
	data() {
		return {
			currentMonth: new Date().getMonth() + 1,
			monthDates: [],
			staffSchedule: [],  // 清空初始数据，改为从接口获取
			classType: "",
			storeDepartmentId: ""
		}
	},
	onLoad(options) {
		console.log(options.id);
		this.classType = Number(options.classType);
		this.storeDepartmentId = Number(options.storeDepartmentId);
		this.generateMonthDates();
		this.getschedule();
	},
	methods: {
		// 获取排班信息
		getschedule() {
			req.showLoading();
			req.getRequest('/shopApi/scheduling/getInfo', {
				type: this.classType,
				storeDepartmentId: this.storeDepartmentId,
			}, res => {
				// 假设接口返回的数据格式如下：
				// const mockData = [{
				//   staffName: '张三',
				//   paramsMap: [
				//     { scheduleDate: "2025-02-04", shiftName: "门店早班-2" },
				//     { scheduleDate: "2025-02-08", shiftName: "门店早班-2" }
				//   ]
				// }]
				if (res.data.length != 0) {
					this.processScheduleData(res.data)
				};
			})
		},

		// 处理排班数据
		processScheduleData(data) {
			this.staffSchedule = data.map(item => {
				// 创建一个与当月日期等长的空数组
				const shifts = new Array(this.monthDates.length).fill('')

				// 遍历该员工的排班记录
				item.paramsMap.forEach(schedule => {
					const scheduleDate = new Date(schedule.scheduleDate)
					// 找到日期对应的索引
					const dateIndex = this.monthDates.findIndex(date =>
						date.getDate() === scheduleDate.getDate() &&
						date.getMonth() === scheduleDate.getMonth() &&
						date.getFullYear() === scheduleDate.getFullYear()
					)

					// 如果找到对应日期，则填入班次信息
					if (dateIndex !== -1) {
						shifts[dateIndex] = schedule.shiftName
					}
				})
				uni.hideLoading();
				return {
					name: item.staffName,
					shifts: shifts
				}
			})
		},

		formatDate(date) {
			return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
		},

		getWeekDay(date) {
			const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
			return weekDays[date.getDay()]
		},

		isWeekend(date) {
			return date.getDay() === 0 || date.getDay() === 6
		},

		generateMonthDates() {
			const now = new Date()
			const year = now.getFullYear()
			const month = now.getMonth()
			const lastDay = new Date(year, month + 1, 0)
			const daysInMonth = lastDay.getDate()

			this.monthDates = Array.from({ length: daysInMonth }, (_, i) => {
				return new Date(year, month, i + 1)
			})
		},
	},

}
</script>

<style scoped>
.schedule-container {
	padding: 20rpx;
	width: 100vw;
	height: 100vh;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	background: #fff;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	padding-left: 100rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
}

.schedule-grid {
	flex: 1;
	border: 1px solid #eee;
	border-radius: 8rpx;
	position: relative;
	overflow: hidden;
}

.scroll-container {
	width: 100%;
	height: 100%;
}

.grid-content {
	display: flex;
	flex-direction: column;
	min-width: fit-content;
}

.grid-header {
	display: flex;
	background-color: #f8f8f8;
	border-bottom: 1px solid #eee;
	position: relative;
}

.fixed-column {
	position: sticky;
	left: 0;
	z-index: 2;
	background: #f8f8f8;
	box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.name-cell {
	width: 100rpx;
	min-width: 100rpx;
	max-width: 100rpx;
	padding: 20rpx 10rpx;
	border-right: 1px solid #eee;
	font-weight: bold;
	text-align: center;
	font-size: 22rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	display: flex;
	justify-content: center;
	align-items: center;
}

.dates-container,
.shifts-container {
	display: flex;
}

.date-cell {
	min-width: 120rpx;
	padding: 10rpx;
	border-right: 1px solid #eee;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.grid-body {
	flex: 1;
}

.grid-row {
	display: flex;
	border-bottom: 1px solid #eee;
	position: relative;
}

.schedule-cell {
	min-width: 120rpx;
	padding: 20rpx 10rpx;
	text-align: center;
	border-right: 1px solid #eee;
	font-size: 22rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.weekend {
	background-color: #f9f9f9;
}

.date-text {
	font-size: 24rpx;
	margin-bottom: 4rpx;
}

.week-text {
	font-size: 24rpx;
	color: #666;
}
</style>
