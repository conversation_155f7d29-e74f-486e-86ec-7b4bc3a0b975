{"id": "next-data-select", "displayName": "next-data-select 下拉框选择器(添加下拉框检索，多选功能，多选搜索功能，自定义数据)", "version": "1.0.5", "description": "通过数据驱动的下拉框选择器(添加下拉框检索，多选功能，多选搜索功能，自定义数据)", "keywords": ["下拉筛选", "select", "next-data-select", "下拉框", "下拉选"], "repository": "", "engines": {"HBuilderX": "^3.1.1"}, "directories": {"example": ""}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "type": "component-vue"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"App": {"app-vue": "u", "app-nvue": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "京东": "y"}, "快应用": {"华为": "y", "联盟": "y"}, "Vue": {"vue2": "y", "vue3": "y"}}}}}