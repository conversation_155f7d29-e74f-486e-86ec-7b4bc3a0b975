<template>
	<view class="o2o-pay">
		<view class="pay-header">
			<view class="amount">
				<text class="label">支付金额</text>
				<view class="price">
					<text class="symbol">¥</text>
					<text class="value">{{amount}}</text>
				</view>
			</view>
		</view>
		
		<view class="pay-body">
			<view class="merchant-info">
				<view class="title">商家信息</view>
				<view class="info-item">
					<text class="label">商家名称</text>
					<text class="value">{{merchantName}}</text>
				</view>
				<view class="info-item">
					<text class="label">订单编号</text>
					<text class="value">{{orderNo}}</text>
				</view>
			</view>
			
			<view class="pay-method">
				<view class="title">支付方式</view>
				<view class="method-item" @click="selectPayMethod('wxpay')">
					<view class="left">
						<text class="iconfont">&#xe607;</text>
						<text class="name">微信支付</text>
					</view>
					<text class="iconfont" :class="{'selected': payMethod === 'wxpay'}">&#xe761;</text>
				</view>
			</view>
		</view>
		
		<view class="pay-footer">
			<button class="pay-btn" @click="handlePay">确认支付</button>
		</view>
	</view>
</template>

<script>
const req = require("../../utils/request");
export default {
	data() {
		return {
			amount: '0.00',
			merchantName: '',
			orderNo: '',
			payMethod: 'wxpay'
		}
	},
	onLoad(options) {
		// 获取页面参数
		if (options.amount) {
			this.amount = options.amount
		}
		if (options.merchantName) {
			this.merchantName = options.merchantName
		}
		if (options.orderNo) {
			this.orderNo = options.orderNo
		}
	},
	methods: {
		// 选择支付方式
		selectPayMethod(method) {
			this.payMethod = method
		},
		// 处理支付
		handlePay() {
			if (!this.amount || !this.merchantName || !this.orderNo) {
				uni.showToast({
					title: '支付信息不完整',
					icon: 'none'
				})
				return
			}
			
			// TODO: 调用支付接口
			uni.showLoading({
				title: '支付处理中'
			})
			
			// 模拟支付请求
			setTimeout(() => {
				uni.hideLoading()
				uni.showToast({
					title: '支付成功',
					icon: 'success',
					duration: 2000,
					success: () => {
						setTimeout(() => {
							uni.navigateBack()
						}, 2000)
					}
				})
			}, 1500)
		}
	}
}
</script>

<style lang="scss">
.o2o-pay {
	min-height: 100vh;
	background: #f5f5f5;
	padding-bottom: calc(140rpx + env(safe-area-inset-bottom));
	
	.pay-header {
		background: #fff;
		padding: 40rpx 30rpx;
		
		.amount {
			text-align: center;
			
			.label {
				font-size: 28rpx;
				color: #666;
				margin-bottom: 20rpx;
				display: block;
			}
			
			.price {
				.symbol {
					font-size: 40rpx;
					color: #333;
				}
				
				.value {
					font-size: 60rpx;
					font-weight: bold;
					color: #333;
					margin-left: 4rpx;
				}
			}
		}
	}
	
	.pay-body {
		margin-top: 20rpx;
		
		.merchant-info, .pay-method {
			background: #fff;
			padding: 30rpx;
			margin-bottom: 20rpx;
			
			.title {
				font-size: 30rpx;
				font-weight: 500;
				color: #333;
				margin-bottom: 20rpx;
			}
		}
		
		.info-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.label {
				font-size: 28rpx;
				color: #666;
			}
			
			.value {
				font-size: 28rpx;
				color: #333;
			}
		}
		
		.method-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 0;
			
			.left {
				display: flex;
				align-items: center;
				
				.iconfont {
					font-size: 40rpx;
					color: #07c160;
					margin-right: 20rpx;
				}
				
				.name {
					font-size: 28rpx;
					color: #333;
				}
			}
			
			.iconfont {
				font-size: 40rpx;
				color: #ddd;
				
				&.selected {
					color: #07c160;
				}
			}
		}
	}
	
	.pay-footer {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background: #fff;
		padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));
		
		.pay-btn {
			height: 88rpx;
			line-height: 88rpx;
			text-align: center;
			background: #07c160;
			color: #fff;
			font-size: 32rpx;
			font-weight: 500;
			border-radius: 44rpx;
			
			&:active {
				opacity: 0.9;
			}
		}
	}
}
</style> 