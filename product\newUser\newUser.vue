<template>
    <view class="new-user">
        <!-- 商品列表 -->
        <view class="goods-list">
            <view @click="goToDetail(item)" v-for="(item, index) in newUserList" :key="index" class="goods-item">
                <image :src="item.img" mode="aspectFill" class="goods-img"></image>
                <!-- <view class="goods-tag">OTC</view> -->
                <view class="goods-info">
                    <view class="goods-name">{{ item.productName }}</view>
                    <view v-if=item.spec class="goods-spec">规格：{{ item.spec }}</view>
                    <view class="price-wrap">
                        <view class="price">
                            <text class="symbol">¥</text>
                            <text class="num">{{ item.money }}</text>
                            <text class="original">/¥{{ item.orPrice }}</text>
                        </view>
                        <image class="cart-btn"
                            src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241113/a9c6fe546b5c4b339a8c99ec8740d4eb.png"
                            mode="widthFix" @click.stop="openBuyPopup(item)"></image>
                    </view>
                </view>
            </view>
        </view>

        <!-- 购买弹窗 -->
        <buy-popup ref="buyPopup" :is-cart-action="isCartAction" :product="{
            image: currentProduct.img || '',
            price: currentProduct.money || 0,
            originalPrice: currentProduct.orPrice || 0,
            name: currentProduct.productName || '',
            id: currentProduct.productId || '',
            maxBuy: currentProduct.maxBuy || 0,
            isMedicalInsurance: currentProduct.isMedicalInsurance,
            isOverallPlanning: currentProduct.isOverallPlanning
        }" @confirm="handleBuyConfirm" :cloud-stock="Number(currentProduct.warehouseStock) || 0"
            :store-stock="Number(currentProduct.sellStock) || 0" />
    </view>
</template>

<script>
import buyPopup from '@/components/buy-popup/buy-popup'
const req = require('@/utils/request.js')

export default {
    components: {
        buyPopup
    },
    data() {
        return {
            newUserList: [], // 新人商品列表
            pageNum: 1,
            pageSize: 10,
            isCartAction: true,
            currentProduct: {} // 当前选中的商品
        }
    },
    onLoad() {
        this.getNewUserList()
    },
    methods: {
        // 获取新人商品列表
        getNewUserList() {
            req.getRequest("/shopApi/shopApi/activity/list", {
                pageNum: 1,
                pageSize: 99,
                merchantId: req.getStorage("currentStore").id,
                type: 4,
            }, res => {
                this.newUserList = res.data.items
            })
        },
        // 打开购买弹窗
        openBuyPopup(item) {
            this.currentProduct = item
            this.$refs.buyPopup.open()
        },
        // 确认购买
        handleBuyConfirm({ quantity, deliveryType }) {
            req.postRequest("/shopApi/purchase/cart", {
                skuId: null,
                merchantId: req.getStorage("currentStore").id,
                quantity,
                productId: this.currentProduct.productId,
                mode: deliveryType,
                state: 0,
                activityType: 4
            }, res => {
                req.msg('加入购物车成功')
            })
        },
        // 跳转商品详情
        goToDetail(item) {
            uni.navigateTo({
                url: `/product/detail/detail?id=${item.productId}&type=4`
            })
        }
    },
}
</script>

<style lang="scss" scoped>
.new-user {
    background-image: url('https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241216/6c6e951add0f4f0d802b30e2bec4e92e.png');
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-color: #CD0A0B;

    .header {
        position: relative;
        background: linear-gradient(180deg, #FF0000 0%, #FF4D4D 100%);
        padding: 40rpx 30rpx;
        color: #fff;

        .header-bg {
            position: absolute;
            top: 0;
            right: 0;
            width: 200rpx;
            opacity: 0.1;
        }

        .header-text {
            font-size: 40rpx;
            font-weight: bold;
            margin-bottom: 10rpx;

            text {
                margin-right: 20rpx;
            }
        }

        .sub-text {
            font-size: 24rpx;
            opacity: 0.8;
        }
    }

    .goods-list {
        padding: 410rpx 20rpx 20rpx;

        .goods-item {
            background: #fff;
            border-radius: 12rpx;
            margin-bottom: 20rpx;
            position: relative;
            display: flex;
            padding: 20rpx;

            .goods-img {
                width: 160rpx;
                height: 160rpx;
                border-radius: 8rpx;
            }

            .goods-tag {
                position: absolute;
                left: 20rpx;
                top: 20rpx;
                background: #4A90E2;
                color: #fff;
                font-size: 20rpx;
                padding: 4rpx 12rpx;
                border-radius: 4rpx;
            }

            .goods-info {
                flex: 1;
                margin-left: 20rpx;
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                .goods-name {
                    font-size: 28rpx;
                    color: #333;
                    line-height: 1.4;
                    margin-bottom: 10rpx;
                }

                .goods-spec {
                    font-size: 24rpx;
                    color: #999;
                }

                .price-wrap {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-end;

                    .price {
                        color: #EA1306;

                        .symbol {
                            font-size: 24rpx;
                        }

                        .num {
                            font-size: 36rpx;
                            font-weight: bold;
                        }

                        .original {
                            font-size: 24rpx;
                            color: #999;
                            text-decoration: line-through;
                            margin-left: 10rpx;
                        }
                    }

                    .cart-btn {
                        width: 50rpx;
                    }
                }
            }
        }
    }
}
</style>