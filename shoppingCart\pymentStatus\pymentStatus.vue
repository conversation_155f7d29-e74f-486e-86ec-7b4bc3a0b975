<template>
	<view class="pymentStatus" :style="{ 'padding-top': statusBarHeight + 'px' }">
		<customTopNav :title="'支付成功'" :color="'#fff'" :homeShow="true">
		</customTopNav>
		<view class="header_">
			<view style="display: flex;justify-content: center;padding-top: 100rpx;align-items: center;">
				<i class="iconfont" style="color: #fff;font-size: 150rpx;margin-right: 8rpx;">&#xe622;</i>
				<view class="text_">
					<view>支付成功！</view>
					<view style="font-size: 28rpx;">感谢您的购买</view>
				</view>
			</view>
			<view class="infoView">
				<view class="tag"></view>
				<view class="info">
					<view class="price"><text style="font-size: 40rpx;">¥</text>{{ orderInfo.payMoney }}</view>
					<view class="msg">
						<view style="margin-top: 40rpx;">订单编号：<text style="color: #3D3D3D;">{{ orderInfo.id }}</text>
						</view>
						<view style="margin-top: 40rpx;">下单时间：<text style="color: #3D3D3D;">{{ orderInfo.createDate
								}}</text></view>
						<view style="margin-top: 40rpx;">支付方式：<text style="color: #3D3D3D;">{{ orderInfo.payState === 1
							? '余额支付' :
							orderInfo.payState === 2 ? '微信支付' :
								orderInfo.payState === 3 ? '支付宝支付' :
									orderInfo.payState === 4 ? '货到付款' :
										orderInfo.payState === 6 ? '混合支付' : '未知支付方式' }}</text></view>
					</view>
				</view>
			</view>
			<view style="display: flex;justify-content: center;">
				<view class="btn" @click="godetails">订单详情</view>
			</view>
		</view>
	</view>
</template>

<script>
const req = require('../../utils/request');
import customTopNav from "@/components/custom-top-nav/custom-top-nav" // 自定义头部
export default {
	data() {
		return {
			orderId: '',
			orderInfo: {},
			statusBarHeight: '',
		};
	},
	components: {
		customTopNav
	},
	onLoad(options) {
		this.orderId = options.orderId;
		this.getData();
		this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 50;
	},
	methods: {
		getData() {
			req.getRequest("/shopApi/mp/order/detail", {
				id: this.orderId
			}, res => {
				this.orderInfo = res.data;
			})
		},
		godetails() {
			uni.redirectTo({
				url: `/shoppingCart/orderDetails/orderDetails?id=${this.orderId}`
			});
		}
	},
}
</script>

<style lang="scss" scoped src="./pymentStatus.scss"></style>
