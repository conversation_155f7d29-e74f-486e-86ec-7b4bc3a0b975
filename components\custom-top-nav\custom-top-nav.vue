<template>
    <view class="custom-top-nav fixed"
        :style="{ 'height': height + 'px', 'padding-top': statusBarHeight + 'px', 'background-color': bgColor }">
        <view class="title" :style="{ 'color': color, 'line-height': `calc(${height}px - ${statusBarHeight}px)` }">
            <i class="iconfont" style="position: fixed;font-size: 40rpx;left: 8px;" :style="{ 'color': color }"
                v-if="backShow" @click="goBack">&#xe68b;</i>
            <i class="iconfont" style="position: fixed;font-size: 40rpx;left: 8px;" :style="{ 'color': color }"
                v-if="homeShow" @click="homeBack">&#xe68e;</i>
            {{ title }}
        </view>
    </view>
</template>
<script>
export default {
    props: {
        title: { // 列的数量 
            type: String,
            default: ""
        },
        color: {
            type: String,
            default: "#333"
        },
        bgColor: {
            type: String,
            default: "transparent"
        },
        backShow: {
            type: Boolean,
            default: false
        },
        homeShow: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            height: 0,
            statusBarHeight: 0,
            navBarHeight: 44,
        }
    },
    created() {
        this.getHeight();
    },
    methods: {
        getHeight() {
            // 获取状态栏高度
            let sysInfo = uni.getSystemInfoSync();
            this.statusBarHeight = sysInfo.statusBarHeight;
            this.height = this.statusBarHeight + this.navBarHeight;
        },
        goBack() {
            uni.navigateBack();
        },
        homeBack() {
            uni.redirectTo({
                url: '/pages/index/index'
            });
        }
    }
}
</script>
<style lang="scss" scoped>
.custom-top-nav {
    box-sizing: border-box;
    width: 750rpx;
}

.title {
    text-align: center;
}

.fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(240, 0, 0, 0.9);
    z-index: 9;
}
</style>