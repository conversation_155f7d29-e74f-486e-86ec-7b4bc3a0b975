<template>
	<view class="orderDetails">
		<view class="goods_stock">
			<view :style="orderInfo.stateName === '未付款' ? 'color :#EA1306;' : ''">{{ orderInfo.stateName }}</view>
			<view v-if="orderInfo.state == 5" style="font-size: 26rpx;color: #4E5969;font-weight: 400;">1~3日后送达</view>
			<view v-if="orderInfo.state == 4 && orderInfo.code && orderInfo.tisaneComplete" class="pickup-code-row">
				<view class="code-text">
					提货码：<text class="code-number">{{ orderInfo.code }}</text>
				</view>·
				<view class="show-barcode-btn" @click="showBarcode">
					<uni-icons type="scan" size="16" color="#EA1306"></uni-icons>
					<text class="btn-text">展示条码</text>
				</view>
			</view>
		</view>
		<uni-popup ref="barcodePopup" type="center">
			<view class="barcode-popup">
				<view class="popup-header">
					<text class="popup-title">提货码</text>
					<text class="close-icon" @click="closeBarcode">×</text>
				</view>
				<view class="barcode-content">
					<image :src="orderInfo.barcode64ForPK" mode="widthFix" class="barcode-image"></image>
					<view class="popup-code">{{ orderInfo.code }}</view>
					<view class="popup-tip">请出示条码给工作人员扫描</view>
				</view>
			</view>
		</uni-popup>
		<view class="addGoods" v-if="orderInfo.state == 3 && false">
			<view>
				<view>追加商品合并配送</view>
				<view style="color: #4E5969;font-weight: 400;font-size: 26rpx;">未拣货前，可免配送费追加商品合并配送</view>
			</view>
			<view class="add_" @click="addGoods">追加商品</view>
		</view>
		<view @click="goUrl('/mine/logistics/logistics?orderId=' + orderInfo.id)"
			v-if="orderInfo.trackList && orderInfo.trackList.length > 0" class="logistics">
			<text class="iconfont logistics-icon">&#xe693;</text>
			<view class="logistics-content">
				<view class="logistics-text">{{ orderInfo.trackList[0].nowValue }}</view>
				<view class="logistics-time">{{ orderInfo.trackList[0].pushTime }}</view>
			</view>
			<text class="iconfont arrow-icon">&#xe667;</text>
		</view>
		<view class="delivery_info">
			<uni-section title="配送信息" type="line">
				<template v-slot:right
					v-if="orderInfo.mode != 1 && (orderInfo.state == 1 || orderInfo.state == 2 || orderInfo.state == 3) && orderInfo.addressState != 1">
					<view class="right_" @click="goAddress">修改</view>
				</template>
				<view class="info_">
					<view style="display: flex;margin-bottom: 20rpx;" v-if="orderInfo.address">
						<i class="iconfont" style="font-size: 28rpx;margin-top: 2rpx;margin-right: 8rpx;">&#xe620;</i>
						<view>
							<view v-if="orderInfo.mode != 1" style="color: #333333;font-size: 32rpx;font-weight: 500;">
								{{	orderInfo.address||'' }}
							</view>
							<view>{{ orderInfo.name||'' }} {{ orderInfo.phone ||''}}</view>
						</view>
					</view>
					<view v-if="orderInfo.mode != 2">
						<view style="display: flex;margin-bottom: 10rpx;">
							<i class="iconfont"
								style="font-size: 32rpx;margin-top: 2rpx;margin-right: 8rpx;">&#xe620;</i>
							<view>
								<view style="display: flex;">
									<view style="color: #333333;font-size: 32rpx;" class="single-line-text">{{
										orderInfo.merchantName }}-</view>
									<view style="text-decoration: underline;font-size: 30rpx;color: #333333;"
										@click="makePhoneCall(orderInfo.merchantPhone)">
										{{ orderInfo.merchantPhone }}</view>
								</view>
								<!-- <view>{{ orderInfo.name }} {{ orderInfo.phone }}</view> -->
							</view>
						</view>

					</view>
					<!-- <view style="display: flex;margin-bottom: 20rpx;">
						<i class="iconfont" style="font-size: 32rpx;margin-top: 2rpx;margin-right: 8rpx;">&#xe627;</i>
						<view>{{ orderInfo.appointTime || '尽快送达' }}</view>
					</view> -->
					<view style="display: flex;margin-bottom: 10rpx;">
						<i class="iconfont" style="font-size: 28rpx;margin-top: 2rpx;margin-right: 8rpx;">&#xe61d;</i>
						<view>{{ orderInfo.remarks || '暂无备注' }}</view>
					</view>
				</view>
			</uni-section>
		</view>
		<view class="goods_info">
			<!-- orderInfo.products.length -->
			<uni-section :title="`商品信息(共${orderInfo.products.length}件)`" type="line">
				<view style="padding: 0rpx 16rpx 40rpx;">
					<view class="goods_">
						<view @click="goToDetail(item)" class="goods_item" v-for="(item, index) in orderInfo.products"
							:key="index">
							<view class="goods_img">
								<image :src="item.pic" style="width: 100%;" mode="widthFix"></image>
							</view>
							<view class="goods_info">
								<view style="margin-bottom: 16rpx;" class="single-line-text">{{ item.title }}</view>
								<view style="margin-bottom: 16rpx;">单价：¥{{ item.salePrice }}</view>
								<view style="margin-bottom: 16rpx;">数量：{{ item.quantity }}</view>
							</view>
							<view class="pay_">实付:¥{{ Number(item.salePrice * item.quantity).toFixed(2) }}</view>
						</view>
					</view>
					<view class="goods_details">
						<view style="display: flex;justify-content: space-between;margin: 20rpx 10rpx;">
							<view>商品金额</view>
							<view style="font-weight: 600;font-size: 32rpx;">¥{{ orderInfo.money }}</view>
						</view>
						<view style="display: flex;justify-content: space-between;margin: 0rpx 10rpx 20rpx;"
							v-if="orderInfo.mode !== 1">
							<view>
								<view>配送费</view>
								<view style="line-height: 30rpx;">基础配送货费¥{{ orderInfo.expressFee }}</view>
							</view>
							<view style="font-weight: 600;">¥{{ orderInfo.expressFee }}</view>
						</view>
						<view v-if="orderInfo.dose"
							style="display: flex;justify-content: space-between;margin: 0rpx 10rpx 20rpx;">
							<view>
								<view>是否代煎</view>
							</view>
							<view style="font-weight: 600;">{{ orderInfo.isTisane ? '是' : '否' }}</view>
						</view>
						<view v-if="orderInfo.isTisane"
							style="display: flex;justify-content: space-between;margin: 0rpx 10rpx 20rpx;">
							<view>
								<view>代煎费用</view>
							</view>
							<view style="font-weight: 600;">¥{{ orderInfo.tisaneFee }}</view>
						</view>
						<view v-if="orderInfo.tisaneTime"
							style="display: flex;justify-content: space-between;margin: 0rpx 10rpx 20rpx;">
							<view>
								<view>接单时间</view>
							</view>
							<view style="font-weight: 600;">{{ orderInfo.tisaneTime }}</view>
						</view>
						<view v-if="orderInfo.dose"
							style="display: flex;justify-content: space-between;margin: 0rpx 10rpx 20rpx;">
							<view>
								<view>剂量</view>
							</view>
							<view style="font-weight: 600;">X{{ orderInfo.dose }}</view>
						</view>
					</view>
					<view
						style="display: flex;justify-content: flex-end;border-top: 1px solid #F2F3F5;padding-top: 20rpx;">
						<view>实付:<text style="color: #EA1306;font-weight: 600;">¥<text
									style="font-size: 40rpx;font-weight: 600;">{{
										orderInfo.payMoney
									}}</text></text>
						</view>
					</view>
				</view>
			</uni-section>
		</view>
		<view class="order_info">
			<uni-section title="订单信息" type="line">
				<view class="info_">
					<view style="display: flex;justify-content: space-between;margin-bottom: 20rpx;">
						<view>订单号</view>
						<view style="display: flex;color: #333333;">
							{{ orderInfo.id }}<view class="btn_" @click="copyOrderId">复制</view>
						</view>
					</view>
					<view style="display: flex;justify-content: space-between;margin-bottom: 20rpx;">
						<view>下单时间</view>
						<view style="font-weight: 600;color: #333333;">
							{{ orderInfo.createDate }}
						</view>
					</view>
					<view style="display: flex;justify-content: space-between;margin-bottom: 20rpx;">
						<view>支付方式</view>
						<view style="color: #333333;">
							{{ orderInfo.payState === 1 ? '余额支付' :
								orderInfo.payState === 2 ? '微信支付' :
									orderInfo.payState === 3 ? '支付宝支付' :
										orderInfo.payState === 4 ? '货到付款' :
											orderInfo.payState === 6 ? '混合支付' :
												orderInfo.payState === 7 ? '个账支付' : '未知支付方式' }}
						</view>
					</view>
				</view>
			</uni-section>
		</view>
		<!-- 为你推荐 -->
		<view class="Recommended" style=" padding: 60rpx 0 0 0;">
			<view style="text-align: center;margin-bottom: 20rpx;">
				<image
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241116/b7074417575a42eaa8d82c22d3c8ba4b.png"
					style="width: 232rpx;height: 48rpx;">
				</image>
			</view>
			<customWaterfallsFlow ref="waterfallsFlowRef" :value="RecommendedList">
				<view v-for="(item, index) in RecommendedList" :key="index" slot="slot{{index}}">
					<goods-card :data="item" :refresh="true"></goods-card>
				</view>
			</customWaterfallsFlow>
		</view>
		<view v-if="orderInfo.id" class="bottom-btns"
			style="position: fixed;bottom: 0;left: 0;right: 0;background: #fff;padding: 20rpx;padding-bottom: calc(20rpx + env(safe-area-inset-bottom));display: flex;justify-content: flex-end;box-shadow: 0 -2rpx 4rpx rgba(0,0,0,0.1);">
			<view v-if="orderInfo.state == 1 || orderInfo.state == 20" class="btn2" @click="cancelOrder(orderInfo.id)">
				取消订单
			</view>
			<view v-else-if="orderInfo.state == 8" class="btn2" @click="deleteOrder(orderInfo.id)">
				删除订单
			</view>

			<view v-if="orderInfo.payState != 7">
				<view v-if="orderInfo.state == 30 || orderInfo.state == 41 || orderInfo.state == 43" class="btn2"
					@click="goUrl(`/mine/afterSaleDetail/afterSaleDetail?id=${orderInfo.refundId}`)">售后详情</view>
				<view v-else-if="orderInfo.state != 1 && orderInfo.state != 8 && orderInfo.state != 20" class="btn2"
					@click="goUrl(`/mine/afterSale/afterSale?orderId=${orderInfo.id}`)">申请售后
				</view>
			</view>
			<view class="btn"
				@click="goUrl(`/shoppingCart/tcConsultation/tcConsultation?orderId=${orderInfo.id}&payState=${orderInfo.payState}`)"
				v-if="orderInfo.state == 20">去开方</view>
			<view v-else-if="orderInfo.payState == 7 && orderInfo.state != 8" class="btn2" @click="goMedicalOrder">
				支付详情
			</view>
			<view class="btn" style="padding: 15rpx 30rpx;border-radius: 30rpx;background: #EA1306;color: #fff;"
				@click="payOrder(orderInfo)" v-else-if="orderInfo.state == 1">
				立即付款
			</view>
			<view class="btn2" @click="oneMoreOrder(orderInfo)" v-if="orderInfo.state == 7">
				再来一单
			</view>
			<view class="btn2" @click="confirmOrder(orderInfo.id)" v-if="orderInfo.state == 5 || orderInfo.state == 6">
				确认收货
			</view>
			<!-- <view class="btn2" @click="confirmOrder(orderInfo.id)" v-if="orderInfo.state == 3">
				提醒发货
			</view> -->
		</view>
		<view
            style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top:200rpx;"
            v-else>
            <image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241116/a1a17cdb255740e7a69beb4e5b1b40da.png"
                mode="widthFix" style="width:50%;"></image>
            <view style="color:#4E5969;margin-top:20rpx;">暂无订单~</view>
        </view>
        
        <uni-popup ref="showMerchantDialog" :mask-click="false">
				<view class="popup-content">
					<view class="popup-title">配送方式</view>
					<view class="delivery-options">
						<view class="delivery-item" :class="{ active: mode === 1 }" @tap="selectmode(1)">
							<view class="radio-wrap">
								<view class="radio-inner"></view>
							</view>
							<text>到店自提</text>
						</view>
						<view class="delivery-item" :class="{ active: mode === 2 }" @tap="selectmode(2)">
							<view class="radio-wrap">
								<view class="radio-inner"></view>
							</view>
							<text>快递配送</text>
						</view>
					</view>
					<!-- 自提信息 -->
					<view class="delivery-info" v-if="mode === 1">
						<view class="info-item">
							<view class="label" @click="$refs.merchantpopup.open()">自提点：{{ selectmerchant.storeName ? selectmerchant.storeName : '请选择门店' }}</view>
						</view>
						<view class="area">
							<!-- <image src="/static/dzico.png" class="dzico"></image> -->
							<text class="address">{{ selectmerchant.address }}</text>
						</view>
					</view>
					<!-- 快递信息 -->
					<view class="delivery-info" v-else>
						<view class="area" @click="goUrl('/mine/addressList/addressList?choice=true')"
							style="margin-bottom: 30rpx;">
							<!-- <image src="/static/dzico.png" class="dzico"></image> -->
							<view class="flex" v-if="address.address || address.house">
								<view class="name">{{ address.address }}{{ address.house }}</view>
								<view class="text" v-if="address.name">{{ address.name }}<text>{{ address.phone
								}}</text></view>
							</view>
							<view class="flex" v-else>请添加收货地址</view>
							<!-- <i class="iconfont">&#xe642;</i> -->
						</view>
						<view class="area">
							<!-- <image src="/static/dzico.png" class="dzico"></image> -->
							<view class="flex">
								<view class="name">
									{{ mode == 3 ? '配送点：' : '服务点：' }}{{ selectmerchant.storeName ||''}}
								</view>
								<view class="text">{{ selectmerchant.address }}</view>
							</view>
						</view>
					</view>
					<view class="popup-buttons">
						<view class="confirm-btn" @tap="confirmDelivery">确定</view>
					</view>
				</view>
			</uni-popup>
            <uni-popup ref="merchantpopup" class="merchant-dialog" type="bottom" background-color="#fff" 
             border-radius="20rpx 20rpx 0 0">
            <view class="dialog-content">
                <view class="dialog-title">请选择门店</view>
                <scroll-view scroll-y class="scroll-box">
                    <view v-for="merchant in merchantList" :key="merchant.id" class="merchant-item" @click="selectMerchantAndPay(merchant)">
                    {{ merchant.storeName }}
                    </view>
                </scroll-view>
            </view>
        </uni-popup>
	</view>
</template>

<script>
import customWaterfallsFlow from "@/components/custom-waterfalls-flow/custom-waterfalls-flow" //瀑布流
import goodsCard from "@/components/goods-card/goods-card" //商品卡片
const req = require('../../utils/request')
const app = getApp()
export default {
	data() {
		return {
			orderId: '',
			orderInfo: {
				products: []
			},
			RecommendedList: [],  // 推荐列表
			pageNum: 1,
			pageSize: 10,
			address: {},
			showupAddress: false,
			getCode: false,
			merchantList: [],
            payOrderItem: {},
            mode: 1,
            selectmerchant: '',
            address: {},
		};
	},
	onLoad(options) {
		if (options.id) {
			this.orderId = options.id
		}
	},
	onShow() {
		if (app.globalData.authCode && this.getCode) {
			this.authCode = app.globalData.authCode
			this.getCode = false
			req.showLoading()
			req.postRequest("/shopApi/medical/order/accountPayment", {
				orderId: this.orderId,
				qrcode: this.authCode
			}, res => {
				uni.hideLoading()
				uni.redirectTo({
					url: '/shoppingCart/medicalOrder/medicalOrder?orderId=' + this.orderId
				})
			})
		}
		this.getOrderDetail()
		this.getRecommendedList();
		if (this.showupAddress) {
			uni.showModal({
				title: '提示',
				content: '确定要修改配送地址吗?',
				success: (res) => {
					console.log(res, "修改地址");
					if (res.confirm) {
						this.upOrderAddress();
						this.orderInfo.addressState = 1;
					} else {
						this.address = this.orderInfo.userAddresses;
					}
				}
			})
		}
	},
	onReachBottom() {
		this.pageNum++;
		this.getRecommendedList();
	},
	components: {
		customWaterfallsFlow,
		goodsCard
	},
	methods: {
		// 选择配送方式
		selectmode(type) {
            this.mode = type;
            this.selectmerchant = '';
            this.getstore();
            if (type == 2) {
                // 获取收获地址
				req.getRequest("/shopApi/mp/address/list", {
					page: 1,
					limit: 20
				}, res => {
					console.log(res, "获取收获列表");
					if (res.data.list.length) {
						this.address = res.data.list[0];
                    }
                    // console.log(this.address);
				})
			};
		},
		goMedicalOrder() {
			if (this.orderInfo.payState == 7 && this.orderInfo.hisStatus == 0) {
				uni.navigateToMiniProgram({
					appId: 'wxe183cd55df4b4369',
					path: `auth/pages/bindcard/auth/index?openType=getAuthCode&bizType=04107&cityCode=440108&channel=30000175&orgChnlCrtfCodg=BqK1kMStlhVDgN2uHf4EsLK/F2LjZPYJ81nK2eYQqxuHgpiS+wkhUBWGPoaYCwz0&orgCodg=P44010600664&orgAppId=1I5K5N9JE1T84460C80A00000ED5F971`,
					envVersion: req.env.NODE_ENV == 'product' ? 'release' : 'trial',
					success: res => {
						this.getCode = true
					}
				})
			} else {
				uni.navigateTo({
					url: `/shoppingCart/medicalOrder/medicalOrder?orderId=${this.orderInfo.id}`
				})
			}
		},
		goToDetail(item) {
			if (!item || !item.id) {
				req.msg('商品信息不完整')
				return
			}
			let url = `/product/detail/detail?id=${item.id}`
			if (item.type) {
				url += `&type=${item.type}`
			}
			uni.navigateTo({
				url: url
			})
		},
		goUrl(url) {
			uni.navigateTo({
				url: url
			})
		},
		closeBarcode() {
			this.$refs.barcodePopup.close()
		},
		cancelOrder(id) {
			uni.showModal({
				title: '提示',
				content: '确定要取消该订单吗?',
				success: (res) => {
					if (res.confirm) {
						req.postRequest("/shopApi/mp/order/cancel/" + id, {}, res => {
							if (res.code == 200) {
								req.msg('取消成功')
								setTimeout(() => {
									// 获取上一页实例
									const prevPage = getCurrentPages()[getCurrentPages().length - 2];
									// 设置上一页的刷新标记
									if (prevPage) {
										prevPage.$vm.page = 1;
										prevPage.$vm.hasMore = true;
										if (prevPage.$vm.getData) {
											prevPage.$vm.getData();
										}
									}
									// 返回上一页
									uni.navigateBack();
								}, 1500);
							}
						})
					}
				}
			})
		},
		payOrder(item) {
			if (!item.merchantId) {
                // 没有选择门店，弹窗选择
                this.payOrderItem = item;
                this.$refs.showMerchantDialog.open();
                this.getstore();
                return;
            }
            // 有门店，直接付款
			req.payOrder(item.id, res => {
				this.payOrderItem = {};
				if (item.mode == 3) {
					req.getRequest("/shopApi/create/order/createByShop", {
						id: item.id
					}, res => {
						if (res.code == 200) {
							this.page = 1
							this.hasMore = true
							this.getOrderDetail()
						}
					})
				} else {
					this.page = 1
					this.hasMore = true
					this.getOrderDetail()
				}
			})
		},
		// 获取门店
        getstore(){
            req.getRequest('/shopApi/mp/order/getStoreList', {
                orderId:this.payOrderItem.id,
                mode: this.mode,
            }, res => {
                console.log(res,'门店列表');
                this.merchantList = res.data;
                if (this.mode==2) {
                    this.selectmerchant = this.merchantList[0];
                }
            });
        },
		getOrderDetail() {
			req.getRequest("/shopApi/mp/order/detail", {
				id: this.orderId
			}, res => {
				this.orderInfo = res.data;
				this.address = this.orderInfo.userAddresses;
			})
		},
		// 确认收货
		confirmOrder(id) {
			req.showLoading()
			req.msgConfirm('是否确认收货？', () => {
				req.postRequest1(`/shopApi/mp/order/receiving/${id}`, {}, res => {
					uni.hideLoading();
					console.log(res, "确认收货");
					req.msg("确认收货成功");
					uni.navigateBack();
				})
			});
		},
		deleteOrder(id) {
			req.showLoading()
			req.msgConfirm('确定要删除该订单吗？', () => {
				req.postRequest1('/shopApi/mp/order/delete', {
					id: id
				}, res => {
					uni.hideLoading();
					req.msg("删除订单成功");
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				})
			});
		},
		copyOrderId() {
			uni.setClipboardData({
				data: this.orderInfo.id.toString(),
				success: () => {
					uni.showToast({
						title: '复制成功'
					})
				}
			});
		},
		// 再来一单
		oneMoreOrder(item) {
			console.log(item);
			req.postRequest1(`/shopApi/mp/order/tryAgain/${item.id}`, {}, res => {
				console.log(res, "再来一单");
				if (res.data) {
					let mode_ = item.mode == 3 || item.mode == 1 ? 1 : 2
					uni.navigateTo({
						url: `/shoppingCart/confirmOrder/confirmOrder?prescriptionDrug=${true}&mode=${mode_}&tabMode=${item.mode}&goodslist=${res.data}&merchantId=${item.merchantId}&discountType=1&toform=1`
					});
				}
			});
		},
		// 修改配送地址
		upOrderAddress() {
			req.postRequest("/shopApi/mp/order/upOrderAddress/", {
				id: this.orderInfo.id,
				addressId: this.address.id
			}, res => {
				console.log(res, "修改配送地址");
				req.msg("修改成功");
			});
		},
		// 获取为你推荐列表
		getRecommendedList() {
			req.getRequest("/shopApi/home/<USER>", {
				storeId: req.getStorage("currentStore").id,
				pageNum: this.pageNum,
				pageSize: this.pageSize
			}, res => {
				console.log(res, "为你推荐列表");
				res.data.list.forEach(item => {
					item.image = item.img
				})
				this.$nextTick(() => {
					this.RecommendedList = [...this.RecommendedList, ...res.data.list]
				})
			})
		},
		showBarcode() {
			this.$refs.barcodePopup.open()
		},
		// 追加商品
		addGoods() {
			uni.switchTab({
				url: '/pages/index/index'
			});
		},
		// 收货地址
		goAddress() {
			uni.navigateTo({
				url: '/mine/addressList/addressList?choice=true&updateAddress=true'
			});
		},
		// 拨打电话
		makePhoneCall(phone) {
			uni.makePhoneCall({
				phoneNumber: phone //仅为示例
			});
		},
        selectMerchantAndPay(merchantId) {
            this.$refs.merchantpopup.close();
            this.selectmerchant = merchantId;
        },
        confirmDelivery() {
            console.log(this.mode,this.address.id,  this.payOrderItem.id ,'hhhh');
            req.postRequest('/shopApi/mp/order/updateOrderStore', {
                id: this.payOrderItem.id,
                merchantId: this.selectmerchant.id,
                mode: this.mode,
                addressId:this.mode===2?this.address.id:''
            }, res => {
                this.payOrderItem.merchantId = this.selectmerchant.id;
                this.$refs.showMerchantDialog.close();
                this.payOrder(this.payOrderItem);
            });
        }
	}
}
</script>

<style lang="scss" scoped src="./orderDetails.scss"></style>

<style lang="scss" scoped>
.pickup-code-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 32rpx;
	color: #4E5969;
	font-weight: 400;
	margin-top: 16rpx;

	.code-text {
		.code-number {
			color: #EA1306;
			font-weight: 500;
		}
	}

	.show-barcode-btn {
		display: flex;
		align-items: center;
		padding: 12rpx 24rpx;
		background: rgba(234, 19, 6, 0.1);
		border-radius: 32rpx;

		.btn-text {
			color: #EA1306;
			font-size: 26rpx;
			margin-left: 8rpx;
		}
	}
}

.barcode-popup {
	background-color: #fff;
	padding: 40rpx;
	border-radius: 24rpx;
	width: 600rpx;

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 40rpx;

		.popup-title {
			font-size: 34rpx;
			font-weight: 600;
			color: #333;
		}

		.close-icon {
			font-size: 48rpx;
			color: #999;
			padding: 10rpx;
			line-height: 1;
		}
	}

	.barcode-content {
		display: flex;
		flex-direction: column;
		align-items: center;

		.barcode-image {
			width: 480rpx;
			margin: 20rpx 0 40rpx;
		}

		.popup-code {
			font-size: 56rpx;
			color: #333;
			font-weight: bold;
			margin-bottom: 20rpx;
		}

		.popup-tip {
			font-size: 26rpx;
			color: #666;
			margin-top: 20rpx;
		}
	}
}
</style>
