<template>
    <view class="card-replace">
        <!-- 顶部日期选择和筛选 -->
        <view class="header">
            <uni-datetime-picker v-model="date" type="daterange" @change="dateChange" />
            <uni-data-select v-model="state" :clear="false" :localdata="stateList"
                @change="stateChange"></uni-data-select>
            <view @click=" $refs.popup.open()" class="add-btn">
                <text style="font-size: 28rpx;">新增</text>
            </view>
        </view>

        <!-- 申请列表 -->
        <view class="application-list">
            <view class="application-item" v-for="(item, index) in applications" :key="index">
                <view v-if="item.state == 0" class="status-tag" style="background: #ff6b35;">审批中</view>
                <view v-if="item.state == 1" class="status-tag" style="background: #67c23a;">已通过</view>
                <view v-if="item.state == 2 || item.state == 3" class="status-tag" style="background: #f23030;">已拒绝
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe62b;</text>
                    <text class="label">所属门店：</text>
                    <text class="value">{{ item.divId }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe62b;</text>
                    <text class="label">申请人：</text>
                    <text class="value">{{ item.applyUser }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe77c;</text>
                    <text class="label">申请时间：</text>
                    <text class="value">{{ item.applyTime }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe77c;</text>
                    <text class="label">加班时间：</text>
                    <text class="value">{{ item.startTime }}至{{ item.endTime }}
                        <text v-if="item.startTime && item.endTime" style="color: #67c23a;">
                            ({{ calculateDuration(item.startTime, item.endTime) }}小时)
                        </text>
                    </text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                    <text class="label">加班原因：</text>
                    <text class="value">{{ item.reason }}</text>
                </view>
                <!-- <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                    <text class="label">审批人：</text>
                    <text class="value">{{ item.reason }}</text>
                </view> -->
                <view class="info-row" v-if="item.state == 0">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                    <text class="label">审批进度：</text>
                    <view @click="open(item)"
                        style="background-color: #ea1306;color: #fff;padding: 10rpx 20rpx;border-radius: 12rpx;">
                        查看审批进度</view>
                </view>
            </view>
            <view v-if="applications.length === 0" style="text-align: center;color: #999;">
                <image mode=""
                    src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png"
                    style="width: 372rpx;height: 312rpx;margin-top: 50rpx;">
                </image>
                <view style="margin-top: 10rpx;font-size: 28rpx;">暂无数据</view>
            </view>
        </view>
        <uni-popup :is-mask-click="false" ref="popup" type="bottom" border-radius="10px 10px 0 0">
            <view class="popup-content">
                <view class="popup-header">
                    <text>加班申请</text>
                    <text class="close-icon" @click="$refs.popup.close()">×</text>
                </view>
                <view class="popup-body">
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">申请人：</text>
                        <input type="text" disabled v-model="formData.name" class="input"
                            style="background: #f5f5f5;" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">所属门店：</text>
                        <input type="text" disabled v-model="formData.storeId" class="input"
                            style="background: #f5f5f5;" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">岗位：</text>
                        <input type="text" disabled v-model="formData.postName" class="input"
                            style="background: #f5f5f5;" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">加班时间：</text>
                        <uni-datetime-picker v-model="formData.date" type="datetimerange">
                            <view class="date-input">
                                <view
                                    style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
                                    <text class="iconfont"
                                        style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
                                </view>
                                <text>{{ formData.date[0] || '请选择' }}-{{ formData.date[1] || '请选择' }}</text>
                            </view>
                        </uni-datetime-picker>
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">部门领导：</text>
                        <uni-data-select v-model="value" :localdata="range" @change="leaderChange"
                            class="selectleader"></uni-data-select>
                        <!-- <input type="text" v-model="leaderInputValue" placeholder="请输入审批人" class="input"
                            @input="leaderChange" />
                        <view class="leader-list" v-if="leaderList.length > 0">
                            <view class="leader-item" v-for="(item, index) in leaderList" :key="index"
                                @click="selectLeader(item)">
                                {{ item.name }}
                            </view>
                        </view> -->
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">加班原因：</text>
                        <textarea v-model="formData.reason" placeholder="请输入加班原因" class="textarea" />
                    </view>

                    <view class="form-footer">
                        <view class="btn save" @click="saveForm">提交</view>
                        <view class="btn cancel" @click="$refs.popup.close()">取消</view>
                    </view>
                </view>
            </view>
        </uni-popup>
        <uni-popup :is-mask-click="false" ref="approve" type="bottom" border-radius="10px 10px 0 0">
            <view class="popup-content1">
                <view class="popup-header">
                    <text>审批进度</text>
                    <text class="close-icon" @click="$refs.approve.close()">×</text>
                </view>
                <view class="popup-body">
                    <view class="step-container">
                        <view class="step" v-for="(item, index) in stepList" :key="index">
                            <view class="step-number"></view>
                            <view class="step-title">
                                <view>开始时间:{{ item.startTime }}</view>
                                <view>{{ item.assignee }}：{{ item.taskName }}</view>
                                <view v-if="item.comment">审批意见：{{ item.comment }}</view>
                                <view v-if="item.endTime">结束时间:{{ item.endTime }}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const req = require("../../utils/request");

export default {
    data() {
        return {
            date: [],
            state: 4,
            leaderInputValue: '',
            stateList: [
                { value: 4, text: "全部" },
                { value: 0, text: "审核中" },
                { value: 1, text: "已通过" },
                { value: 2, text: "已拒绝" },
            ],
            applications: [],
            formData: {
                name: req.getStorage('userInfo').staffName,
                storeId: req.getStorage('userInfo').divName,
                postName: req.getStorage('userInfo').postName,
                date: "",
                reason: '',
                leader: '',
            },
            pageNum: 1,
            total: 0,
            timer: null,
            leaderList: [],
            is_headquarters: '',   // 是否总部人
            stepList: [],
            value: 0,
            range: [],
        }
    },
    onLoad() {
        this.getData();
        this.getBusiness();
    },
    onReachBottom() {
        this.pageNum++
        this.getData()
    },
    methods: {
        open(item) {
            req.showLoading();
            req.getRequest('/shopApi/translate/getDetails', {
                id: item.id
            }, res => {
                let data = [...res.data];
                let list = data.filter(item => item.processDefinitionId.includes('overtimeApp'));
                let taskId = list.length > 0 ? list[0].taskId : ""
                req.getRequest(`/shopApi/translate/history/${taskId}`, {}, res => {
                    console.log(res);
                    this.stepList = res;
                    uni.hideLoading();
                    this.$refs.approve.open();
                })
            })
        },
        getBusiness() {
            req.getRequest('/shopApi/goOut/business', {
                name: req.getStorage('userInfo').staffName
            }, res => {
                this.is_headquarters = res.data;
                this.getReviewer();
            });
        },
        getReviewer() {
            req.getRequest('/shopApi/translate/getReviewer', {
                uid: req.getStorage('userInfo').introductionId,
                node: 'addleave',
                isHeadquarters: this.is_headquarters
            }, res => {
                if (res.data.length != 0) {
                    this.range = res.data.map(item => ({
                        value: item.id,
                        text: item.name
                    }))
                }
            });
        },
        leaderChange(e) {
            this.formData.leader = e ? this.range.find(item => item.value === e).text : "";
            // const value = e.detail.value.trim();
            // this.leaderInputValue = value;
            // this.formData.leader = ""
            // if (this.timer) {
            //     clearTimeout(this.timer);
            //     this.timer = null;
            // }

            // if (!value) {
            //     this.leaderList = [];
            //     return;
            // }

            // this.timer = setTimeout(() => {
            //     let params = {
            //         name: value
            //     };
            //     let url = this.is_headquarters == 1 ? "/shopApi/replacement/getStaff" : "/shopApi/goOut/getManager";
            //     if (this.is_headquarters != 1) {
            //         params.uid = req.getStorage('userInfo').introductionId;
            //     }
            //     req.getRequest(url, params, res => {
            //         this.leaderList = res.data.items;
            //     });
            // }, 500);
        },
        // selectLeader(item) {
        //     this.formData.leader = item.name;
        //     this.leaderInputValue = item.name;
        //     this.leaderList = [];
        // },
        stateChange(e) {
            this.state = e
            this.pageNum = 1
            this.getData()
        },
        dateChange(e) {
            this.date = e
            this.pageNum = 1
            this.$nextTick(() => {
                this.getData();
            })
        },
        getData() {
            req.showLoading();
            req.getRequest("/shopApi/overtime/list", {
                pageNum: this.pageNum,
                pageSize: 10,
                state: this.state == 4 ? null : this.state,
                startTime: this.date[0],
                endTime: this.date[1],
                staffId: req.getStorage('userInfo').introductionId,
                applyUser: req.getStorage('userInfo').staffName,
            }, res => {
                uni.hideLoading()
                this.total = res.total
                if (this.pageNum === 1) {
                    this.applications = res.data.items
                } else {
                    this.applications = [...this.applications, ...res.data.items]
                }
            })
        },
        saveForm() {
            if (!this.formData.name) {
                req.msg('请输入申请人')
                return;
            }
            if (!this.formData.date) {
                req.msg('请选择加班时间')
                return
            }
            if (!this.formData.leader) {
                req.msg('请选择部门领导')
                return;
            }
            if (!this.formData.reason) {
                req.msg('请输入加班原因')
                return
            }
            // TODO: 调用接口保存数据
            req.showLoading()
            req.postRequest("/shopApi/overtime/add", {
                applyUser: req.getStorage('userInfo').staffName,
                post: req.getStorage('userInfo').postId,
                divId: req.getStorage('userInfo').divId,
                startTime: this.formData.date[0],
                endTime: this.formData.date[1],
                reason: this.formData.reason,
                deptleader: this.formData.leader,
            }, res => {
                uni.hideLoading()
                req.msg('提交成功')
                this.pageNum = 1
                this.formData = {
                    name: req.getStorage('userInfo').staffName,
                    storeId: req.getStorage('userInfo').divName,
                    postName: req.getStorage('userInfo').postName,
                    date: "",
                    reason: '',
                    leader: '',
                }
                this.getData()
                this.$refs.popup.close()
            })
        },
        calculateDuration(startTime, endTime) {
            if (!startTime || !endTime) return '';
            const start = new Date(startTime);
            const end = new Date(endTime);
            const diff = end - start;
            const hours = diff / (1000 * 60 * 60);
            return hours.toFixed(1);
        },
    }
}
</script>
<style lang="scss" scoped src="./applicationWork.scss"></style>
