/deep/ .header .uni-date__x-input,
/deep/ .header .uni-select__selector-empty,
/deep/ .header .uni-select__selector-item {
  font-size: 30rpx !important;
}

.tabs-box {
  width: 100%;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;

  .scroll-view {
    width: 100%;
    white-space: nowrap;

    // 隐藏滚动条
    ::-webkit-scrollbar {
      display: none;
      width: 0;
      height: 0;
      color: transparent;
    }

    .tabs {
      display: flex;
      width: 100%;

      .tab-item {
        width: 50%;
        padding: 20rpx 25rpx;
        position: relative;
        text-align: center;
        transition: all 0.3s ease;

        &::after {
          content: "";
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-50%);
          width: 0;
          height: 4rpx;
          background-color: #f00;
          border-radius: 2rpx;
          transition: all 0.3s ease;
          opacity: 0;
        }

        &.active {
          color: #f00;
          font-weight: bold;

          &::after {
            width: 40rpx;
            opacity: 1;
          }
        }
      }
    }
  }
}

.card-replace {
  min-height: 100vh;
  background-color: #f5f7fa;

  .header {
    background: #fff;
    // margin: 0 20rpx;
    padding: 20rpx 15rpx;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 20rpx;
    position: relative;

    .add-btn {
      background: #fef0f0;
      color: #f00;
      padding: 8px 16px;
      border-radius: 4px;
    }

    .batch-approval {
      background: #f00;
      color: #fff;
      height: 70rpx;
      line-height: 70rpx;
      text-align: center;
      border-radius: 8rpx;
      width: 150rpx;
      font-size: 28rpx;
    }
  }

  .application-list {
    padding: 15rpx 30rpx;

    .application-item {
      background: #fff;
      border-radius: 8px;
      padding: 28rpx 15rpx 20rpx;
      margin-bottom: 15rpx;
      position: relative;
      display: flex;
      align-items: center;

      .checkbox-box {
        .iconfont {
          padding: 0 20rpx 0 10rpx;
        }

        .active {
          color: #f00;
        }
      }

      .status-tag {
        position: absolute;
        top: 0;
        left: 0;
        padding: 4px 12px;
        color: #fff;
        font-size: 14px;
        border-radius: 8px 0 8px 0;
      }

      .info-row {
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        width: 100%;
        .label {
          color: #333;
          font-size: 14px;
          width: 150rpx;
          text-align: justify;
          text-align-last: justify;
        }

        .value {
          color: #4e5969;
          font-size: 14px;
          flex: 1;
        }
      }
    }
  }

  .batch-approval-box {
    background: #f00;
    width: 560rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    border-radius: 40rpx;
    font-size: 32rpx;
    color: #fff;
    position: fixed;
    bottom: 100rpx;
    left: 50%;
    transform: translateX(-50%);
  }
}

.popup-content {
  background: #fff;
  border-radius: 10px 10px 0 0;

  .popup-header {
    position: relative;
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid #eee;

    text {
      font-size: 16px;
      font-weight: 500;
    }

    .close-icon {
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 24px;
      color: #999;
      padding: 5px;
    }
  }

  .popup-body {
    padding: 20px;

    .form-item {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      position: relative;

      .required {
        color: #f23030;
        margin-right: 5px;
      }

      .label {
        width: 80px;
        font-size: 14px;
        color: #333;
        margin-right: 10px;
      }

      .input,
      .textarea,
      .date-input,
      .select-input {
        flex: 1;
        border: 1px solid red;
        border-radius: 4px;
        font-size: 14px;
        color: #333;
        background: #fff;
      }

      .input {
        height: 40px;
        padding: 0 12px;
      }

      .textarea {
        height: 80px;
        padding: 10px 12px;
      }

      .date-input {
        height: 40px;
        padding-right: 12px;
        display: flex;
        align-items: center;
        // justify-content: space-between;

        .calendar-icon,
        .arrow-down {
          color: #999;
          font-size: 14px;
        }
      }

      .leader-list {
        position: absolute;
        top: 42px;
        left: 95px;
        right: 0;
        max-height: 300rpx;
        background: #fff;
        border: 1px solid #eee;
        border-radius: 4px;
        overflow-y: auto;
        z-index: 999;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

        .leader-item {
          padding: 20rpx;
          border-bottom: 1px solid #eee;
          cursor: pointer;

          &:hover {
            background: #f5f5f5;
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }

    .form-footer {
      display: flex;
      justify-content: space-between;
      gap: 20rpx;
      margin-top: 60rpx;
      padding: 0 30rpx 40rpx;

      .btn {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 8rpx;
        font-size: 32rpx;
        text-align: center;

        &.cancel {
          background: #f7f7f7;
          color: #333;
        }

        &.save {
          background: #f23030;
          color: #fff;
        }
      }
    }
  }
}

.popup-content1 {
  background: #fff;
  border-radius: 10px 10px 0 0;

  .popup-header {
    position: relative;
    padding: 20rpx 30px;
    text-align: center;
    border-bottom: 1px solid #eee;
    background-color: #ea1306;
    border-radius: 10px 10px 0 0;
    color: #fff;

    text {
      font-size: 16px;
      font-weight: 500;
    }

    .close-icon {
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 24px;
      color: #fff;
      padding: 5px;
    }
  }

  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }

  .popup-body {
    padding: 20px;
    max-height: 800rpx;
    overflow: scroll;

    /* Step Container */
    .step-container {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      // width: 200px;
    }

    /* Individual Step */
    .step {
      display: flex;
      align-items: center;
      margin: 10px 0;
      position: relative;
      padding-left: 15rpx;
    }

    /* Step Number */
    .step-number {
      width: 10px;
      height: 10px;
      border: 5px solid #ea1306;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
    }

    /* Step Title */
    .step-title {
      margin-left: 10px;
      font-size: 14px;
      color: #333;
    }

    /* Line Between Steps (Dashed) */
    .step::after {
      content: "";
      position: absolute;
      top: 68%;
      left: 32rpx;
      width: 2px;
      height: 90%;
      border-left: 2px dashed #ea1306;
      // /* Dashed Line */
      // z-index: -1;
    }

    /* Remove line for the last step */
    .step:last-child::after {
      display: none;
    }
  }
}
