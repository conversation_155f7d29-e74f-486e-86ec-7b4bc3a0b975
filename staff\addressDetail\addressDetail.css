/* pages/editAddress/editAddress.wxss */
page {
  padding-bottom: 120rpx;
}
.list {
  background: #fff;
  margin: 20rpx 0 0;
  position: relative;
}
.list .li {
  border-bottom: 1rpx solid #f4f6fa;
  height: 100rpx;
  display: flex;
  display: -webkit-flex;
  align-items: center;
  padding: 0 30rpx;
}
.list .li text {
  display: block;
  font-size: 28rpx;
  color: #999;
}
.moren .li text {
  color: #333;
}
.item {
  flex: 1;
  -webkit-flex: 1;
  font-size: 28rpx;
  color: #333;
  text-align: right;
}
.item input {
  width: 100%;
  text-align: right;
  font-size: 28rpx;
  color: #333;
}
.rico {
  display: block;
  width: 16rpx;
  height: 26rpx;
  margin-left: 20rpx;
}
.she {
  justify-content: flex-end;
  display: flex;
  display: -webkit-flex;
}
.check {
  width: 34rpx;
  height: 34rpx;
}
.btn {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
}
.del {
  width: 50%;
  height: 100rpx;
  line-height: 100rpx;
  background: #fff;
  font-size: 36rpx;
  color: #333;
  text-align: center;
}
.loca {
  width: 48rpx;
  height: 48rpx;
  padding-left: 20rpx;
}
.submit {
  width: 50% !important;
  height: 100rpx !important;
  line-height: 100rpx !important;
  background: #0b78ff;
  font-size: 36rpx;
  color: #fff;
  text-align: center;
}
.addBtn {
  position: fixed;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  background: #0b78ff;
  width: 85%;
  padding: 20rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  border-radius: 10rpx;
}
