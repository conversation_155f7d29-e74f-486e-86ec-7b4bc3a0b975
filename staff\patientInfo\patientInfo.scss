/* 基础样式优化 */
page {
  background-color: #f5f7fa;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
}

.content {
  padding: 30rpx;
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

/* 标题栏美化 */
.title {
  padding: 20rpx 0;
  border-bottom: 1px solid #eef0f5;
  margin-bottom: 24rpx;
}

.title .df {
  display: flex;
  align-items: center;
}

.shu {
  width: 8rpx;
  height: 36rpx;
  background: linear-gradient(180deg, #0173ff, #00a0ff);
  margin-right: 16rpx;
  border-radius: 4rpx;
}

.Copay {
  font-size: 34rpx;
  font-weight: 600;
  background: linear-gradient(90deg, #0173ff, #00a0ff);
  -webkit-background-clip: text;
  color: transparent;
}

/* 表单项美化 */
.infoDet {
  margin: 10rpx 0;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.leftInfo {
  width: 180rpx;
  color: #666;
  font-size: 28rpx;
  font-weight: 500;
}

.rightInfo {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 搜索框美化 */
.searchs {
  position: relative;
  margin: 24rpx 0;
  padding: 20rpx 24rpx;
  background: #f5f7fa;
  border-radius: 12rpx;
  border: 1px solid #eef0f5;
  transition: all 0.3s ease;
}

.searchs:focus-within {
  border-color: #0173ff;
  box-shadow: 0 0 0 2px rgba(1, 115, 255, 0.1);
}

.ssico {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 按钮样式美化 */
.finishSubmit {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: linear-gradient(90deg, #0173ff, #00a0ff);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  box-shadow: 0 4rpx 16rpx rgba(1, 115, 255, 0.3);
  transition: all 0.3s ease;
  z-index: 2;
}

.finishSubmit:active {
  transform: translateX(-50%) scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(1, 115, 255, 0.2);
}

// /* 列表项美化 */
// .list2 .li {
//   display: flex;
//   padding: 24rpx;
//   transition: all 0.3s ease;
// }

.list2 .li:hover {
  background: #f5f7fa;
}

.checkbox {
  width: 40rpx;
  display: flex;
  align-items: center;
}

.checkImg {
  width: 32rpx;
  height: 32rpx;
  transition: all 0.3s ease;
}

/* 商品卡片美化 */
.cimgs {
  width: 140rpx;
  height: 140rpx;
  margin-right: 24rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.cimg {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.jian:active {
  background: #eef0f5;
}

/* 历史记录部分美化 */
.lumpSum_section {
  background: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

/* 年份选择器样式 */
.time_year {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  margin-bottom: 30rpx;
  border-bottom: 1px solid #eef0f5;
}

.time_year {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 消费总额统计样式 */
.lumpSum {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  color: #666;
}

.lumpSum:first-child {
  padding-top: 0;
}

.lumpSum:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

/* 金额显示样式 */
.lumpSum view:last-child {
  color: #333;
  font-weight: 500;
}

.lumpSum:first-child view:last-child {
  color: #0173ff;
  font-size: 36rpx;
  font-weight: 600;
}

/* 月度明细样式 */
.details_section {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

/* 月份标题样式 */
.details_time {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1px solid #eef0f5;
}

.details_time view:first-child {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.details_time view:last-child {
  font-size: 26rpx;
  color: #666;
}

/* 消费记录项样式 */
.details_item {
  padding: 24rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.details_item:hover {
  background: #f5f7fa;
}

.details_item:not(:last-child) {
  border-bottom: 1px solid #eef0f5;
}

/* 消费记录顶部信息 */
.details_top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.details_top view:first-child {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.details_top view:last-child {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #0173ff;
  font-weight: 500;
}

/* 消费记录底部信息 */
.details_bottom {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #999;
}

.details_bottom view {
  position: relative;
  padding: 0 15rpx;
}

.details_bottom view:first-child {
  padding-left: 0;
}

.details_bottom view:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 24rpx;
  background: #eef0f5;
}

/* 展开/收起按钮样式 */
.unfold-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  color: #0173ff;
  font-size: 28rpx;
}

.unfold-btn view {
  display: flex;
  align-items: center;
}

.unfold-btn view:last-child {
  margin-left: 8rpx;
  transition: transform 0.3s ease;
}

/* 展开状态的箭头旋转 */
.unfold-btn.active view:last-child {
  transform: rotate(180deg);
}

/* 添加动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content {
  animation: slideIn 0.3s ease;
}

/* 病历模块样式调整 */
.card_body {
  padding: 20rpx 0;
}

.nav {
  display: flex;
  padding: 10rpx 0;
  border-bottom: 1px solid #eef0f5;
  align-items: center;
}

.nav:last-child {
  border-bottom: none;
}

.case_left {
  width: 120rpx;
  color: #666;
  font-size: 28rpx;
  font-weight: 500;
  flex-shrink: 0;
  text-align: right;
  padding-right: 20rpx;
}

.nav_right {
  flex: 1;
  display: flex;
  position: relative;
  align-items: center;
}

.diagnoseInp {
  width: 100%;
  background: #fff;
  border: 1px solid #004ed6;
  padding-left:10rpx;
  border-radius: 8rpx;
  min-height: 64rpx;
  display: flex;
  align-items: center;
  overflow: hidden;
  font-size:24rpx;
}
.delete_icon {
  position: absolute;
  right: 10rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 34rpx;
  color: #999;
}

/* 体格检查部分样式 */
.physical-exam {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  padding: 24rpx 0;
  align-items: center;
}

.exam-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.exam-label {
  color: #666;
  margin-right: 16rpx;
}

.exam-input {
  width: 160rpx;
  background: #f5f7fa;
  border-radius: 8rpx;
  padding: 12rpx;
  text-align: center;
}

.exam-unit {
  color: #666;
  margin-left: 8rpx;
}

/* 文件上传区域样式 */
.upload-section {
  margin: 24rpx 0;
  display: flex;
  align-items: center;
}

.upload-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

/* 病症标签样式 */
.liste {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin: 16rpx 0;
  align-items: center;
}

.tabItem {
  padding: 8rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
  background: #f5f7fa;
  border: 1px solid #eef0f5;
  transition: all 0.3s ease;
}

.itemActive {
  color: #0173ff;
  background: rgba(1, 115, 255, 0.1);
  border-color: #0173ff;
}

/* 搜索框样式统一 */
.drugSearchs {
  position: relative;
  margin: 24rpx 0;
  padding: 16rpx 24rpx;
  background: #f5f7fa;
  border-radius: 12rpx;
  border: 1px solid #eef0f5;
  display: flex;
  align-items: center;
}

.drugSearchs input {
  flex: 1;
  font-size: 28rpx;
  padding-left: 60rpx;
}

/* 添加医嘱按钮样式 */
.createBox {
  margin-left: auto;
}

.createImg {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  color: #0173ff;
  background: rgba(1, 115, 255, 0.1);
  border: 1px solid #0173ff;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.createImg:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 输入框统一样式 */
.uni-easyinput {
  width: 100%;
  background: #f5f7fa;
  border-radius: 8rpx;
  border: 1px solid #eef0f5;
  display: flex;
  align-items: center;
}

.uni-easyinput:focus-within {
  border-color: #0173ff;
  box-shadow: 0 0 0 2px rgba(1, 115, 255, 0.1);
}

.uni-easyinput__content {
  display: flex;
  align-items: center;
}

/* 体温等指标的输入框样式 */
.vital-signs {
  display: flex;
  flex-wrap: wrap;
  gap: 32rpx;
  padding: 24rpx 0;
}

.vital-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.vital-label {
  color: #666;
  margin-right: 16rpx;
}

.vital-input {
  width: 120rpx;
  text-align: center;
  background: #f5f7fa;
  border-radius: 8rpx;
  padding: 12rpx;
  margin-right: 8rpx;
}

.vital-unit {
  color: #666;
}

/* 修复uni-easyinput组件的样式 */
/deep/ .uni-easyinput__content {
  min-height: 64rpx !important;
  line-height: 64rpx !important;
}

/deep/ .uni-easyinput__placeholder-class {
  height: 64rpx !important;
  line-height: 64rpx !important;
}

/* 修复textarea的对齐问题 */
textarea {
  min-height: 64rpx;
  line-height: 1.5;
  padding: 12rpx 0;
}

/* 商品信息区域样式优化 */
.zhinfo {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
}

.zhinfo .zhtit {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  font-weight: 500;
}

/* 价格和数量控制区域 */
.zhinfo .jiage {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 0;
  flex-wrap: wrap;
}

/* 用法用量区域样式优化 */
.usage-container {
  margin-top: 20rpx;
  width: 100%;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.usage-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.usage-label {
  min-width: 60rpx;
  font-size: 28rpx;
  color: #666;
}

.usage-input {
  flex: 1;
  height: 64rpx;
  background: #f5f7fa;
  border: 1px solid #eef0f5;
  border-radius: 8rpx;
  padding: 0 12rpx;
  font-size: 28rpx;
}

/* 频次输入区域优化 */
.frequency-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  box-sizing: border-box;
  margin-top: 16rpx;
  gap: 40rpx;
}

.frequency-group {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  min-width: 180rpx;
  margin-right: 0;
}

.frequency-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 12rpx;
  white-space: nowrap;
  flex-shrink: 0;
}

.frequency-unit {
  font-size: 28rpx;
  color: #666;
  margin-left: 12rpx;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 用法输入框样式优化 */
.usage-container {
  margin-top: 20rpx;
  width: 100%;
  box-sizing: border-box;
  padding: 0;
}

.usage-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.usage-label {
  min-width: 60rpx;
  font-size: 28rpx;
  color: #666;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.usage-input {
  flex: 1;
  min-width: 200rpx;
  height: 64rpx;
  background: #f5f7fa;
  border: 1px solid #eef0f5;
  border-radius: 8rpx;
  padding: 0 12rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

/* 价格显示 */
.zhjia {
  display: flex;
  align-items: center;
  color: #cc0516;
  font-size: 32rpx;
  font-weight: 500;
}

.zhjia text:first-child {
  font-size: 24rpx;
  margin-right: 4rpx;
}

/* 数量控制器样式优化 */
.num-control {
  display: flex;
  align-items: center;
  border: 1px solid #eef0f5;
  border-radius: 8rpx;
  overflow: hidden;
}

.num-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #666;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.num-input {
  width: 88rpx;
  height: 64rpx;
  text-align: center;
  border-left: 1px solid #eef0f5;
  border-right: 1px solid #eef0f5;
  font-size: 28rpx;
  color: #333;
}

/* 商品信息hover效果 */
.zhinfo:hover .zhtit {
  color: #0173ff;
}

/* 商品列表样式优化 */
.list2 {
  width: 100%;
  overflow: hidden; /* 防止内容溢出 */
}

.list2 .li {
  display: flex;
  /* border-bottom: 1px solid #eef0f5; */
  width: 100%;
  box-sizing: border-box;
}

.info {
  flex: 1;
  display: flex;
  align-items: center;
  width: calc(100% - 40rpx); /* 减去checkbox的宽度 */
  overflow: hidden; /* 防止内容溢出 */
}

// .zhinfo {
//   flex: 1;
//   min-width: 0; /* 防止flex子项溢出 */
//   padding: 0 20rpx;
//   box-sizing: border-box;
// }

/* 用法用量区域样式优化 */
.usage-container {
  width: 100%;
  margin-top: 20rpx;
  box-sizing: border-box;
}

.usage-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.usage-label {
  min-width: 60rpx;
  font-size: 28rpx;
  color: #666;
  margin-right: 12rpx;
}

.usage-input {
  flex: 1;
  height: 64rpx;
  background: #f5f7fa;
  border: 1px solid #eef0f5;
  border-radius: 8rpx;
  padding: 0 12rpx;
  font-size: 28rpx;
  min-width: 0; /* 防止input撑开容器 */
}

/* 频次输入区域优化 */
.frequency-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.frequency-group {
  display: flex;
  align-items: center;
  min-width: 0; /* 防止flex子项溢出 */
}

.frequency-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 8rpx;
  white-space: nowrap;
}

.frequency-unit {
  font-size: 28rpx;
  color: #666;
  margin-left: 8rpx;
  white-space: nowrap;
}

/* 价格和数量控制区域 */
.jiage {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 16rpx 0;
  flex-wrap: nowrap; /* 防止换行 */
}

.zhjia {
  font-size: 32rpx;
  color: #cc0516;
  white-space: nowrap;
}

/* 数量控制器样式 */
.num {
  display: flex;
  align-items: center;
  border-radius: 8rpx;
  overflow: hidden;
}

.jian {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  background: #f5f7fa;
  font-size: 28rpx;
}

.nums {
  width: 50rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  border-left: 1px solid #eef0f5;
  border-right: 1px solid #eef0f5;
  font-size: 28rpx;
}
/deep/ .is-input-border,
/deep/ .uni-stat__select,
/deep/ .uni-date-x--border {
  border: 1px solid #004ed6 !important;
  height: 64rpx !important;
}
/deep/ .uni-date-x {
  height: 60rpx !important;
}

.search-item-hover {
  background-color: #f0f7ff;
  color: #0173ff;
}
.popup_content {
  border-radius: 20rpx 20rpx 0 0;
  height: 80vh;
  background: #fff;
  overflow-y: auto;
  padding: 20rpx;
  font-size: 24rpx;

  .add_btn {
    padding: 10rpx 50rpx;
    margin: 20rpx auto;
    background: #007aff;
    color: #fff;
    border-radius: 10rpx;
    text-align: center;
    width: 110rpx;
    font-size: 24rpx;
  }

  .table {
    border: 1px solid #ebeef5;
    border-radius: 8rpx;
    margin: 0 auto;
    background: #fff;

    .table-header {
      display: flex;
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;
      font-weight: bold;

      .table-cell {
        flex: 1;
        padding: 20rpx;
        text-align: left;
        font-size: 28rpx;
        color: #333;

        &:first-child {
          flex: 0 0 200rpx;
        }
      }
    }

    .table-group {
      display: flex;
      border-bottom: 1px solid #ebeef5;

      &:last-child {
        border-bottom: none;
      }

      .category-cell {
        flex: 0 0 200rpx;
        padding: 20rpx;
        font-size: 28rpx;
        color: #333;
        border-right: 1px solid #ebeef5;
        background-color: #fafafa;
      }

      .table-cell-group {
        flex: 1;
        display: flex;
        flex-direction: column;

        .content-cell {
          padding: 20rpx;
          font-size: 28rpx;
          color: #333;
          border-bottom: 1px solid #ebeef5;
          word-break: break-all;

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }
}

/* 体格检查项目的样式统一 */
.vital-signs-row {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}

.vital-sign-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  margin-bottom: 16rpx;
}

.vital-sign-label {
  min-width: 60rpx;
  margin-right: 8rpx;
}

.vital-sign-input-container {
  width: 100px;
  display: flex;
  align-items: center;
}

.vital-sign-input {
  width: 50%;
  flex-shrink: 0;
}

.vital-sign-unit {
  margin-left: 4rpx;
  font-size: 24rpx;
  color: #666;
}
