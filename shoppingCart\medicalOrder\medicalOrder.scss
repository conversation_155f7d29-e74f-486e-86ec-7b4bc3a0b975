.nav {
  display: flex;
  align-items: center;
}
.titleBg {
  width: 100%;
  height: 324rpx;
  background: #3b71e8;
  border-radius: 0 0 46rpx 46rpx;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.merchants_box {
  display: flex;
  align-items: center;
  margin: 54rpx 60rpx 50rpx;
  justify-content: space-between;
  // line-height:1.25;
}
.info_box {
  // line-height:1.25;
  height: 721rpx;
  background: #fff;
  margin: 0 40rpx 0;
  border-radius: 16rpx;
  .totalCost {
    color: #606266;
    font-size: 36rpx;
    height: 140rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 40rpx;
    border-bottom: 2rpx dashed #eff0f4;
  }
  .detailedCosts {
    padding-top: 44rpx;
    .detailedCosts_item {
      display: flex;
      justify-content: space-between;
      margin: 0 40rpx;
      color: #909399;
      font-size: 28rpx;
      margin-bottom: 16rpx;
    }
    .cashCosts {
      color: #3b71e8;
      font-size: 32rpx;
      margin: 0 44rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  .distributionCosts {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 40rpx;
    color: #909399;
    font-size: 28rpx;
    padding-bottom: 54rpx;
    border-bottom: 2rpx dashed #eff0f4;
  }
  .viewDetails {
    height: 108rpx;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0 0 16rpx 16rpx;
    color: #606266;
    font-size: 28rpx;
    // background-color: red;
    // z-index: -1;
  }
}
.persons {
  margin: 56rpx 40rpx;
  .persons_btn {
    display: flex;
    align-items: center;
    margin-top: 30rpx;
    view {
      width: 320rpx;
      height: 72rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 3rpx solid #ccc;
      font-size: 26rpx;
      border-radius: 8rpx;
      background: #f8f9fa;
      color: #606266;
    }
    .persons_active {
      border-color: #3b71e8;
      background: #f6f9ff;
      color: #3b71e8;
    }
  }
}
.pay_mode {
  margin: 56rpx 40rpx 40rpx;
  .pay_mode_item {
    width: 670rpx;
    height: 130rpx;
    border: 2rpx solid #e6e6e6;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
  }
}

.pay_box {
  width: 100%;
  height: 158rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  background: #fff;
  display: flex;
  justify-content: space-between;
  border: 2rpx solid #f5f5f5;
  .pay_money {
    margin-top: 30rpx;
    margin-left: 40rpx;
  }
  .pay_btn {
    margin-top: 30rpx;
    margin-right: 40rpx;
    font-size: 36rpx;
    color: #fff;
    background: #3b71e8;
    width: 240rpx;
    height: 100rpx;
    border-radius: 50rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.details {
  height: calc(100vh - 44px);
  background: #fff;
  border-radius: 40rpx 40rpx 0 0;
  overflow: scroll;
  .details_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    /* margin: 48rpx 28rpx 50rpx 40rpx; */
    position: fixed;
    background: #fff;
    width: 100%;
    border-radius: 40rpx 40rpx 0 0;
    height: 150rpx;
  }
  .details_info {
    width: 706rpx;
    background: #fff;
    margin: 0 auto 20rpx;
    border-radius: 12rpx;
    border: 2rpx solid #e6e6e6;
    .details_info_title {
      height: 130rpx;
      display: flex;
      align-items: center;
      border-bottom: 2rpx solid #e6e6e6;
      .line {
        background: #3b71e8;
        width: 8rpx;
        height: 32rpx;
        margin-left: 6rpx;
        margin-right: 24rpx;
      }
      .text {
        color: #303133;
        font-size: 34rpx;
      }
    }
    .details_content {
      padding: 40rpx;
      .details_content_item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #606266;
        font-size: 26rpx;
        margin-bottom: 24rpx;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
.loading {
  width: 280rpx;
  height: 298rpx;
  background: #fff;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
}
