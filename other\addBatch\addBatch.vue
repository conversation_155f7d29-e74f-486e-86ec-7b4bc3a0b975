<template>
    <view class="add-batch">
        <!-- 新增批号 -->
        <view class="section">
            <view class="section-header">
                <view class="section-title">{{ isEdit ? '编辑批号' : '新增批号' }}</view>
            </view>

            <view class="form-item">
                <view class="label">批　　号</view>
                <input :disabled="isEdit" class="input" type="text" placeholder="请输入批号"
                    v-model="batchForm.batchNumber" />
            </view>

            <view class="form-item">
                <view class="label">账面库存</view>
                <input disabled class="input" type="number" placeholder="请输入数值" v-model="batchForm.quantity" />
            </view>

            <view class="form-item">
                <view class="label">盘点库存</view>
                <input class="input" type="number" placeholder="请输入数值" v-model="batchForm.invStock" />
            </view>

            <view class="form-item">
                <view class="label">生产日期</view>
                <uni-datetime-picker type="date" :clear-icon="false" return-type="string" format="yyyy-MM-dd"
                    @change="onProductionDateChange" v-model="batchForm.productionDate">
                </uni-datetime-picker>
            </view>

            <view class="form-item">
                <view class="label">失效期</view>
                <uni-datetime-picker type="date" :clear-icon="false" return-type="string" format="yyyy-MM-dd"
                    @change="onExpiryDateChange" v-model="batchForm.until">
                </uni-datetime-picker>
            </view>
        </view>

        <!-- 底部按钮 -->
        <view class="bottom-actions">
            <view class="action-btn cancel" @click="goBack">取消</view>
            <view class="action-btn save" @click="saveInfo">保存</view>
        </view>
    </view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
    data() {
        return {
            isEdit: false,
            goodsNo: "",
            batchForm: {
                batchNumber: "",
                quantity: 0,
                invStock: "",
                productionDate: "",
                until: "",
                isInv: 0,
            },
            originalBatchNumber: "", // 用于记录原始批号，便于在列表中找到对应项
            index: null
        }
    },
    onLoad(options) {
        console.log(options);
        this.goodsNo = options.goodsNo
        if (options.obj) {
            const parsedObj = JSON.parse(decodeURIComponent(options.obj));
            this.batchForm = JSON.parse(JSON.stringify(parsedObj)); // 深拷贝对象
            this.originalBatchNumber = parsedObj.batchNumber; // 保存原始批号
            this.isEdit = true;
            this.index = options.index
            uni.setNavigationBarTitle({
                title: '编辑批号'
            })
        }
    },
    methods: {
        goBack() {
            uni.navigateBack();
        },
        onProductionDateChange(e) {
            this.batchForm.productionDate = e;
        },
        onExpiryDateChange(e) {
            this.batchForm.until = e;
        },
        saveInfo() {
            // 表单验证
            if (!this.batchForm.batchNumber) {
                req.msg('请输入批号');
                return;
            }
            if (!this.batchForm.invStock) {
                req.msg('请输入盘点库存');
                return;
            }

            if (!this.batchForm.productionDate) {
                req.msg('请选择生产日期');
                return;
            }

            if (!this.batchForm.until) {
                req.msg('请选择失效期');
                return;
            }

            const pages = getCurrentPages();
            const prevPage = pages[pages.length - 2];

            if (this.isEdit) {
                // 直接使用传入的index更新对应的批号数据
                prevPage.$vm.batchInfoList.splice(this.index, 1, JSON.parse(JSON.stringify(this.batchForm)));
            } else {
                // 新增批号
                prevPage.$vm.batchInfoList.push({
                    ...this.batchForm,
                    goodsNo: this.goodsNo
                });
            }

            // 返回上一页
            uni.navigateBack();
        }
    }
}
</script>

<style lang="scss" scoped>
@import './addBatch.scss';
</style>