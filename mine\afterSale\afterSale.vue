<template>
    <view class="after-sale">
        <view class="form-section" v-if="orderInfo.products && orderInfo.products.length">
            <!-- 商品信息展示 -->
            <view class="goods-info" v-for="(item, index) in orderInfo.products" :key="index">
                <view class="checkbox-wrapper" @tap="toggleSelect(index)">
                    <view :class="['checkbox', item.selected ? 'checked' : '']"></view>
                </view>
                <image :src="item.pic" mode="aspectFill"></image>
                <view class="goods-detail">
                    <view class="goods-name">{{ item.title }}</view>
                    <!-- <view class="goods-spec">{{ item.spec }}</view> -->
                    <view class="goods-price">¥{{ item.salePrice }}</view>
                    <view class="goods-quantity">
                        <text>数量：{{ item.quantity }}</text>
                    </view>
                </view>
            </view>

            <!-- 售后类型选择 -->
            <view class="form-item">
                <view class="label">售后类型</view>
                <view class="type-options">
                    <view v-for="(item, index) in filteredTypeOptions" :key="index"
                        :class="['type-item', selectedType === item.value ? 'active' : '']"
                        @tap="selectType(item.value)">
                        {{ item.label }}
                    </view>
                </view>
            </view>

            <!-- 退款金额 -->
            <view class="form-item">
                <view class="label">退款金额</view>
                <view class="refund-amount">¥{{ calculateTotalRefund() }}</view>
            </view>

            <!-- 退款原因 -->
            <view class="form-item">
                <view class="label">退款原因</view>
                <picker @change="reasonChange" :value="reasonIndex" :range="currentReasonOptions">
                    <view class="picker">
                        {{ currentReasonOptions[reasonIndex] || '请选择退款原因' }}
                    </view>
                </picker>
            </view>

            <!-- 问题描述 -->
            <view class="form-item">
                <view class="label">问题描述</view>
                <textarea v-model="description" placeholder="请详细描述您遇到的问题" :maxlength="200" />
            </view>

            <!-- 图片上传 -->
            <view class="form-item">
                <view class="label">上传凭证</view>
                <uni-file-picker v-model="imageUrls" :auto-upload="true" :limit="3" file-mediatype="image" mode="grid"
                    @select="uploadImage" @delete="deleteFile" />
            </view>
        </view>
        <view v-else class="empty-tip">
            加载中...
        </view>

        <!-- 提交按钮 -->
        <view class="submit-btn" @tap="submitAfterSale">提交申请</view>
    </view>
</template>

<script>
const req = require('../../utils/request')
export default {
    data() {
        return {
            orderId: '',
            orderInfo: {
                products: []
            },

            typeOptions: [
                { label: '仅退款', value: 1 },
                { label: '退货退款', value: 2 }
            ],
            selectedType: 1,
            reasonOptions: [],
            refundOptions: [],
            reasonIndex: -1,
            description: '',
            imageUrls: []
        }
    },

    computed: {
        // 根据订单状态过滤售后类型选项
        filteredTypeOptions() {
            if (this.orderInfo.state === 5 || this.orderInfo.state === 6) {
                return this.typeOptions
            }
            return this.typeOptions.filter(item => item.value === 1)
        },

        // 当前显示的原因选项
        currentReasonOptions() {
            return this.selectedType === 1
                ? this.refundOptions.map(item => item.label)
                : this.reasonOptions.map(item => item.label)
        }
    },

    watch: {
        // 监听过滤后的选项变化，自动调整选中的类型
        filteredTypeOptions: {
            immediate: true,
            handler(newOptions) {
                if (newOptions.length === 1 && this.selectedType === 2) {
                    this.selectedType = newOptions[0].value
                }
            }
        }
    },

    onLoad(options) {
        if (options.orderId) {
            this.orderId = options.orderId
            this.getOrderDetail()
            this.getReason()
        }
    },

    methods: {
        getReason() {
            req.getRequest("/shopApi/mp/refund/reason", {}, res => {
                if (res.code === 200 && res.data) {
                    this.reasonOptions = res.data.reason || []
                    this.refundOptions = res.data.refund || []
                    // 根据当前选择的类型设置对应的选项
                    this.updateReasonOptions()
                }
            })
        },

        // 新增方法：更新原因选项
        updateReasonOptions() {
            this.reasonIndex = -1 // 重置选择的索引
        },

        getOrderDetail() {
            req.showLoading()
            req.getRequest("/shopApi/mp/order/detail", {
                id: this.orderId
            }, res => {
                uni.hideLoading()
                if (res.code === 200) {
                    const products = res.data.products.map(item => ({
                        ...item,
                        selected: true
                    }))
                    this.orderInfo = {
                        ...res.data,
                        products
                    }
                } else {
                    req.msg(res.msg)
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 1500)
                }
            })
        },
        // 选择售后类型
        selectType(type) {
            if (type === 2 && !(this.orderInfo.state === 5 || this.orderInfo.state === 6)) {
                return req.msg('当前订单状态不支持退货退款')
            }
            this.selectedType = type
            this.updateReasonOptions()
        },

        // 选择退款原因
        reasonChange(e) {
            this.reasonIndex = e.detail.value
        },

        // 选择文件
        uploadImage(e) {
            req.showLoading()
            const promises = e.tempFilePaths.map(path =>
                req.uploadFile('/shopApi/home/<USER>', path, data => {
                    this.imageUrls.push({ url: data })
                })
            )

            Promise.all(promises)
                .then(() => uni.hideLoading())
                .catch(err => {
                    uni.hideLoading()
                    req.msg(err.msg)
                })
        },

        // 删除文件
        deleteFile(e) {
            // 使用filter过滤掉要删除的图片
            this.imageUrls = this.imageUrls.filter((_, i) => i !== e.index)
        },

        // 计算总退款金额
        calculateTotalRefund() {
            if (!this.orderInfo.products || this.orderInfo.products.length === 0) {
                return '0.00'
            }
            return this.orderInfo.products.reduce((total, item) => {
                return total + (item.selected ? item.salePrice * item.quantity : 0)
            }, 0).toFixed(2)
        },

        // 提交售后申请
        submitAfterSale() {
            // 检查是否有选中的商品
            const selectedGoods = this.orderInfo.products.filter(item => item.selected)
            if (selectedGoods.length === 0) {
                return req.msg('请选择要退款的商品')
            }

            // 获取选中的原因对象
            const currentOptions = this.selectedType === 1 ? this.refundOptions : this.reasonOptions
            const selectedReason = currentOptions[this.reasonIndex]

            if (!selectedReason) {
                return req.msg('请选择退款原因')
            }

            if (!this.description) {
                return req.msg('请填写问题描述')
            }

            // 构建退款商品列表
            const refundProductList = selectedGoods.map(item => ({
                id: item.id, // 商品ID
                deliveryInformation: item.deliveryInformation, // 发货信息ID
                quantity: item.quantity, // 商品数量
                skuId: item.skuId, // SKU ID
                pic: item.pic, // 商品图片
                refundQuantity: item.quantity, // 退款数量（这里设置为全部数量）
                actualPrice: item.salePrice, // 实际价格
                orderProductId: item.orderProductId, // 订单商品ID
                isAdd: true
            }))

            // 按发货单分组商品
            const groupedProducts = {}
            refundProductList.forEach(product => {
                const deliverId = product.deliveryInformation
                if (!groupedProducts[deliverId]) {
                    groupedProducts[deliverId] = {
                        refundMoney: 0,
                        deliverId: deliverId,
                        refundProductList: []
                    }
                }
                groupedProducts[deliverId].refundProductList.push(product)
                groupedProducts[deliverId].refundMoney += product.actualPrice * product.refundQuantity
            })

            // 转换为数组格式
            const jsonStr = Object.values(groupedProducts).map(group => ({
                ...group,
                refundMoney: group.refundMoney.toFixed(2)
            }))

            const params = {
                orderId: this.orderId,
                refundType: this.selectedType,
                refundReason: selectedReason.value,
                remarks: this.description,
                imgUrl: this.imageUrls.map(item => item.url).join(','),
                jsonStr: JSON.stringify(jsonStr),
                apiType: 1
            }
            req.showLoading()
            req.postRequest('/shopApi/mp/refund/save', params, res => {
                uni.hideLoading()
                if (res.code === 200) {
                    req.msg('提交成功')
                    setTimeout(() => {
                        // 跳转到售后详情页
                        uni.redirectTo({
                            url: `/mine/afterSaleDetail/afterSaleDetail?id=${res.data[0]}`
                        })
                    }, 1500)
                }
            })
        },

        // 切换商品选中状态
        toggleSelect(index) {
            this.$set(this.orderInfo.products[index], 'selected', !this.orderInfo.products[index].selected)
        },
    }
}
</script>

<style src="./afterSale.scss" lang="scss"></style>