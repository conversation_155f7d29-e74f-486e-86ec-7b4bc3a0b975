<template>
    <view class="personal-center">
        <!-- 顶部用户信息 -->
        <view class="user-info">
            <view class="left">
                <image class="avatar" :src="userInfo.avatar"></image>
                <view class="user-detail">
                    <view class="name">{{ userInfo.memName }}</view>
                    <view class="recommender">推荐人:{{ userInfo.recommender }}</view>
                </view>
            </view>
            <view class="promote-btn" @click="handlePromote">
                <image
                    src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/b8cbecc0b6054f7098a1b7e271024978.png"
                    mode="widthFix"></image>
                我要推广
            </view>
        </view>

        <!-- 数据统计 -->
        <view class="data-statistics">
            <view class="title">我的数据</view>
            <view class="statistics-list">
                <view class="statistics-item">
                    <image
                        src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/6e623a7df668481887acbd70e0029bb6.png"
                        mode="widthFix"></image>
                    <view class="value">{{ statistics.todayPromote }}</view>
                    <view class="label">今天推广</view>
                </view>
                <view class="statistics-item">
                    <image
                        src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/664686ed266b40a2b052466d4462ca7d.png  "
                        mode="widthFix"></image>
                    <view class="value">{{ statistics.totalPromote }}</view>
                    <view class="label">总推广数</view>
                </view>
                <view class="statistics-item">
                    <image
                        src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/9de308ad890149c1b4504ee15710e329.png"
                        mode="widthFix"></image>
                    <view class="value">{{ statistics.balance }}</view>
                    <view class="label">和币</view>
                </view>
                <view class="statistics-item">
                    <image
                        src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/e6773d45a0f74cd7a66cc4d20313a531.png"
                        mode="widthFix"></image>
                    <view class="value">{{ statistics.orders }}</view>
                    <view class="label">销售订单</view>
                </view>
            </view>
        </view>

        <!-- 达标情况 -->
        <view class="target-status">
            <view class="title">达标情况</view>
            <view class="target-list">
                <view class="target-item" v-for="(item, index) in targetList" :key="index">
                    <image :src="item.image" mode="widthFix" class="medal"></image>
                    <view class="target-info">
                        达标标准<text>{{ item.total }}</text>单 已完成<text>{{ item.finished }}</text>单 差<text>{{ item.remaining
                            }}</text>单 首单奖励<text>{{ item.reward }}</text>元!!!
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const req = require('../../utils/request')
export default {
    data() {
        return {
            userInfo: {
                name: '王亚辉',
                recommender: '方赞冰'
            },
            statistics: {
                todayPromote: 0,
                totalPromote: 0,
                balance: 0,
                orders: 0
            },
            targetList: [
                {
                    image: "https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/b78de8c5fefc4cffb8d85fab10495b2b.png",
                    total: 300,
                    finished: 200,
                    remaining: 100,
                    reward: 3000
                },
                {
                    image: "https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/fdf432685f2b4e66a0e3d2abb200b393.png",
                    total: 50,
                    finished: 48,
                    remaining: 2,
                    reward: 500
                },
                {
                    image: "https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241209/34653ac43ce7405d974a976f924c99ff.png",
                    total: 50,
                    finished: 48,
                    remaining: 2,
                    reward: 200
                }
            ]
        }
    },
    onLoad() {
        this.userInfo = req.getStorage('userInfo')
    },
    methods: {
        handlePromote() {
            // 处理推广按钮点击
            uni.showToast({
                title: '推广功能开发中',
                icon: 'none'
            })
        }
    }
}
</script>

<style scoped lang="scss" src="./personalCenter.scss"></style>