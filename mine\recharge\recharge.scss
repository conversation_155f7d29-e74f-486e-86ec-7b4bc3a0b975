.recharge-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}

.balance-card {
  background: #EA1306;
  border-radius: 20rpx;
  padding: 40rpx;
  color: #fff;
  margin-bottom: 30rpx;
  position: relative;

  .title {
    font-size: 28rpx;
    margin-bottom: 20rpx;
  }

  .amount {
    .symbol {
      font-size: 40rpx;
    }
    .number {
      font-size: 60rpx;
      font-weight: bold;
    }
  }

  .record-link {
    position: absolute;
    right: 30rpx;
    top: 30rpx;
    font-size: 24rpx;
    color: #fff;
    display: flex;
    align-items: center;
    
    .iconfont {
      font-size: 24rpx;
      margin-left: 4rpx;
    }
  }
}

.amount-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;

  .section-title {
    font-size: 30rpx;
    color: #333;
    margin-bottom: 30rpx;
  }

  .amount-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;

    .amount-item {
      background: #f8f8f8;
      border-radius: 12rpx;
      padding: 30rpx 20rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 2rpx solid transparent;

      &.active {
        background: #fff0f0;
        border-color: #ff6b6b;

        .money {
          color: #ff6b6b;
        }
      }

      .money {
        font-size: 32rpx;
        color: #333;
      }

      .gift {
        font-size: 24rpx;
        color: #ff6b6b;
      }
    }
  }
}

.custom-amount {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;

  .section-title {
    font-size: 30rpx;
    color: #333;
    margin-bottom: 30rpx;
  }

  .input-wrap {
    display: flex;
    align-items: center;
    background: #f8f8f8;
    border-radius: 12rpx;
    padding: 20rpx;

    .symbol {
      font-size: 32rpx;
      color: #333;
      margin-right: 10rpx;
    }

    input {
      flex: 1;
      font-size: 32rpx;
    }
  }
}

.bottom-button {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 40rpx;
  padding: 0 40rpx;

  .recharge-btn {
    background: #EA1306;
    color: #fff;
    border-radius: 45rpx;
    font-size: 32rpx;
    height: 90rpx;
    line-height: 90rpx;

    &:disabled {
      background: #ccc;
    }
  }
}
