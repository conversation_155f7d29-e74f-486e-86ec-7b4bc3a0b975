page {
  background-color: #f9f9f9;
}

.content {
  width: 96%;
  margin: 10px auto;
}

.df {
  display: flex;
}

.title {
  padding: 20px 10px 10px 10px;
  position: relative;
  margin: 10px 0;
  border-radius: 5px;
  background-color: #fff;
  box-shadow: 0px 2px 2px 0px #f1f1f1;
}

.shu {
  width: 3px;
  height: 16px;
  margin-right: 5px;
  padding: 2px 0 0 0;
}

.Copay {
  font-weight: 600;
}

.sickPeople {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height:100rpx;
  margin: 10px;
}

.submit {
  position: fixed;
  left: 10%;
  margin-top: 10px;
  bottom: 100rpx;
  width: 80%;
  height: 46px !important;
  line-height: 46px !important;
  background: #EA1306;
  font-size: 36rpx;
  color: #fff;
  text-align: center;
  border-radius: 50rpx !important;
}
