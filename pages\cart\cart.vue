<template>
	<!-- 禁止滚动穿透 -->
	<page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
	<view class="cartView" @touchmove="closeAll" @click="closeAll">
		<view style="padding: 24rpx 26rpx 0 44rpx;background-color: #fff;">
			<view class="mode_tab">
				<view style="width: 320rpx;text-align: center;" :class="modeTab === 1 ? 'active' : ''"
					@click="changeMode(1)">
					自提/闪送(30分钟达)
				</view>
				<view :class="modeTab === 2 ? 'active' : ''" @click="changeMode(2)"
					style="width: 320rpx;text-align: center;">邮寄(1~3日达)
				</view>
			</view>
			<view class="address" @click="goUrl('/other/storeList/storeList')">
				<i class="iconfont" style="color: #EA1306;margin-right: 10rpx;">&#xe60e;</i>
				<text style="color: #4E5969;font-size: 28rpx;">{{ store.address }}</text>
				<i class="iconfont" style="color: #4E5969;font-size: 12rpx;margin-left: 10rpx;">&#xe604;</i>
			</view>
			<view class="cart_tab">
				<view style="display: flex;">
					<view class="tab_item" :class="tabValue === 0 ? 'tab_item_active' : ''" @click="changeListType(0)">
						购物车
					</view>
					<view class="tab_item" :class="tabValue === 1 ? 'tab_item_active' : ''" @click="changeListType(1)">
						我常买
					</view>
				</view>
				<view style="display: flex;" v-if="tabValue === 0">
					<view class="tab_item" style="margin-right: 20rpx;"
						@click="goUrl('/mine/couponList/couponList?formCart=1')">
						优惠券</view>
					<view class="tab_item" @click="manage = !manage" :style="manage ? 'color: #EA1306;' : ''">{{
						manage ? '退出管理' : '管理' }}</view>
				</view>
			</view>
		</view>
		<!-- 购物车 -->
		<view class="cart_" v-if="tabValue === 0 && goodsList.length">
			<!-- 换购 -->
			<view class="cart_trade_in" v-if="false">
				<view class="trade_in_title">
					<view style="display: flex;">
						<view
							style="width: 6rpx;height: 26rpx;border-radius: 4rpx;background: #EA1306;margin-right: 8rpx;">
						</view>
						<view class="title_">全场换购</view>
						<view class="title_tag">满68元换购</view>
					</view>
					<view style="color: #4E5969;font-size: 20rpx;">更多换购<uni-icons type="right" size="10"></uni-icons>
					</view>
				</view>
				<scroll-view scroll-x="true" class="trade_in_goods">
					<view style="display: flex;justify-content: flex-start;">
						<view class="goods_item">
							<image
								src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241118/92edd1c2a6de43e2b051251670c06f50.png"
								style="width: 120rpx;height: 76rpx;margin-bottom: 8rpx;"></image>
							<view class="goods_title">999抗病毒口服液</view>
							<view style="display: flex;justify-content: space-between;align-items: flex-end;">
								<view class="price">
									<view>¥16.9</view>
									<view style="text-decoration: line-through;color: #BFBFBF;">¥18.9</view>
								</view>
								<view class="add_cart"><i class="iconfont " style="font-size: 22rpx;">&#xe60d;</i>
								</view>
							</view>
						</view>
						<view class="goods_item">
							<image
								src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241118/92edd1c2a6de43e2b051251670c06f50.png"
								style="width: 120rpx;height: 76rpx;margin-bottom: 8rpx;"></image>
							<view class="goods_title">999抗病毒口服液</view>
							<view style="display: flex;justify-content: space-between;align-items: flex-end;">
								<view class="price">
									<view>¥16.9</view>
									<view style="text-decoration: line-through;color: #BFBFBF;">¥18.9</view>
								</view>
								<view class="add_cart"><i class="iconfont " style="font-size: 22rpx;">&#xe60d;</i>
								</view>
							</view>
						</view>
						<view class="goods_item">
							<image
								src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241118/92edd1c2a6de43e2b051251670c06f50.png"
								style="width: 120rpx;height: 76rpx;margin-bottom: 8rpx;"></image>
							<view class="goods_title">999抗病毒口服液</view>
							<view style="display: flex;justify-content: space-between;align-items: flex-end;">
								<view class="price">
									<view>¥16.9</view>
									<view style="text-decoration: line-through;color: #BFBFBF;">¥18.9</view>
								</view>
								<view class="add_cart"><i class="iconfont " style="font-size: 22rpx;">&#xe60d;</i>
								</view>
							</view>
						</view>
						<view class="goods_item">
							<image
								src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241118/92edd1c2a6de43e2b051251670c06f50.png"
								style="width: 120rpx;height: 76rpx;margin-bottom: 8rpx;"></image>
							<view class="goods_title">999抗病毒口服液</view>
							<view style="display: flex;justify-content: space-between;align-items: flex-end;">
								<view class="price">
									<view>¥16.9</view>
									<view style="text-decoration: line-through;color: #BFBFBF;">¥18.9</view>
								</view>
								<view class="add_cart"><i class="iconfont " style="font-size: 22rpx;">&#xe60d;</i>
								</view>
							</view>
						</view>
						<view class="goods_item">
							<image
								src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241118/92edd1c2a6de43e2b051251670c06f50.png"
								style="width: 120rpx;height: 76rpx;margin-bottom: 8rpx;"></image>
							<view class="goods_title">999抗病毒口服液</view>
							<view style="display: flex;justify-content: space-between;align-items: flex-end;">
								<view class="price">
									<view>¥16.9</view>
									<view style="text-decoration: line-through;color: #BFBFBF;">¥18.9</view>
								</view>
								<view class="add_cart"><i class="iconfont " style="font-size: 22rpx;">&#xe60d;</i>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
			<!-- 购物车内容 -->
			<view class="cart_content">
				<uni-section title="购物车" type="line">
					<template v-slot:right v-if="false">
						<view style="color: #4E5969;font-size: 28rpx;">去凑单<uni-icons type="right" size="10"></uni-icons>
						</view>
					</template>
					<view class="cart_item">
						<uni-swipe-action ref="cartItem">
							<view style="border-top: 1px solid #F2F3F5;padding: 40rpx 0rpx 30rpx;"
								v-for="(item, index) in goodsList" :key="index">
								<uni-swipe-action-item :right-options="options" @click="deleteGoods(index)"
									:disabled="manage">
									<view style="display: flex;"
										@click="goToDetail({ id: item.productId, type: item.type })">
										<view style="text-align: center;line-height: 136rpx;"
											@click.stop="checked_box(item.id)">
											<i class="iconfont"
												style="color: #EA1306;margin-right: 16rpx;font-size: 36rpx;"
												v-if="checked.includes(item.id)">&#xe610;</i>
											<i class="iconfont"
												style="color: #BFBFBF;margin-right: 16rpx;font-size: 36rpx;"
												v-else>&#xe611;</i>
										</view>
										<view style="display: flex;">
											<view class="cart_img">
												<view
													style="position: absolute;top: 0;left: 0;font-size: 22rpx;color: #fff;padding: 0 5rpx;background-color: #EA1306;border-radius: 8rpx 0 8rpx 0;"
													v-if="item.activityType === 6">
													组合
												</view>
												<view
													style="position: absolute;top: 0;left: 0;font-size: 22rpx;color: #fff;padding: 0 5rpx;background-color: #EA1306;border-radius: 8rpx 0 8rpx 0;"
													v-else-if="item.activityType === 2">
													秒
												</view>
												<view
													style="position: absolute;top: 0;left: 0;font-size: 22rpx;color: #fff;padding: 0 5rpx;background-color: #EA1306;border-radius: 8rpx 0 8rpx 0;"
													v-else-if="item.activityType === 5">
													折
												</view>
												<view
													style="position: absolute;top: 0;left: 0;font-size: 22rpx;color: #fff;padding: 0 5rpx;background-color: #EA1306;border-radius: 8rpx 0 8rpx 0;"
													v-else-if="item.activityType === 4">
													新客
												</view>
												<image :src="item.pic" style="width: 158rpx;height: 136rpx;"
													mode="aspectFit"
													:style="{ filter: item.prescriptionDrug == 1 ? 'blur(5px)' : '' }">
												</image>
												<image
													style="position: absolute;top:0;left:0;width: 158rpx;height: 136rpx;"
													v-if="item.prescriptionDrug == 1"
													src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240111/a338c782689a421b8e9ab0848c3c5c10.png">
												</image>
											</view>
											<view class="cart_title" style="justify-content: space-between;">
												<view class="title_">
													<view
														style="background-color: #0B78FF;width: 50rpx;height: 30rpx;text-align: center;border-radius: 6rpx; margin-right: 12rpx;margin-top: 4rpx;line-height: 30rpx;"
														v-if="item.isOtc">
														<i class="iconfont"
															style="color: #fff;font-size: 28rpx;">&#xe606;</i>
													</view>
													<view class="single-line-text">{{ item.title }}</view>
												</view>
												<view class="cart_num">
													<view class="price">￥{{ item.price1 }}<text
															style="color: #BFBFBF;">/</text><text
															style="text-decoration: line-through;color: #BFBFBF;font-weight: 500;">￥{{
																item.salePrice }}</text>
													</view>
													<view
														style="display: flex;justify-content: space-between;align-items: center;">
														<view
															style="background-color: #F5F5F5;border-radius: 50%;width: 50rpx;height: 50rpx;line-height: 50rpx;text-align: center;"
															@click.stop="jianQuantity(item)">
															<i class="iconfont"
																style="color: #4E5969;font-size: 40rpx;">&#xe60a;</i>
														</view>
														<view
															style="color: #EA1306;font-size: 36rpx;font-weight: 700;width: 60rpx;text-align: center;">
															{{ item.quantity }}
														</view>
														<view
															style="background-color: #E91306;border-radius: 50%;width: 50rpx;height: 50rpx;line-height: 50rpx;text-align: center;"
															@click.stop="jiaQuantity(item)">
															<i class="iconfont"
																style="color: #FFFFFF;font-size: 24rpx;">&#xe634;</i>
														</view>
													</view>
												</view>
												<view v-if="item.estimatePrice"
													style="font-size: 24rpx;color: #EA1306;margin: 4rpx 0;">
													预估价格：￥{{ item.estimatePrice }}</view>
											</view>
										</view>
									</view>
								</uni-swipe-action-item>
							</view>
						</uni-swipe-action>
					</view>
				</uni-section>
			</view>
		</view>
		<!-- 我常买 -->
		<view class="cart_" v-if="tabValue === 1 && MyList.length">
			<view style="display: flex;align-items: center;margin-bottom: 20rpx;">
				<image
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241121/1e55ec59ecef4c8da66098bb0aa8c744.png"
					style="width: 87rpx ;height: 76rpx;margin-right: 20rpx;">
				</image>
				<view class="buy_text">你买过的商品最多次数</view>
			</view>
			<view class="cart_content">
				<uni-section title="我常买" type="line">
					<view class="cart_item">
						<view v-for="(item, index) in MyList" :key="index" class="items">
							<view style="display: flex;" @click="goToDetail(item)">
								<view style="display: flex;">
									<view class="cart_img">
										<image :src="item.img" style="width: 158rpx;height: 136rpx;"
											:style="{ filter: item.prescriptionDrug == 1 ? 'blur(5px)' : '' }"
											mode="aspectFit">
										</image>
										<image style="position: absolute;top:0;left:0;width: 158rpx;height: 136rpx;"
											v-if="item.prescriptionDrug == 1"
											src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240111/a338c782689a421b8e9ab0848c3c5c10.png">
										</image>
									</view>
									<view class="cart_title">
										<view class="title_">
											<view
												style="border: 1rpx solid #0B78FF;width: 50rpx;height: 30rpx;text-align: center;border-radius: 6rpx; margin-right: 12rpx;    margin-top: 4rpx;"
												v-if="item.isOtc">
												<i class="iconfont"
													style="color: #0B78FF;font-size: 28rpx;">&#xe606;</i>
											</view>
											<view>{{ item.goodsName }}</view>
										</view>
										<view class="buy_nums">近期买过{{ item.historyCount }}次</view>
										<view class="cart_num" style=" margin-bottom: 0rpx;">
											<view class="price">￥{{ item.level1Price }}<text
													style="color: #BFBFBF;">/</text><text
													style="text-decoration: line-through;color: #BFBFBF;font-weight: 500;">￥{{
														item.salePrice }}</text>
											</view>
											<view>
											</view>
											<!-- <view
												style="display: flex;width: 120rpx;justify-content: space-between;align-items: center;"
												v-if="isAddCart">
												<view
													style="border: 1rpx solid #F2F3F5;border-radius: 50%;width: 16px;height: 16px;">
													<i class="iconfont" style="color: #4E5969;">&#xe60a;</i>
												</view>
												<view style="color: #EA1306;font-size: 28rpx;">1</view>
												<i class="iconfont" style="color: #E91306;">&#xe609;</i>
											</view> -->
											<view
												style="border: 1px solid #E91306;border-radius: 50%;width: 44rpx;height: 44rpx;text-align: center;line-height: 44rpx;"
												@click.stop="openBuyPopup(item)">
												<i class="iconfont"
													style="color: #E91306;font-size: 38rpx;">&#xe60c;</i>
											</view>
											<!-- <view
												style="display: flex;justify-content: space-between;align-items: center;"
												v-if="false">
												<view class="num">
													<view class="jian" @click="jianQuantity(item)" :data-idx="index">-
													</view>
													<input class="nums" :value="item.quantity" type="number"
														@input="getNum(item, $event)" @blur="minNum(item, $event)"
														:data-idx="index" />
													<view class="jian" @click="jiaQuantity(item)" :data-idx="index">
														+
													</view>
												</view>
											</view> -->
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</uni-section>
			</view>
		</view>
		<!-- 空页面显示 -->
		<view class="empty"
			v-if="(tabValue === 0 && goodsList.length === 0) || (tabValue === 1 && MyList.length === 0)">
			<image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241116/aadc972c0a994af6b9b2b4dc4fc9e4bf.png"
				style="width: 458rpx;height: 338rpx;">
			</image>
			<view class="empty_text">购物车空空如也~</view>
			<view style="display: flex;justify-content: center;">
				<view class="cart_btn" @click="goIndex">去逛逛</view>
			</view>
		</view>
		<!-- 结算 -->
		<uni-popup ref="popup" type="bottom" background-color="#fff" borderRadius="40rpx 40rpx 0px 0px"
			@change="change">
			<view class="popup_content" :style="{ 'padding-bottom': 'calc(124rpx + env(safe-area-inset-bottom))' }">
				<i class="iconfont" style="font-size: 24rpx;position: fixed;right: 40rpx;top: 40rpx;color: #fff;"
					@click="close">&#xeb6a;</i>
				<view class="popup_title">
					<view style="font-weight: 600;">优惠明细</view>
					<view style="font-size: 24rpx;color: #fff;">自动计算最佳优惠，实际以结算页为准</view>
				</view>
				<scroll-view class="popup_details" scroll-y enhanced :show-scrollbar="false">
					<view style="padding-bottom: 10rpx;">
						<view style="background-color: #fff;margin-bottom: 20rpx;border-radius: 16rpx;">
							<view style="display: flex;flex-wrap: wrap;text-align: center;padding: 20rpx 10rpx;">
								<view style="width: 156rpx;margin-right: 16rpx;" v-for="(item, index) in showList"
									:key="index">
									<view style="position: relative;">
										<image :src="item.pic" style="width: 120rpx;height: 76rpx;"
											:style="{ filter: item.prescriptionDrug == 1 ? 'blur(5px)' : '' }">
										</image>
										<image
											style="position: absolute;top:0;left:50%;transform: translateX(-50%);width: 120rpx;height: 76rpx;"
											v-if="item.prescriptionDrug == 1"
											src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240111/a338c782689a421b8e9ab0848c3c5c10.png">
										</image>

									</view>
									<view style="line-height: 32rpx;font-size: 20rpx;">{{ item.title }}</view>
								</view>
								<view
									style="font-size: 24rpx;line-height: 28rpx;text-align: center;width: 100%;color: #999999;margin-top: 20rpx;"
									@click="checkshowMore">
									已选{{ detailInfo.goodsList ? detailInfo.goodsList.length : 0 }}件商品<uni-icons
										:type="showAllGoods ? 'up' : 'down'" size="10"></uni-icons>
								</view>
							</view>
						</view>
						<view style="background-color: #fff;border-radius: 16rpx;padding: 20rpx;margin-bottom: 20rpx;">
							<view class="details">
								<view>商品总价</view>
								<view style="font-weight: 600;">¥{{ detailInfo.sumPrice }}</view>
							</view>
							<view v-if="modeTab == 2" class="details">
								<view>配送费<text class="distribution" v-if="detailInfo.fullSubtractName">{{
									detailInfo.fullSubtractName }}</text></view>
								<view style="font-weight: 600;"><text
										style="text-decoration: line-through;color: #999999;margin-right: 12rpx;">¥{{
											detailInfo.deliveryPrice }}</text>¥{{ chargePrice }}<uni-icons type="right"
										size="14"></uni-icons>
								</view>
							</view>
							<view class="details" @click="goCoupon">
								<view>优惠券<uni-icons type="down" size="10"></uni-icons></view>
								<view style="font-weight: 600;color: #EA1306;font-size: 28rpx;">-¥{{
									detailInfo.couponPrice ? detailInfo.couponPrice : 0.00 }}
								</view>
							</view>
							<view class="details">
								<view>共优惠</view>
								<view style="font-weight: 600;color: #EA1306;font-size: 28rpx;">-¥{{
									detailInfo.discountPrice ? Math.abs(detailInfo.discountPrice) : 0 }}
								</view>
							</view>
							<view class="total_">
								<view>合计金额</view>
								<view style="font-weight: 600;">¥{{ detailInfo.amountPrice }}</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
		<view class="manage" v-if="!manage && goodsList.length">
			<view style="display: flex;justify-content: space-between;align-items:center;">
				<view v-if="checked.length" class="check_btn" style="line-height: 32rpx;align-items: flex-start;"
					@click="selectAll">
					<i class="iconfont" style="color: #EA1306;margin-right: 16rpx;">&#xe610;</i>
					<view style="line-height: 30rpx;">
						<view style="font-weight: 500;font-size: 28rpx;color: #4E5969;">已选{{ checked.length }}件</view>
						<view style="font-size: 20rpx;color: #666666;text-decoration: underline;"
							@click.stop="getDiscount(true)">
							查看详情
						</view>
					</view>
				</view>
				<view v-else-if="!checkedAll" class="check_btn" @click="selectAll">
					<i class="iconfont" style="color:#BFBFBF;margin-right: 16rpx;font-size: 36rpx;">&#xe611;</i>
					<view>全选</view>
				</view>
				<view style="display: flex;">
					<view class="total_text">
						<view>合计：<text style="font-weight: 600;color: #EA1306;">¥<text style="font-size: 32rpx;">{{
							detailInfo.amountPrice ? detailInfo.amountPrice : 0.00 }}</text></text>
						</view>
						<view>
							<!-- <text style="color: #999999;font-size: 20rpx;">免费配送</text> -->
							<text class="preferential_btn" @click="open">优惠明细¥{{
								detailInfo.discountPrice ? detailInfo.discountPrice : 0 }}</text>
						</view>
					</view>
					<view class="manage_btn" style="color: #fff;background: #EA1306;" @click="comfirmOrder">去结算({{
						checked.length }})
					</view>
				</view>
			</view>
		</view>
		<!-- 管理 -->
		<view class="manage" v-if="manage">
			<view style="display: flex;justify-content: space-between;">
				<view class="check_btn" @click="selectAll">
					<i class="iconfont" style="color: #EA1306;margin-right: 16rpx;">{{
						checkedAll ? '&#xe610;' : '&#xe611;' }}</i>全选 {{ checked ? checked.length : 0 }} 件
				</view>
				<view style="display: flex;">
					<!-- <view class="manage_btn" @click="manageShare">分享</view> -->
					<!-- <view class="manage_btn">批量清理</view> -->
					<view class="manage_btn" style="color: #fff;background: #EA1306;" @click="manageDelete">删除</view>
				</view>
			</view>
		</view>
		<!-- 为你推荐 -->
		<view class="Recommended" :style="{ 'padding-bottom': 'calc(124rpx + env(safe-area-inset-bottom))' }">
			<view style="text-align: center;margin-bottom: 20rpx;">
				<image
					src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241116/b7074417575a42eaa8d82c22d3c8ba4b.png"
					style="width: 232rpx;height: 48rpx;">
				</image>
			</view>
			<customWaterfallsFlow ref="waterfallsFlowRef" :value="RecommendedList" @imageClick="goToDetail">
				<view v-for="(item, index) in RecommendedList" :key="index" slot="slot{{index}}">
					<goods-card :data="item" :bottom="goodsList.length" @getList="getList" :refresh="true"></goods-card>
				</view>
			</customWaterfallsFlow>
		</view>
		<!-- 加入购物车 -->
		<buy-popup ref="buyPopup" :is-cart-action="true" :product="{
			image: data.img || '',
			price: data.salePrice || 0,
			name: data.goodsName || '',
			id: data.id || '',
			type: data.type || '',
			isMedicalInsurance: data.isMedicalInsurance,
			isOverallPlanning: data.isOverallPlanning
		}" @confirm="handleBuyConfirm" :cloud-stock="Number(data.warehouseStock) || 0"
			:store-stock="Number(data.saleStock) || 0" @click.stop :bottom="goodsList.length" />
	</view>
</template>

<script>
import customWaterfallsFlow from "@/components/custom-waterfalls-flow/custom-waterfalls-flow" //瀑布流
import goodsCard from "@/components/goods-card/goods-card" //商品卡片
const req = require("../../utils/request.js");
export default {
	data() {
		return {
			show: false,  // 遮罩出现、禁止穿透滚动
			modeTab: 1,   // 配送方式 2快递 1闪送
			merchantId: "",
			store: null,  // 当前门店
			tabValue: 0,  // 0购物车；1我常买；
			isAddCart: false,
			options: [{
				text: '删除',
				style: {
					backgroundColor: '#ea1306'
				}
			}],
			goodsList: [],  // 购物车商品列表
			RecommendedList: [],  // 推荐列表
			MyList: [],     // 常买列表
			checked: [],    // 选中的商品id
			pageNum: 1,
			pageSize: 10,
			manage: false,
			checkedAll: false,  // 全选
			manageAll: false, // 管理全选
			detailInfo: {},
			chargePrice: 0.00,
			data: {},
			showAllGoods: false, // 查看全部商品
			showList: [],
			couponUserId: '',
			swipeLoaded: false, // 添加新的状态标记
			closeAllTimer: null, // 添加防抖timer
		};
	},
	components: {
		customWaterfallsFlow,
		goodsCard
	},
	onLoad() {
		this.checked = [];
	},
	onShow() {
		if (req.getStorage("userInfo")) {
			getApp().globalData.updateCartBadge()
		}
		if (!req.getStorage("currentStore")) {
			this.getDefaultStore();
		};
		this.store = req.getStorage("currentStore");
		this.merchantId = this.modeTab === 1 ? this.store.id : 60;
		if (this.tabValue === 0) {
			this.getList();
		} else if (this.tabValue === 1) {
			this.getMyList();
		}
		// this.getList();
		this.getRecommendedList();
	},
	onReachBottom() {
		this.pageNum++;
		this.getRecommendedList();
	},
	methods: {
		closeAll() {
			// 清除之前的定时器
			if (this.closeAllTimer) {
				clearTimeout(this.closeAllTimer);
			}
			// 使用防抖延迟执行
			this.closeAllTimer = setTimeout(() => {
				if (this.swipeLoaded && this.$refs.cartItem && typeof this.$refs.cartItem.closeAll === 'function') {
					this.$refs.cartItem.closeAll();
				}
			}, 100);
		},
		// 切换配送方式
		changeMode(mode) {
			this.merchantId = mode === 1 ? this.store.id : 60;
			this.modeTab = mode;
			this.checked = [];
			this.checkedAll = false;
			this.tabValue = 0;
			this.detailInfo = {};
			this.couponUserId = '';
			this.getList();
		},
		// 切换购物车、我常买列表数据
		changeListType(type) {
			this.tabValue = type;
			if (type === 0) {
				this.getList();
			} else {
				this.goodsList = [];
				this.getMyList();
			}
		},
		// 购物车商品选中结算
		checked_box(id) {
			console.log(id);
			if (this.checked.includes(id)) {
				console.log("去除");
				this.couponUserId = '';
				this.checked = this.checked.filter(item => item !== id);
				console.log(this.checked);
			} else {
				this.checked.push(id);
			};
			if (this.checked.length === this.goodsList.length) {
				this.checkedAll = true;
			} else {
				// console.log("false");
				this.checkedAll = false;
			};
			if (this.checked.length) {
				this.couponUserId = '';
				this.getDiscount();
			} else {
				this.detailInfo = {};
				this.couponUserId = '';
			}
		},
		// 全选
		selectAll() {
			// console.log("全选");
			this.close();
			this.checkedAll = !this.checkedAll;
			console.log(this.checkedAll);
			if (this.checkedAll) {
				// let goodsList_ = this.goodsList.map(item => item.id);
				// console.log(goodsList_);
				this.checked = this.goodsList.map(item => item.id);
				this.couponUserId = '';
				this.getDiscount();
			} else {
				this.checked = [];
				this.detailInfo = {};
				this.couponUserId = '';
				// this.detailInfo.sumPrice = 0;
				// this.detailInfo.discountPrice = 0;
			};
		},
		// 管理 - 删除
		manageDelete() {
			let ids_ = this.checked.join(",");
			req.msgConfirm('确定删除选中的商品', () => {
				req.postRequest1(`/shopApi/purchase/deletes/${ids_}`, {}, res => {
					console.log(res, "删除");
					this.checked = [];
					this.checkedAll = false;
					this.couponUserId = '';
					getApp().globalData.updateCartBadge()
					this.getList();
					this.getDiscount();
				});
			});
		},
		// 管理 - 分享
		manageShare() { },
		// 商品结算明细 - 打开
		open() {
			this.$refs.popup.open('bottom')
		},
		// 商品结算明细 - 关闭
		close() {
			this.$refs.popup.close();
		},
		// 查看更多商品
		checkshowMore() {
			this.showAllGoods = !this.showAllGoods;
			if (this.showAllGoods) {
				this.showList = [...this.detailInfo.goodsList];
			} else {
				this.showList = this.detailInfo.goodsList.slice(0, 4);
			}
		},
		// 查看优惠券
		goCoupon() {
			let ids_ = this.checked.join(",");
			let that = this;
			// this.goUrl(`/mine/couponList/couponList?formCart=1&cartIds=${ids_}`);
			uni.navigateTo({
				url: `/mine/couponList/couponList?formCart=1&cartIds=${ids_}&merchantId=${this.merchantId}`,
				events: {
					// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
					acceptDataFromOpenedPage: function (data) {
						console.log(data, "页面传递");
						that.couponUserId = data.id&&data.id.length>0?data.id.join(','):data.id;
						that.getDiscount();
					},
				},
			})
		},
		// 去结算 - 确认订单
		comfirmOrder() {
			console.log(this.checked);
			if (!this.checked.length) {
				return req.msg("请选择结算商品")
			};
			let list_ = this.goodsList.filter(item => this.checked.includes(item.id));
			console.log(list_);
			let result = this.checkProperties(list_, 'prescriptionDrug');  // 含有处方药 则返回false; 不含处方药则返回true;
			uni.navigateTo({
				url: `/shoppingCart/confirmOrder/confirmOrder?prescriptionDrug=${result}&mode=${this.modeTab}&goodslist=${this.checked}&merchantId=${this.merchantId}&discountType=1&couponId=${this.couponUserId}`
			});
		},
		// 商品减少
		jianQuantity(item) {
			let num = item.quantity;
			num--;
			if (num <= 0) {
				req.msg("商品数量不能再减少了");
				item.quantity = 1;
				// req.msgConfirm('确定删除该商品', () => {
				// 	let ids_ = item.id;
				// 	req.postRequest1(`/shopApi/purchase/deletes/${ids_}`, {}, res => {
				// 		console.log(res, "删除");
				// 		this.checked = this.checked.filter(item => item !== ids_);
				// 		this.getList();
				// 		this.getDiscount();
				// 	});
				// });
			} else {
				item.quantity = num;
				this.updateDuantity(item);
			}
		},
		// 商品增加
		jiaQuantity(item) {
			let num = item.quantity;
			let originalNum = item.quantity;
			num++;
			item.quantity = num;
			// if (item.quantity > item.saleStock && item.purchaseNo === 1) {
			// 	item.quantity = item.saleStock;
			// 	return req.msg(`该药品库存数量最大为${item.saleStock}`);
			// };
			this.updateDuantity(item, originalNum);
		},
		// 商品数量输入
		getNum(item, event) {
			event.stopPropagation();  // 阻止冒泡
			let num = event.detail.value;
			let originalNum = item.quantity;
			if (num <= 0 || num === '') {
				return num = 1
			};
			item.quantity = num;
			if (num > 0) {
				this.updateDuantity(item, originalNum);
			}
		},
		minNum(item, event) {
			event.stopPropagation(); // 阻止冒泡
			let originalNum = item.quantity;
			let num = event.detail.value;
			if (num <= 0 || num == '') {
				return num = 1
			};
			item.quantity = num;
			if (num > 0) {
				this.updateDuantity(item, originalNum);
			};
		},
		// 修改购物车商品数量 - 方法
		updateDuantity(item, originalNum) {
			req.postRequest('/shopApi/purchase/quantity', {
				id: item.id,
				quantity: item.quantity,
				mode: this.modeTab,
				merchantId: item.merchantId
			}, res => {
				console.log(res, "修改商品1111");
				if (res.code == 200) {
					getApp().globalData.updateCartBadge()
				}
				if (res.code == 500) {
					console.log('2222');
					item.quantity = originalNum;
					return req.msg(res.msg);
				};
				if (this.checked.length != 0) {
					this.couponUserId = '';
					this.getDiscount();
				};
			}, true)
		},
		// 删除购物车商品
		deleteGoods(index) {
			let ids_ = this.goodsList[index].id;
			req.msgConfirm('确定删除该商品', () => {
				req.postRequest1(`/shopApi/purchase/deletes/${ids_}`, {}, res => {
					console.log(res, "删除");
					this.$refs.cartItem.closeAll();
					this.checked = this.checked.filter(item => item !== ids_);
					getApp().globalData.updateCartBadge()
					this.getList();
					this.couponUserId = '';
					this.getDiscount();
				});
			});
		},
		openBuyPopup(item) {
			this.data = item;
			this.$refs.buyPopup.open();
		},
		// 添加购物车
		handleBuyConfirm({ quantity, deliveryType, isCartAction }) {
			req.postRequest("/shopApi/purchase/cart", {
				skuId: null,
				merchantId: this.merchantId,
				quantity,
				productId: this.data.id,
				mode: deliveryType,
				state: 0,
				actId: this.data.actId ? this.data.actId : ""
			}, res => {
				req.msg('加入购物车成功')
				getApp().globalData.updateCartBadge()
			})
		},
		// 查看结算优惠明细
		getDiscount(open) {
			if (open) {
				this.open();
			};
			let ids_ = this.checked.join(",");
			if (!ids_) {
				this.detailInfo = {};
				this.couponUserId = '';
				return;
			}
			req.getRequest('/shopApi/purchase/discountDetail', {
				ids: ids_,
				// discountType: 1,
				merchantId: this.merchantId,
				mode: this.modeTab,
				// couponUserId: this.couponUserId
				couponUserIds: this.couponUserId
			}, res => {
				console.log(res, "优惠明细");
				this.detailInfo = res.data;
				if (this.showAllGoods) {
					this.showList = [...this.detailInfo.goodsList];
				} else {
					this.showList = this.detailInfo.goodsList.slice(0, 4);
				}
				this.chargePrice = (this.detailInfo.deliveryPrice - this.detailInfo.deliverySubtractPrice).toFixed(2);
				this.detailInfo.deliveryPrice = this.detailInfo.deliveryPrice ? this.detailInfo.deliveryPrice.toFixed(2) : '0.00';
				this.couponUserId = res.data.couponUserIdList&&res.data.couponUserIdList.length>0 ? res.data.couponUserIdList.join(',') :res.data.couponUserId?res.data.couponUserId:'';
			})
		},
		// 获取购物车列表
		getList() {
			req.showLoading();
			req.getRequest('/shopApi/purchase/list', {
				mode: this.modeTab,
				merchantId: this.merchantId,
				isDirect: 0
			}, res => {
				console.log(res, "购物车列表");
				this.goodsList = [...res.data];
				uni.hideLoading();
			})
		},
		// 获取我常买列表
		getMyList() {
			req.getRequest('/shopApi/product/constantGoodsList', {
				// mode: this.modeTab
				merchantId: this.merchantId
			}, res => {
				console.log(res, "我常买列表");
				this.MyList = [...res.data];
			})
		},
		// 获取为你推荐列表
		getRecommendedList() {
			req.getRequest("/shopApi/home/<USER>", {
				storeId: this.merchantId,
				pageNum: this.pageNum,
				pageSize: this.pageSize
			}, res => {
				console.log(res, "为你推荐列表");
				res.data.list.forEach(item => {
					item.image = item.img
				})
				this.$nextTick(() => {
					this.RecommendedList = [...this.RecommendedList, ...res.data.list]
				})
			})
		},
		//获取默认门店
		getDefaultStore() {
			req.getRequest("/shopApi/home/<USER>", {}, res => {
				this.store = res.data
				req.setStorage("currentStoreId", res.data.id);
				req.setStorage("currentStore", res.data);
			})
		},
		// 去逛逛
		goIndex() {
			uni.switchTab({
				url: '/pages/index/index'
			});
		},
		goToDetail(item) {
			console.log(item);
			if (!item || !item.id) {
				req.msg('商品信息不完整')
				return
			}
			let url = `/product/detail/detail?id=${item.id}`
			if (item.type) {
				url += `&type=${item.type}`
			}
			// console.log(url);
			this.goUrl(url);
		},
		// 禁止穿透滚动 - 遮罩
		change(e) {
			this.show = e.show;
		},
		goUrl(url) {
			uni.navigateTo({
				url: url
			})
		},
		// 查看列表属性是否处方药 - 方法 
		checkProperties(arr, key) {
			for (let i = 0; i < arr.length; i++) {
				const obj = arr[i];
				// 遍历对象的每一个属性
				if (obj.hasOwnProperty(key)) {
					const value = obj[key];
					//  如果属性值为1 
					if (value === 1 || (typeof value === "string" && value.trim() === "1")) {
						return false;
					}
				}
			}
			return true; //  如果属性值不为1 
		}
	},
	mounted() {
		// 在组件挂载后标记swipe组件已加载
		this.$nextTick(() => {
			this.swipeLoaded = true;
		});
	},
}
</script>

<style lang="scss" scoped src="./cart.scss"></style>
