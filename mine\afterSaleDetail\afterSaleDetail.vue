<template>
  <view class="after-sale-detail">
    <!-- 售后状态 -->
    <view class="status-section">
      <view class="status-text">{{ refundInfo.stateName }}</view>
    </view>

    <!-- 商品信息 -->
    <view class="goods-section">
      <view class="section-title">退款商品</view>
      <view class="goods-item" v-for="(item, index) in refundInfo.listRefundProduct" :key="index">
        <image :src="item.pic" mode="aspectFill" class="goods-img"></image>
        <view class="goods-info">
          <view class="goods-name">{{ item.productName }}</view>
          <view class="goods-price">¥{{ item.payMoney }}</view>
          <view class="goods-quantity">x{{ item.quantity }}</view>
        </view>
      </view>
    </view>

    <!-- 退款信息 -->
    <view class="refund-section">
      <view class="section-title">退款信息</view>
      <view class="info-item">
        <text class="label">订单号</text>
        <text class="value">{{ refundInfo.orderId }}</text>
      </view>
      <view class="info-item">
        <text class="label">退款类型</text>
        <text class="value">{{ refundInfo.refundType === 1 ? '仅退款' : '退货退款' }}</text>
      </view>
      <view class="info-item">
        <text class="label">退款原因</text>
        <text class="value">{{ refundInfo.refundReasonName }}</text>
      </view>
      <view class="info-item">
        <text class="label">退款金额</text>
        <text class="value">¥{{ refundInfo.refundMoney }}</text>
      </view>
      <view class="info-item">
        <text class="label">申请时间</text>
        <text class="value">{{ refundInfo.createDate }}</text>
      </view>
      <view class="info-item" v-if="refundInfo.remarks">
        <text class="label">问题描述</text>
        <text class="value">{{ refundInfo.remarks }}</text>
      </view>
    </view>

    <!-- 凭证图片 -->
    <view class="images-section" v-if="refundInfo.imgUrl">
      <view class="section-title">凭证图片</view>
      <view class="image-list">
        <image v-for="(img, index) in imageList" :key="index" :src="img" mode="aspectFill" @click="previewImage(index)">
        </image>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view v-if="refundInfo.refundState != 43" class="bottom-btn">
      <view class="cancel-btn" @click="handleCancel">取消售后</view>
    </view>
  </view>
</template>

<script>
const req = require('../../utils/request')

export default {
  data() {
    return {
      refundId: '',
      refundInfo: {},
      imageList: []
    }
  },

  onLoad(options) {
    if (options.id) {
      this.refundId = options.id
      this.getRefundDetail()
    }
  },

  methods: {
    getRefundDetail() {
      req.showLoading()
      req.getRequest('/shopApi/mp/refund/detail', {
        id: this.refundId
      }, res => {
        uni.hideLoading()
        if (res.code === 200) {
          this.refundInfo = res.data
          if (this.refundInfo.imgUrl) {
            this.imageList = this.refundInfo.imgUrl.split(',')
          }
        } else {
          req.msg(res.msg)
        }
      })
    },

    previewImage(index) {
      uni.previewImage({
        urls: this.imageList,
        current: this.imageList[index]
      })
    },

    handleCancel() {
      uni.showModal({
        title: '提示',
        content: '确定要取消售后申请吗？',
        success: (res) => {
          if (res.confirm) {
            this.cancelRefund()
          }
        }
      })
    },

    cancelRefund() {
      req.showLoading()
      req.getRequest('/shopApi/mp/refund/cancel', {
        refundId: this.refundInfo.id
      }, res => {
        uni.hideLoading()
        if (res.code === 200) {
          req.msg('取消成功')
          // 刷新页面数据
          setTimeout(() => {
            uni.navigateBack()
          }, 1500);
        } else {
          req.msg(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import './afterSaleDetail.scss';
</style>