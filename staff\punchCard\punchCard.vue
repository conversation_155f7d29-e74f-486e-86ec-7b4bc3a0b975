<template>
	<view class="punch-card-container">
		<view v-if="formIndex!=1">
			<!-- 顶部时间显示 -->
			<view class="time-display">
				<view class="current-time">{{ currentTime }}</view>
				<view class="current-date">{{ currentDate }}</view>
			</view>

			<!-- 定位信息显示 -->
			<view class="location-info">
				<text class="iconfont location-icon">&#xe632;</text>
				<text class="location-text">{{ location.address || '正在获取位置...' }}</text>
				<text class="refresh-btn" @click="getLocation">刷新</text>
			</view>

			<!-- 打卡照片上传 -->
			<view class="photo-upload">
				<view class="photo-title">打卡照片</view>
				<view class="photo-container">
					<view v-if="photoUrl.length > 0" style="display: flex;flex-wrap: wrap;align-items: center;">
						<view class="photo-preview" v-for="(item, index) in photoUrl" :key="index">
								<image :src="item" mode="aspectFill"></image>
								<text class="delete-icon" @click="deletePhoto(index)">×</text>
						</view>
					</view>
					<view class="upload-btn" @click="takePhoto">
						<text class="iconfont">&#xe665;</text>
						<text>拍照打卡</text>
					</view>
				</view>
			</view>

			<!-- 打卡按钮 -->
			<view class="punch-btn" @click="submitPunchCard" :style="{ backgroundColor: photoUrl.length > 0 ? '#EA1306' : '#f8f8f8',color: photoUrl.length > 0 ? '#fff' : '#999' }">
				打卡
				</view>
		</view>
		<!-- 打卡记录 -->
		<view class="record-list">
			<!-- <view class="record-title">打卡记录</view> -->
			<uni-section title="打卡记录" type="line"></uni-section>
			<view class="record-item" v-for="(item, index) in recordList" :key="index">
				<view class="record-header">
					<text class="record-date">{{ item.createTime }}</text>
					<!-- <text class="record-status" :style="{ color: item.status === 1 ? '#67C23A' : '#EA1306' }">
						{{ item.status === 1 ? '正常' : '异常' }}
					</text> -->
				</view>
				<view class="record-address">
					<text class="iconfont" style="color: #EA1306;margin-right: 10rpx;">&#xe632;</text>
					<text>{{ item.address }}</text>
				</view>
				<view class="record-photos" v-if="item.pic">
					<image v-for="(img, imgIndex) in JSON.parse(item.pic)" 
						:key="imgIndex" 
						:src="img" 
						mode="aspectFill"
						@click="previewImage(JSON.parse(item.pic), imgIndex)">
					</image>
				</view>
			</view>
			<view v-if="recordList.length === 0" class="empty-record">
				<image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png" mode="aspectFit"></image>
				<text style="display: block;">暂无打卡记录</text>
			</view>
		</view>
	</view>
</template>

<script>
const req = require("../../utils/request");
export default {
	data() {
		return {
			currentTime: '',
			currentDate: '',
			location: {
				latitude: '',
				longitude: '',
				address: ''
			},
			photoUrl: [],
			timer: null,
			id: 0,
			recordList: [],
			formIndex:""
		}
	},
	onLoad(options) {
		console.log(options);
		
		this.id = Number(options.id);
		this.formIndex =options.formIndex? Number(options.formIndex):"";
		this.startTimer();
		this.getLocation();
		this.getRecordList();
	},
	onUnload() {
		if (this.timer) {
			clearInterval(this.timer);
		}
	},
	methods: {
		// 开始计时器更新时间
		startTimer() {
			this.updateTime();
			this.timer = setInterval(() => {
				this.updateTime();
			}, 1000);
		},
		// 更新时间显示
		updateTime() {
			const now = new Date();
			// 格式化时间，避免显示 GMT+0800(CST)
			const hours = now.getHours().toString().padStart(2, '0');
			const minutes = now.getMinutes().toString().padStart(2, '0');
			const seconds = now.getSeconds().toString().padStart(2, '0');
			this.currentTime = `${hours}:${minutes}:${seconds}`;
			
			// 格式化日期
			const year = now.getFullYear();
			const month = (now.getMonth() + 1).toString().padStart(2, '0');
			const date = now.getDate().toString().padStart(2, '0');
			const weekDay = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][now.getDay()];
			this.currentDate = `${year}年${month}月${date}日 ${weekDay}`;
		},
		// 获取定位
		getLocation() {
			req.showLoading('获取位置中...');
			req.getLocationInfo(res => {
				this.location.address = res.address;
				console.log(res, 'res');
				uni.hideLoading();
			});
		},
		// 拍照上传
		takePhoto() {
			uni.chooseImage({
				sourceType: ['camera'],
				success: (res) => {
					req.showLoading('上传中...');
					const tempFilePaths = res.tempFilePaths;
                    console.log(tempFilePaths);
                    this.photoUrl = [...this.photoUrl, ...tempFilePaths];
                    this.photoUrl.forEach((item, index) => {
                        req.uploadImg('/shopApi/ask/uploads', item, res => {
                            this.photoUrl[index] = res[0];
                            if (this.photoUrl.length === index + 1) {
                                uni.hideLoading();
                                console.log(this.photoUrl);
                            };
                        });
                    });
				}
			});
		},
		// 删除照片
		deletePhoto(index) {
			this.photoUrl.splice(index, 1);
		},
		// 提交打卡
		submitPunchCard() {
			if (!this.location.address) {
				req.msg('请等待位置信息获取完成');
				return;
			}
			if (!this.photoUrl.length) {
				req.msg('请先拍照打卡');
				return;
			}
			req.showLoading('提交中...');
			req.postRequest('/shopApi/goOut/outClockIn', {
				employeeGoOutId  : this.id,
				address: this.location.address,
				pic: JSON.stringify(this.photoUrl)
			}, res => {
				uni.hideLoading();
				req.msg('打卡成功');
				this.photoUrl = [];
				this.getRecordList();
			});
		},
		// 获取打卡记录
		getRecordList() {
			req.showLoading('加载中');
			req.getRequest('/shopApi/goOut/outClockInDetail', {
				id: this.id,
			}, res => {
				this.recordList = res.data;	
				uni.hideLoading();
			});
		},
		// 预览图片
		previewImage(urls, current) {
			uni.previewImage({
				urls: urls,
				current: current
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.punch-card-container {
	padding: 30rpx;
	
	.time-display {
		text-align: center;
		margin-bottom: 40rpx;
		
		.current-time {
			font-size: 60rpx;
			font-weight: bold;
			color: #333;
		}
		
		.current-date {
			font-size: 28rpx;
			color: #666;
			margin-top: 10rpx;
		}
	}
	
	.location-info {
		background: #f8f8f8;
		padding: 20rpx;
		border-radius: 10rpx;
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;
		
		.location-icon {
			font-size: 36rpx;
			color: #EA1306;
			margin-right: 10rpx;
		}
		
		.location-text {
			flex: 1;
			font-size: 28rpx;
			color: #333;
		}
		
		.refresh-btn {
			color: #ff6b35;
			font-size: 24rpx;
			padding: 10rpx;
		}
	}
	
	.photo-upload {
		margin-bottom: 40rpx;
		
		.photo-title {
			font-size: 28rpx;
			color: #333;
			margin-bottom: 20rpx;
		}
		
		.photo-container {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			.photo-preview {
				width: 200rpx;
				height: 200rpx;
				position: relative;
				margin-right: 20rpx;
				margin-bottom: 20rpx;
				image {
					width: 100%;
					height: 100%;
					border-radius: 10rpx;
				}
				
				.delete-icon {
					position: absolute;
					top: -20rpx;
					right: -20rpx;
					width: 40rpx;
					height: 40rpx;
					line-height: 36rpx;
					text-align: center;
					background: rgba(0,0,0,0.5);
					color: #fff;
					border-radius: 50%;
				}
			}
			
			.upload-btn {
				width: 200rpx;
				height: 200rpx;
				background: #f8f8f8;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				border-radius: 10rpx;
				margin-bottom: 20rpx;
				.iconfont {
					font-size: 48rpx;
					color: #999;
					margin-bottom: 10rpx;
				}
				
				text {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
	
	.punch-btn {
		background: #EA1306;
		color: #fff;
		height: 88rpx;
		line-height: 88rpx;
		text-align: center;
		border-radius: 44rpx;
		font-size: 32rpx;
		margin-top: 60rpx;
		margin-bottom: 40rpx;
		
		&:active {
			opacity: 0.8;
		}
	}

	.record-list {
		.record-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 20rpx;
		}

		.record-item {
			background: #fff;
			border-radius: 10rpx;
			padding: 20rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);

			.record-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 10rpx;

				.record-date {
					font-size: 28rpx;
					color: #333;
				}

				.record-status {
					font-size: 24rpx;
				}
			}

			.record-address {
				display: flex;
				align-items: center;
				font-size: 26rpx;
				color: #666;
				margin-bottom: 10rpx;
			}

			.record-photos {
				display: flex;
				flex-wrap: wrap;
				margin-top: 10rpx;

				image {
					width: 160rpx;
					height: 160rpx;
					margin-right: 10rpx;
					margin-bottom: 10rpx;
					border-radius: 8rpx;
				}
			}
		}

		.empty-record {
			text-align: center;
			padding: 40rpx 0;

			image {
				width: 240rpx;
				height: 240rpx;
				margin-bottom: 20rpx;
			}

			text {
				font-size: 28rpx;
				color: #999;
			}
		}
	}
}

  /deep/.uni-section {
    border-radius: 8rpx;
  }

  /deep/.uni-section .uni-section-header__decoration {
    background-color: #ea1306;
  }

  /deep/.uni-section__content-title {
    font-size: 32rpx !important;
  }
</style>
