.personal-center {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;

  .user-info {
    background-color: #ee3f3f;
    border-radius: 20rpx;
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #fff;

    .left {
      display: flex;
      align-items: center;

      .avatar {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }

      .user-detail {
        .name {
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 10rpx;
        }

        .recommender {
          font-size: 28rpx;
        }
      }
    }

    .promote-btn {
      display: flex;
      align-items: center;
      font-size: 28rpx;

      image {
        width: 40rpx;
        margin-right: 10rpx;
      }
    }
  }

  .data-statistics,
  .target-status {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-top: 20rpx;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 30rpx;
      position: relative;
      padding-left: 20rpx;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 30rpx;
        background-color: #ee3f3f;
      }
    }
  }

  .statistics-list {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    .statistics-item {
      text-align: center;
      width: 25%;
      margin-bottom: 30rpx;

      image {
        width: 80rpx;
        margin-bottom: 10rpx;
      }

      .value {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        margin: 10rpx 0;
      }

      .label {
        font-size: 26rpx;
        color: #666;
      }
    }
  }

  .target-list {
    .target-item {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .medal {
        width: 60rpx;
        margin-right: 20rpx;
      }

      .target-info {
        font-size: 28rpx;
        color: #333;

        text {
          color: #ee3f3f;
        }
      }
    }
  }
}
