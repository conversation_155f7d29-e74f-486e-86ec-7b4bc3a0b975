<template>
    <view>
        <web-view :src="url"></web-view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            url: ''
        }
    },
    onLoad(options) {
        options = JSON.parse(options.obj)
        this.url = options.url + `?type=${options.type}&name=${options.name}&hideNav=true`
        console.log(this.url);
    }

}
</script>

<style lang="scss" scoped></style>