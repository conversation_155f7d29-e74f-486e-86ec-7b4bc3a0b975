<template>
    <view class="card-replace">
        <!-- 顶部日期选择和筛选 -->
        <view class="header">
            <uni-datetime-picker v-model="date" type="daterange" @change="dateChange" />
            <uni-data-select v-model="state" :clear="false" :localdata="stateList"
                @change="stateChange"></uni-data-select>
            <view @click=" $refs.popup.open()" class="add-btn">
                <text style="font-size: 28rpx;">新增</text>
            </view>
        </view>

        <!-- 申请列表 -->
        <view class="application-list">
            <view class="application-item" v-for="(item, index) in applications" :key="index">
                <view v-if="item.state == 0" class="status-tag" style="background: #ff6b35;">审批中</view>
                <view v-if="item.state == 1" class="status-tag" style="background: #67c23a;">已通过</view>
                <view v-if="item.state == 2" class="status-tag" style="background: #f23030;">已拒绝</view>
                <view v-if="item.state == 3" class="status-tag" style="background: #f23030;">已取消</view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe62b;</text>
                    <text class="label">所属部门：</text>
                    <text class="value">{{ formData.department }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe62b;</text>
                    <text class="label">申请人：</text>
                    <text class="value">{{ item.userId }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe77c;</text>
                    <text class="label">申请时间：</text>
                    <text class="value">{{ item.applyTime }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe77c;</text>
                    <text class="label">原岗位：</text>
                    <text class="value">{{ formData.oldPost }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe656;</text>
                    <text class="label">调职岗位：</text>
                    <text class="value">{{ item.post }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                    <text class="label">调职时间：</text>
                    <text class="value">{{ item.applyTime }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                    <text class="label">调职原因：</text>
                    <text class="value">{{ item.reason }}</text>
                </view>
                <!-- <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                    <text class="label">审批人：</text>
                    <text class="value">{{ item.reason }}</text>
                </view> -->
            </view>
            <view v-if="applications.length === 0" style="text-align: center;color: #999;">
                <image mode=""
                    src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png"
                    style="width: 372rpx;height: 312rpx;margin-top: 50rpx;">
                </image>
                <view style="margin-top: 10rpx;font-size: 28rpx;">暂无数据</view>
            </view>
        </view>
        <uni-popup :is-mask-click="false" ref="popup" type="bottom" border-radius="10px 10px 0 0">
            <view class="popup-content">
                <view class="popup-header">
                    <text>调职申请</text>
                    <text class="close-icon" @click="$refs.popup.close()">×</text>
                </view>
                <view class="popup-body">
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">所属部门：</text>
                        <input type="text" v-model="formData.department" class="input" @input="departmentChange"
                            placeholder="请输入所属部门" />
                        <!-- 搜索结果列表 -->
                        <view class="leader-list" v-if="departmentList.length > 0">
                            <view class="leader-item" v-for="(item, index) in departmentList" :key="index"
                                @click="selectDepartment(item)">
                                {{ item.divName }}
                            </view>
                        </view>
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">申请人：</text>
                        <input type="text" disabled v-model="formData.name" class="input"
                            style="background: #f5f5f5;" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">原岗位：</text>
                        <input type="text" v-model="formData.oldPost" class="input" disabled
                            style="background: #f5f5f5;" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">调职岗位：</text>
                        <input type="text" v-model="formData.newPost" class="input" placeholder="请输入调职岗位"
                            @focus="postChange" />
                        <!-- 搜索结果列表 -->
                        <view class="leader-list" v-if="postList.length > 0">
                            <view class="leader-item" v-for="(item, index) in postList" :key="index"
                                @click="selectPost(item)">
                                {{ item.postName }}
                            </view>
                        </view>
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">调职时间：</text>
                        <uni-datetime-picker v-model="formData.date" type="date">
                            <view class="date-input">
                                <view
                                    style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
                                    <text class="iconfont"
                                        style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
                                </view>
                                <text>{{ formData.date || '请选择' }}</text>
                            </view>
                        </uni-datetime-picker>
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">调职原因：</text>
                        <textarea v-model="formData.reason" placeholder="请输入申请原因" class="textarea" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">审批人：</text>
                        <input type="text" v-model="formData.leader" placeholder="请输入审批人" class="input"
                            @input="leaderChange" />
                        <!-- 搜索结果列表 -->
                        <view class="leader-list" v-if="leaderList.length > 0">
                            <view class="leader-item" v-for="(item, index) in leaderList" :key="index"
                                @click="selectLeader(item)">
                                {{ item.name }}
                            </view>
                        </view>
                    </view>
                    <view class="form-footer">
                        <view class="btn save" @click="saveForm">提交</view>
                        <view class="btn cancel" @click="$refs.popup.close()">取消</view>
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const req = require("../../utils/request");

export default {
    data() {
        return {
            date: [],
            state: 4,
            stateList: [
                { value: 4, text: "全部" },
                { value: 0, text: "审核中" },
                { value: 1, text: "已通过" },
                { value: 2, text: "已拒绝" },
            ],
            typeList: [
                { value: 1, text: "药师补贴" },
                { value: 2, text: "驻点补贴" },
                { value: 3, text: "社保补贴" },
                { value: 4, text: "住宿补贴" },
                { value: 5, text: "通讯补贴" },
                { value: 6, text: "其他补贴" }
            ],
            selectedStatus: '',
            applications: [],
            formData: {
                department: req.getStorage('userInfo').divName,
                departmentId: req.getStorage('userInfo').divId,
                name: req.getStorage('userInfo').staffName,
                oldPost: req.getStorage('userInfo').postName,
                date: "",
                reason: '',
                leader: '',
                type: ''
            },
            pageNum: 1,
            total: 0,
            timer: null,
            timer1: null,
            timer2: null,
            leaderList: [],
            departmentList: [],
            postList: []
        }
    },
    onLoad() {
        this.getData()
    },
    onReachBottom() {
        this.pageNum++
        this.getData()
    },
    methods: {
        departmentChange(e) {
            const value = e.detail.value.trim();

            if (this.timer1) {
                clearTimeout(this.timer1);
                this.timer1 = null;
            }

            if (!value) {
                this.departmentList = [];
                return;
            }

            this.timer1 = setTimeout(() => {
                req.getRequest("/shopApi/transfer/getDicsionLists", {
                    search: value,
                    staffId: req.getStorage('userInfo').introductionId,
                }, res => {
                    this.departmentList = res.data;
                });
            }, 500);
        },
        selectDepartment(item) {
            this.formData.department = item.divName;
            this.formData.departmentId = item.id;
            this.departmentList = [];
        },
        leaderChange(e) {
            const value = e.detail.value.trim();

            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }

            if (!value) {
                this.leaderList = [];
                return;
            }

            this.timer = setTimeout(() => {
                req.getRequest("/shopApi/replacement/getStaff", {
                    name: value
                }, res => {
                    this.leaderList = res.data.items;
                });
            }, 500);
        },
        selectLeader(item) {
            this.formData.leader = item.name;
            this.formData.leaderId = item.id;
            this.leaderList = [];
        },
        postChange(e) {
            // const value = e.detail.value.trim();
            req.getRequest("/shopApi/preferment/postList1", {
                divId: this.formData.departmentId
            }, res => {
                this.postList = res.data;
            });
        },
        selectPost(item) {
            this.formData.newPost = item.postName;
            this.formData.newPostId = item.id;
            this.postList = [];
        },
        stateChange(e) {
            this.state = e
            this.pageNum = 1
            this.getData()
        },
        dateChange(e) {
            this.date = e
            this.pageNum = 1
            this.$nextTick(() => {
                this.getData();
            })
        },
        getData() {
            req.showLoading();
            req.getRequest("/shopApi/transfer/list", {
                pageNum: this.pageNum,
                pageSize: 10,
                state: this.state == 4 ? null : this.state,
                startTime: this.date[0],
                endTime: this.date[1],
                staffId: req.getStorage('userInfo').introductionId,
                userId: req.getStorage('userInfo').staffName,
            }, res => {
                uni.hideLoading()
                this.total = res.total
                if (this.pageNum === 1) {
                    this.applications = res.data.items
                } else {
                    this.applications = [...this.applications, ...res.data.items]
                }
            })
        },
        saveForm() {
            if (!this.formData.department) {
                uni.showToast({
                    title: '请输入所属部门',
                    icon: 'none'
                });
                return;
            }
            if (!this.formData.newPost) {
                uni.showToast({
                    title: '请输入调职岗位',
                    icon: 'none'
                });
                return;
            }
            if (!this.formData.date) {
                uni.showToast({
                    title: '请选择调职时间',
                    icon: 'none'
                });
                return;
            }
            if (!this.formData.reason) {
                uni.showToast({
                    title: '请输入调职原因',
                    icon: 'none'
                });
                return;
            }
            if (!this.formData.leaderId) {
                uni.showToast({
                    title: '请输入审批人',
                    icon: 'none'
                });
                return;
            }
            // TODO: 调用接口保存数据
            req.showLoading()
            req.postRequest("/shopApi/transfer/add", {
                userId: req.getStorage('userInfo').staffName,
                reason: this.formData.reason,
                dept: this.formData.departmentId,
                post: this.formData.newPostId,
                applyTime: this.formData.date,
                deptleader: this.formData.leader
            }, res => {
                uni.hideLoading()
                this.formData = {
                    department: req.getStorage('userInfo').divName,
                    departmentId: req.getStorage('userInfo').divId,
                    name: req.getStorage('userInfo').staffName,
                    oldPost: req.getStorage('userInfo').postName,
                    date: "",
                    reason: '',
                    leader: '',
                    type: ''
                }
                this.pageNum = 1
                this.getData()
                this.$refs.popup.close()
                req.msg('提交成功')
            })
        }
    }
}
</script>
<style lang="scss" scoped src="./transfer.scss"></style>
