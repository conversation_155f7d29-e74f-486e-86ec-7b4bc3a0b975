.after-sale {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 140rpx;

  .form-section {
    background: #fff;
    padding: 30rpx;
    margin: 20rpx;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

    .goods-info {
      display: flex;
      align-items: center;
      padding: 30rpx;
      margin-bottom: 20rpx;
      background: #fafafa;
      border-radius: 16rpx;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      .checkbox-wrapper {
        padding: 0 20rpx;

        .checkbox {
          width: 44rpx;
          height: 44rpx;
          border: 3rpx solid #e0e0e0;
          border-radius: 50%;
          position: relative;
          transition: all 0.2s ease;

          &.checked {
            background: linear-gradient(135deg, #ff6b6b, #ff4444);
            border-color: transparent;

            &::after {
              content: '';
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              width: 22rpx;
              height: 22rpx;
              background: #fff;
              border-radius: 50%;
            }
          }
        }
      }

      image {
        width: 180rpx;
        height: 180rpx;
        border-radius: 12rpx;
        margin-right: 24rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
      }

      .goods-detail {
        flex: 1;

        .goods-name {
          font-size: 28rpx;
          line-height: 40rpx;
          font-weight: 600;
          margin-bottom: 12rpx;
          color: #333;
        }

        .goods-spec {
          font-size: 24rpx;
          color: #666;
          margin-bottom: 12rpx;
          background: #f0f0f0;
          padding: 4rpx 12rpx;
          border-radius: 6rpx;
          display: inline-block;
        }

        .goods-price {
          color: #ff4444;
          font-size: 34rpx;
          font-weight: 600;
          margin-bottom: 8rpx;
        }

        .goods-quantity {
          font-size: 26rpx;
          color: #666;
        }
      }
    }

    .form-item {
      padding: 30rpx 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 24rpx;
        font-weight: 500;
      }

      .type-options {
        display: flex;
        gap: 20rpx;

        .type-item {
          padding: 16rpx 32rpx;
          border: 2rpx solid #e0e0e0;
          border-radius: 40rpx;
          font-size: 26rpx;
          color: #666;
          transition: all 0.3s ease;

          &.active {
            background: linear-gradient(135deg, #ff6b6b, #ff4444);
            color: #fff;
            border-color: transparent;
            box-shadow: 0 4rpx 12rpx rgba(255, 68, 68, 0.2);
          }
        }
      }

      .refund-amount {
        height: 88rpx;
        line-height: 88rpx;
        font-size: 40rpx;
        font-weight: 600;
        color: #ff4444;
        background: #fff5f5;
        padding: 0 24rpx;
        border-radius: 12rpx;
      }

      .picker {
        height: 88rpx;
        line-height: 88rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        padding: 0 24rpx;
        font-size: 28rpx;
        color: #333;
      }

      textarea {
        width: 100%;
        height: 200rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        padding: 24rpx;
        font-size: 28rpx;
        color: #333;
        box-sizing: border-box;
      }

      :deep(.uni-file-picker__container) {
        border-radius: 12rpx;
        overflow: hidden;
      }

      :deep(.uni-file-picker__upload-btn) {
        width: 180rpx;
        height: 180rpx;
        margin-right: 20rpx;
        border-radius: 12rpx;
        border: 2rpx dashed #e0e0e0;
        background: #f8f9fa;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
        }
      }

      :deep(.file-picker__progress) {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 2;
        background: rgba(255, 68, 68, 0.9);
        height: 4rpx;
      }

      .reason-selector {
        height: 88rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #f8f9fa;
        border-radius: 12rpx;
        padding: 0 24rpx;
        
        .reason-text {
          font-size: 28rpx;
          color: #333;
          
          &.placeholder {
            color: #999;
          }
        }
        
        .icon-right {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }

  .submit-btn {
    position: fixed;
    left: 30rpx;
    right: 30rpx;
    bottom: 40rpx;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    background: linear-gradient(135deg, #ff6b6b, #ff4444);
    color: #fff;
    font-size: 32rpx;
    font-weight: 600;
    border-radius: 44rpx;
    box-shadow: 0 6rpx 20rpx rgba(255, 68, 68, 0.3);
    transition: all 0.3s ease;
    z-index: 2;
    &:active {
      transform: scale(0.98);
      box-shadow: 0 4rpx 12rpx rgba(255, 68, 68, 0.2);
    }
  }

  .empty-tip {
    text-align: center;
    padding: 40rpx;
    color: #999;
    font-size: 28rpx;
  }
}

// 添加弹窗相关样式
.reason-popup {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  
  .popup-header {
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
    
    .close-btn {
      position: absolute;
      right: 24rpx;
      width: 64rpx;
      height: 64rpx;
      line-height: 64rpx;
      text-align: center;
      font-size: 40rpx;
      color: #999;
    }
  }
  
  .reason-list {
    max-height: 60vh;
    padding: 20rpx 0;
    
    .reason-item {
      height: 100rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 32rpx;
      font-size: 28rpx;
      color: #333;
      transition: all 0.3s ease;
      
      &.active {
        color: #ff4444;
        background: #fff5f5;
        
        .icon-check {
          color: #ff4444;
          font-size: 32rpx;
        }
      }
      
      &:active {
        background: #f8f9fa;
      }
    }
  }
}

// 添加弹窗动画
:deep(.uni-popup) {
  .uni-popup__wrapper {
    border-radius: 24rpx 24rpx 0 0;
  }
  
  &.popup-bottom {
    .uni-popup__wrapper {
      transform: translateY(100%);
      transition: transform 0.3s ease;
    }
    
    &.uni-popup-show {
      .uni-popup__wrapper {
        transform: translateY(0);
      }
    }
  }
}
