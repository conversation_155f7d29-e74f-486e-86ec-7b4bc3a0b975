.info_box {
  width: 710rpx;
  border-radius: 16rpx;
  background: #ffffff;
  margin-bottom: 20rpx;
}
.staff_info {
  display: flex;
  align-items: center;
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: normal;
  .title {
    width: 120rpx;
    text-align: justify;
    text-align-last: justify;
  }
  .info {
    flex: 1;
    height: 64rpx;
    border-radius: 16rpx;
    background: #f2f3f5;
    line-height: 64rpx;
    padding: 0 20rpx;
    color: #4e5969;
  }
}
.member_input {
  border: 1px solid #ea1306;
  border-radius: 16rpx;
  height: 64rpx;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
}
.member_name {
  width: 120rpx;
  text-align: justify;
  text-align-last: justify;
  color: #3d3d3d;
  font-size: 28rpx;
  font-family: PingFang SC;
}
.info_name {
  color: #4e5969;
}
.confirm {
  width: 136rpx;
  height: 64rpx;
  border-radius: 8rpx;
  background: #e41206;
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: normal;
  line-height: 64rpx;
  color: #ffffff;
  margin-right: 8rpx;
  text-align: center;
}
.cancel {
  width: 136rpx;
  height: 64rpx;
  border-radius: 8rpx;
  background: #f2f3f5;
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: normal;
  line-height: 64rpx;
  color: #333333;
  margin-right: 8rpx;
  text-align: center;
}
.chooseGoods {
  width: 116rpx;
  height: 40rpx;
  border-radius: 4rpx;
  background: #e41206;
  padding: 0 10rpx;
  font-family: Alibaba Sans;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 40rpx;
  color: #ffffff;
  text-align: center;
}
.leader-list {
  position: absolute;
  top: 42px;
  left: 0;
  right: 0;
  max-height: 300rpx;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 4px;
  overflow-y: auto;
  z-index: 999;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .leader-item {
    padding: 20rpx;
    border-bottom: 1px solid #eee;
    cursor: pointer;

    &:hover {
      background: #f5f5f5;
    }

    &:last-child {
      border-bottom: none;
    }
  }
}
.goods_info {
  padding: 0 40rpx 1rpx;
  .goods_ {
    display: flex;
    // justify-content: space-between;
    // align-items: center;
    // .goods_img {
    //   display: flex;
    //   margin-bottom: 30rpx;
    // }
    font-family: Alibaba Sans;
    font-size: 28rpx;
    font-weight: normal;
    line-height: 32rpx;
    color: #3d3d3d;
    border-bottom: 1px solid #f2f3f5;
    // margin-bottom: 20rpx;
    padding-top: 20rpx;
    .goods_img {
      position: relative;
      width: 160rpx;
      height: 136rpx;
      border-radius: 8rpx;
      opacity: 1;
      background: #edf3fa;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 30rpx;
      margin-right: 20rpx;
      .discount {
        position: absolute;
        bottom: 20rpx;
        left: 20rpx;
        height: 22rpx;
        border-radius: 6rpx;
        opacity: 1;
        background: #fcf1f5;
        box-sizing: border-box;
        border: 1px solid #ea1306;
        padding: 0 10rpx;
        font-family: Alibaba Sans;
        font-size: 16rpx;
        font-weight: normal;
        line-height: 20rpx;
        text-align: center;
        color: #ea1306;
      }
    }
    .icon {
      color: #ffffff;
      display: inline-block;
      width: 50rpx;
      height: 24rpx;
      border-radius: 4rpx;
      background: #0b78ff;
      text-align: center;
      line-height: 24rpx;
      font-size: 28rpx;
      margin-right: 10rpx;
    }
  }
  .details {
    display: flex;
    justify-content: space-between;
    font-family: Alibaba Sans;
    font-size: 30rpx;
    font-weight: normal;
    line-height: 36rpx;
    color: #333333;
    margin-bottom: 30rpx;
    .distribution {
      display: inline-block;
      height: 32rpx;
      border-radius: 8rpx;
      box-sizing: border-box;
      border: 1px solid #999999;
      font-family: Alibaba Sans;
      font-size: 24rpx;
      font-weight: normal;
      line-height: 30rpx;
      color: #4e5969;
      padding: 0 14rpx;
      margin-left: 8rpx;
    }
  }
}
.radio_label {
  display: flex;
  align-items: center;
  margin-right: 60rpx;
}

/deep/.uni-section {
  border-radius: 16rpx;
}
/deep/.uni-section .uni-section-header__decoration {
  background-color: #ea1306 !important;
}
/deep/.uni-swipe_button {
  padding: 0 20rpx;
}
/deep/.uni-swipe_button {
  width: 100rpx;
}
