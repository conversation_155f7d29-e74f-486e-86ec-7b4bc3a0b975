<template>
    <view class="card-replace">
        <!-- 顶部日期选择和筛选 -->
        <view class="header">
            <uni-datetime-picker v-model="date" type="daterange" @change="dateChange" />
            <uni-data-select v-model="state" :clear="false" :localdata="stateList"
                @change="stateChange"></uni-data-select>
            <view @click=" $refs.popup.open()" class="add-btn">
                <text style="font-size: 28rpx;">新增</text>
            </view>
        </view>

        <!-- 申请列表 -->
        <view class="application-list">
            <view class="application-item" v-for="(item, index) in applications" :key="index">
                <view v-if="item.state == 0" class="status-tag" style="background: #ff6b35;">审批中</view>
                <view v-if="item.state == 1" class="status-tag" style="background: #67c23a;">已通过</view>
                <view v-if="item.state == 2 || item.state == 3" class="status-tag" style="background: #f23030;">已拒绝
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe670;</text>
                    <text class="label">所属门店：</text>
                    <text class="value">{{ item.storeId }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe62b;</text>
                    <text class="label">申请人：</text>
                    <text class="value">{{ item.userId }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe69d;</text>
                    <text class="label">原岗位：</text>
                    <text class="value">{{ item.originalPostId }}</text>
                </view>
                <view v-if="item.nowDivName" class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe69d;</text>
                    <text class="label">调整岗位：</text>
                    <text class="value">{{ item.nowDivName }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe69c;</text>
                    <text class="label">晋升岗位：</text>
                    <text class="value">{{ item.promoteId }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe6b5;</text>
                    <text class="label">晋升时间：</text>
                    <text class="value">{{ item.applyTime }}</text>
                </view>
                <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe67d;</text>
                    <text class="label">申请原因：</text>
                    <text class="value">{{ item.reason }}</text>
                </view>
                <!-- <view class="info-row">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe656;</text>
                    <text class="label">审批人：</text>
                    <text class="value">{{ item.reason }}</text>
                </view> -->
                <view class="info-row" v-if="item.state == 0">
                    <text class="iconfont" style="font-size:24rpx;color:red;margin-right:5rpx;">&#xe774;</text>
                    <text class="label">审批进度：</text>
                    <view @click="open(item)"
                        style="background-color: #ea1306;color: #fff;padding: 10rpx 20rpx;border-radius: 12rpx;">
                        查看审批进度</view>
                </view>
            </view>
            <view v-if="applications.length === 0" style="text-align: center;color: #999;">
                <image mode=""
                    src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/02d1720432a148828f186755b4215496.png"
                    style="width: 372rpx;height: 312rpx;margin-top: 50rpx;">
                </image>
                <view style="margin-top: 10rpx;font-size: 28rpx;">暂无数据</view>
            </view>
        </view>
        <uni-popup :is-mask-click="false" ref="popup" type="bottom" border-radius="10px 10px 0 0">
            <view class="popup-content">
                <view class="popup-header">
                    <text>晋升申请</text>
                    <text class="close-icon" @click="$refs.popup.close()">×</text>
                </view>
                <view class="popup-body">
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">所属门店：</text>
                        <input type="text" disabled v-model="formData.storeId" class="input"
                            style="background: #f5f5f5;" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">申请人：</text>
                        <input type="text" disabled v-model="formData.name" class="input"
                            style="background: #f5f5f5;" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">原岗位：</text>
                        <input type="text" disabled v-model="formData.originalPostId" class="input"
                            style="background: #f5f5f5;" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">原工资：</text>
                        <input type="text" v-model="formData.oldMoney" placeholder="请输入原工资" class="input" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">调整部门：</text>
                        <input type="text" v-model="formData.promotionDepartment" placeholder="请输入调整部门" class="input"
                            @input="promotionDepartmentChange" />
                        <!-- 搜索结果列表 -->
                        <view class="leader-list" v-if="promotionDepartmentList.length > 0">
                            <view class="leader-item" v-for="(item, index) in promotionDepartmentList" :key="index"
                                @click="selectPromotionDepartment(item)">
                                {{ item.divName }}
                            </view>
                        </view>
                    </view>
                    <view v-if="formData.promotionDepartmentId" class="form-item">
                        <text class="required">*</text>
                        <text class="label">晋升岗位：</text>
                        <view class="select-input">
                            <uni-data-select v-model="formData.postId" :clear="false"
                                :localdata="postNameList"></uni-data-select>
                        </view>
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">晋升工资：</text>
                        <input type="text" v-model="formData.promotionMoney" placeholder="请输入晋升工资" class="input" />
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">晋升时间：</text>
                        <uni-datetime-picker v-model="formData.date" type="date">
                            <view class="date-input">
                                <view
                                    style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
                                    <text class="iconfont"
                                        style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
                                </view>
                                <text>{{ formData.date || '请选择' }}</text>
                            </view>
                        </uni-datetime-picker>
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">部门领导：</text>
                        <uni-data-select v-model="value" :localdata="range" @change="leaderChange"
                            class="selectleader"></uni-data-select>
                        <!-- <input type="text" v-model="leaderInputValue" placeholder="请输入审批人" class="input"
                            @input="leaderChange" />
                        <view class="leader-list" v-if="leaderList.length > 0">
                            <view class="leader-item" v-for="(item, index) in leaderList" :key="index"
                                @click="selectLeader(item)">
                                {{ item.name }}
                            </view>
                        </view> -->
                    </view>
                    <view class="form-item">
                        <text class="required">*</text>
                        <text class="label">晋升原因：</text>
                        <textarea v-model="formData.reason" placeholder="请输入申请原因" class="textarea" />
                    </view>
                    <view class="form-item">
                        <text class="required"></text>
                        <text class="label label_1">附件：</text>
                        <view
                            style="margin-right: 20rpx;padding: 0 20rpx;line-height: 46rpx;border: 1px solid #F2F3F5; border-radius: 8rpx;color: #333;position: relative;"
                            @click="previewFile" v-if="fileUrl">查看文件 <i
                                style="display: inline-block;width: 24rpx;height: 24rpx;border-radius: 50%;position: absolute;top: -10rpx;right: -10rpx;border: 1px solid #EA1306;background-color: #fff;text-align: center;line-height: 20rpx;font-size: 24rpx;color: #EA1306;"
                                @click.stop="fileUrl = ''">x</i>
                        </view>
                        <view @click="uploadFile"
                            style="padding: 0 20rpx;line-height: 46rpx;border: 1px solid #F2F3F5; border-radius: 8rpx;color: #333;">
                            上传附件</view>
                    </view>
                    <view class="form-footer">
                        <view class="btn save" @click="saveForm">提交</view>
                        <view class="btn cancel" @click="$refs.popup.close()">取消</view>
                    </view>
                </view>
            </view>
        </uni-popup>
        <uni-popup :is-mask-click="false" ref="approve" type="bottom" border-radius="10px 10px 0 0">
            <view class="popup-content1">
                <view class="popup-header">
                    <text>审批进度</text>
                    <text class="close-icon" @click="$refs.approve.close()">×</text>
                </view>
                <view class="popup-body">
                    <view class="step-container">
                        <view class="step" v-for="(item, index) in stepList" :key="index">
                            <view class="step-number"></view>
                            <view class="step-title">
                                <view>开始时间:{{ item.startTime }}</view>
                                <view>{{ item.assignee }}：{{ item.taskName }}</view>
                                <view v-if="item.comment">审批意见：{{ item.comment }}</view>
                                <view v-if="item.endTime">结束时间:{{ item.endTime }}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const req = require("../../utils/request");

export default {
    data() {
        return {
            date: [],
            state: 4,
            leaderInputValue: '',
            stateList: [
                { value: 4, text: "全部" },
                { value: 0, text: "审核中" },
                { value: 1, text: "已通过" },
                { value: 2, text: "已拒绝" },
            ],
            postNameList: [],
            selectedStatus: '',
            applications: [],
            formData: {
                name: req.getStorage('userInfo').staffName,
                storeId: req.getStorage('userInfo').divName,
                originalPostId: req.getStorage('userInfo').postName,
                postId: null
            },
            pageNum: 1,
            total: 0,
            timer: null,
            leaderList: [],
            promotionDepartmentList: [],
            fileUrl: "",
            is_headquarters: '',   // 是否总部人
            stepList: [],
            value: 0,
            range: [],
        }
    },
    onLoad() {
        this.getData();
        // this.getPostName();
        this.getBusiness();
    },
    onReachBottom() {
        this.pageNum++
        this.getData()
    },
    methods: {
        open(item) {
            req.showLoading();
            req.getRequest('/shopApi/translate/getDetails', {
                id: item.id
            }, res => {
                let data = [...res.data];
                let list = data.filter(item => item.processDefinitionId.includes('employeePromotion'));
                let taskId = list.length > 0 ? list[0].taskId : ""
                req.getRequest(`/shopApi/translate/history/${taskId}`, {}, res => {
                    console.log(res);
                    this.stepList = res;
                    uni.hideLoading();
                    this.$refs.approve.open();
                })
            })
        },
        promotionDepartmentChange(e) {
            const value = e.detail.value.trim();
            this.formData.promotionDepartmentId = '';
            this.formData.leader = '';
            this.formData.postId = null;
            if (this.timer) {
                clearTimeout(this.timer);
                this.timer = null;
            }

            if (!value) {
                this.promotionDepartmentList = [];
                return;
            }
            this.timer = setTimeout(() => {
                req.getRequest('/shopApi/translate/getDiv', {
                    search: value,
                    pageNum: 1,
                    pageSize: 10
                }, res => {
                    this.promotionDepartmentList = res.data.items;
                });
            }, 500);
        },

        selectPromotionDepartment(item) {
            this.formData.promotionDepartment = item.divName;
            this.formData.promotionDepartmentId = item.id;
            this.promotionDepartmentList = [];
            this.getPostName()
        },
        getBusiness() {
            req.getRequest('/shopApi/goOut/business', {
                name: req.getStorage('userInfo').staffName
            }, res => {
                this.is_headquarters = res.data;
                this.getReviewer();
            });
        },
        getReviewer() {
            req.getRequest('/shopApi/translate/getReviewer', {
                uid: req.getStorage('userInfo').introductionId,
                node: 'addleave',
                isHeadquarters: this.is_headquarters
            }, res => {
                if (res.data.length != 0) {
                    this.range = res.data.map(item => ({
                        value: item.id,
                        text: item.name
                    }))
                }
            });
        },
        // 上传附件
        uploadFile() {
            console.log("上传附件");
            wx.chooseMessageFile({
                count: 1, // 选择文件的个数
                type: 'all', // 文件类型，可以是图片、视频、文档等
                success: (res) => {
                    req.showLoading('上传中');
                    const tempFilePath = res.tempFiles[0].path; // 获取文件路径
                    req.uploadImg('/shopApi/translate/upload', tempFilePath, res => {
                        // console.log(res, "---------------");
                        this.fileUrl = res;
                        uni.hideLoading();
                    });
                },
                fail(err) {
                    console.log('选择文件失败', err);
                }
            });
        },
        // 预览文件
        previewFile() {
            const fileExtension = this.fileUrl.split('.').pop().toLowerCase();
            if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
                // 预览图片
                uni.previewImage({
                    current: this.fileUrl, // 当前显示图片的http链接
                    urls: [this.fileUrl]    // 需要预览的图片http链接列表
                });
                // fileExtension === 'pdf'
            } else if (['pdf', 'xls', 'ppt', 'docx', 'xlsx', 'pptx'].includes(fileExtension)) {
                uni.downloadFile({
                    url: this.fileUrl,
                    success: function (res) {
                        var filePath = res.tempFilePath;
                        uni.openDocument({
                            filePath: filePath,
                            showMenu: true,
                            success: function (res) {
                                console.log('打开文档成功');
                            }
                        });
                    }
                });
            } else {
                // 其他文件类型可以使用外部查看或下载方式
                wx.showModal({
                    title: '文件格式不支持预览',
                    content: '您可以下载该文件查看。',
                    confirmText: '下载',
                    success(res) {
                        if (res.confirm) {
                            wx.downloadFile({
                                url: this.jobInfo.annex,
                                success(downloadRes) {
                                    wx.openDocument({
                                        filePath: downloadRes.tempFilePath,
                                        success: function () {
                                            console.log('文件打开成功');
                                        }
                                    });
                                }
                            });
                        }
                    }
                });
            }
        },
        getPostName() {
            req.getRequest("/shopApi/preferment/postList1", {
                divId: this.formData.promotionDepartmentId,
            }, res => {
                res.data.forEach(item => {
                    item.text = item.postName
                    item.value = item.id
                })
                this.postNameList = res.data
            })
        },
        leaderChange(e) {
            this.formData.leader = e ? this.range.find(item => item.value === e).text : "";
            // const value = e.detail.value.trim();
            // this.leaderInputValue = value;
            // this.formData.leader = ""
            // if (this.timer) {
            //     clearTimeout(this.timer);
            //     this.timer = null;
            // }

            // if (!value) {
            //     this.leaderList = [];
            //     return;
            // }
            // this.timer = setTimeout(() => {
            //     let params = {
            //         name: value
            //     };
            //     let url = this.is_headquarters == 1 ? "/shopApi/replacement/getStaff" : "/shopApi/goOut/getManager";
            //     if (this.is_headquarters != 1) {
            //         params.uid = req.getStorage('userInfo').introductionId;
            //     }
            //     req.getRequest(url, params, res => {
            //         this.leaderList = res.data.items;
            //     });
            // }, 500);
        },
        // selectLeader(item) {
        //     this.formData.leader = item.name;
        //     this.leaderInputValue = item.name;
        //     this.leaderList = [];
        // },
        stateChange(e) {
            this.state = e
            this.pageNum = 1
            this.getData()
        },
        dateChange(e) {
            this.date = e
            this.pageNum = 1
            this.$nextTick(() => {
                this.getData();
            })
        },
        getData() {
            req.showLoading();
            req.getRequest("/shopApi/preferment/list", {
                pageNum: this.pageNum,
                pageSize: 10,
                state: this.state == 4 ? null : this.state,
                startTime: this.date[0],
                endTime: this.date[1],
                staffId: req.getStorage('userInfo').introductionId,
                userId: req.getStorage('userInfo').staffName,
            }, res => {
                uni.hideLoading()
                this.total = res.total
                if (this.pageNum === 1) {
                    this.applications = res.data.items
                } else {
                    this.applications = [...this.applications, ...res.data.items]
                }
            })
        },
        saveForm() {
            // 表单验证
            if (!this.formData.storeId) {
                req.msg('请选择所属门店')
                return;
            }
            if (!this.formData.name) {
                req.msg('请输入申请人')
                return;
            }
            if (!this.formData.oldMoney) {
                req.msg('请输入原工资')
                return;
            }
            if (!this.formData.postId) {
                req.msg('请选择晋升岗位')
                return;
            }
            if (!this.formData.promotionMoney) {
                req.msg('请输入晋升工资')
                return;
            }
            if (!this.formData.date) {
                req.msg('请选择晋升时间')
                return;
            }
            if (!this.formData.leader) {
                req.msg('请选择部门领导')
                return;
            }
            if (!this.formData.reason) {
                req.msg('请输入晋升原因')
                return;
            }

            // // 如果选择的晋升岗位与原岗位相同
            // if (this.formData.postName === req.getStorage('userInfo').postName) {
            //     req.msg('晋升岗位不能与原岗位相同')
            //     return;
            // }
            let attachment = [this.fileUrl];
            // 验证通过后，可以继续提交表单
            req.showLoading();
            req.postRequest("/shopApi/preferment/add", {
                userId: req.getStorage('userInfo').staffName,
                storeId: req.getStorage('userInfo').divId,
                originalPostId: req.getStorage('userInfo').postId,
                promoteId: this.formData.postId,
                oldMoney: this.formData.oldMoney,
                promotionMoney: this.formData.promotionMoney,
                applyTime: this.formData.date,
                reason: this.formData.reason,
                deptleader: this.formData.leader,
                promotionDepartment: this.formData.promotionDepartmentId,
                attachment: JSON.stringify(attachment),//附件
            }, res => {
                uni.hideLoading();
                if (res.code === 200) {
                    req.msg('提交成功')
                    this.$refs.popup.close();
                    this.pageNum = 1;
                    this.getData();
                    this.formData = {
                        name: req.getStorage('userInfo').staffName,
                        storeId: req.getStorage('userInfo').divName,
                        originalPostId: req.getStorage('userInfo').postName,
                        postId: null,
                        leader: "",
                        reason: "",
                        oldMoney: "",
                        promotionMoney: "",
                        applyTime: "",
                        promotionDepartment: ""
                    };
                    this.fileUrl = "";
                }
            });
        }
    }
}
</script>
<style lang="scss" scoped src="./promotion.scss"></style>
