<template>
  <view class="hot-sale">
    <!-- 商品列表 -->
    <view class="product-list">
      <view @click="goToDetail(item)" class="product-item" v-for="(item, index) in list" :key="index">
        <image v-if="type == 'hot' && index < 10" class="rank-img" :src="`/static/hot/top${index + 1}.png`"
          mode="widthFix" />
        <!-- 商品信息 -->
        <view class="product-content">
          <!-- 商品图片 -->
          <view class="product-img">
            <image :src="item.pic" mode="aspectFit"></image>
          </view>

          <!-- 商品详情 -->
          <view class="product-info">
            <view class="product-name">{{ item.goodsName }}</view>
            <view class="product-desc">规格：{{ item.doseSpec ? item.doseSpec : '暂无信息' }}</view>
            <view class="product-feature">功能主治：{{ item.fi ? item.fi : '暂无信息' }}</view>
          </view>
        </view>
        <view class="price-section">
          <!-- 价格区域 -->
          <view class="price">
            <text class="symbol">¥</text>
            <text class="amount">{{ item.level1 }}</text>
            <text v-if="item.salePrice > item.level1" class="original-price">¥{{ item.salePrice }}</text>
          </view>
          <view @click.stop="openBuyPopup(item)" class="buy-btn">
            去购买
            <text style="font-size: 24rpx;margin-left: 10rpx;" class="iconfont">&#xe667;</text>
          </view>
        </view>
      </view>
    </view>
    <buy-popup ref="buyPopup" :is-cart-action="isCartAction" :product="{
      image: data.pic || '',
      price: data.level1 || 0,
      originalPrice: data.salePrice || 0,
      name: data.goodsName || '',
      id: data.id || '',
      maxBuy: data.maxBuy || 0,
      isMedicalInsurance: data.isMedicalInsurance,
      isOverallPlanning: data.isOverallPlanning
    }" @confirm="handleBuyConfirm" :cloud-stock="Number(data.warehouseStock) || 0"
      :store-stock="Number(data.saleStock) || 0" @click.stop />
  </view>
</template>

<script>
const req = require("../../utils/request")
import buyPopup from "@/components/buy-popup/buy-popup" //商品卡片
export default {
  components: {
    buyPopup
  },
  data() {
    return {
      list: [],
      data: {},
      type: "",
    }
  },
  onLoad(options) {
    this.getHotSaleList()
    this.type = options.type
    uni.setNavigationBarTitle({
      title: options.title
    })
  },
  methods: {
    getHotSaleList() {
      req.showLoading()
      req.getRequest("/shopApi/product/hotSaleAndHealth", {
        merchantId: req.getStorage("currentStore").id
      }, res => {
        uni.hideLoading()
        if (this.type == "health") {
          this.list = res.data.HealthList
        } else {
          this.list = res.data.hotSaleList
        }
      })
    },
    goToDetail(item) {
      uni.navigateTo({
        url: `/product/detail/detail?id=${item.id}`
      })
    },
    openBuyPopup(item) {
      this.data = item
      this.$refs.buyPopup.open()
    },
  }
}
</script>

<style lang="scss" scoped>
.hot-sale {
  background-image: url('https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241121/78a2a0eddbf84af987f08200c51b7045.png');
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-color: #f5f5f5;
  min-height: 100vh;

  .nav-bar {
    background-color: #ff0000;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    color: #fff;
    position: sticky;
    top: 0;
    z-index: 100;

    .title {
      font-size: 32rpx;
      font-weight: 500;
    }

    .right-icons {
      display: flex;
      gap: 30rpx;
    }
  }

  .product-list {
    padding: 20rpx;

    .product-item {
      background-color: #ED150D;
      border-radius: 20rpx;
      margin-bottom: 20rpx;
      position: relative;

      .rank-tag {
        position: absolute;
        left: 20rpx;
        top: 20rpx;
        background-color: #999;
        color: #fff;
        padding: 4rpx 12rpx;
        border-radius: 6rpx;
        font-size: 24rpx;

        &.top-three {
          background-color: #ff6b00;
        }
      }

      .product-content {
        display: flex;
        background: #fff;
        border-radius: 20rpx;
        padding: 30rpx;

        .product-img {
          width: 200rpx;
          height: 200rpx;
          margin-right: 20rpx;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .product-info {
          flex: 1;

          .product-name {
            font-size: 30rpx;
            font-weight: 500;
            margin-bottom: 10rpx;
          }

          .product-desc {
            font-size: 24rpx;
            color: #666;
            margin-bottom: 10rpx;
          }

          .product-feature {
            font-size: 24rpx;
            color: #999;
            line-height: 1.4;
            margin-bottom: 20rpx;
          }
        }
      }

      .hot-index {
        position: absolute;
        right: 20rpx;
        top: 20rpx;
        font-size: 24rpx;
        color: #ff6b00;
      }

      .price-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx;
        border-radius: 20rpx;

        .price {
          display: flex;
          align-items: baseline;

          .symbol {
            color: #fff;
            font-size: 24rpx;
          }

          .amount {
            color: #fff;
            font-size: 45rpx;
            font-weight: bold;
            margin-right: 10rpx;
          }

          .original-price {
            color: #fff;
            font-size: 30rpx;
            text-decoration: line-through;
          }
        }

        .buy-btn {
          font-weight: 900;
          background: linear-gradient(90deg, #FBCAB0 0%, #FEDCC9 100%);
          color: #EA1306;
          padding: 12rpx 20rpx;
          border-radius: 15rpx;
          font-size: 26rpx;
        }
      }

      .rank-img {
        position: absolute;
        left: 0;
        top: -6rpx;
        width: 80rpx;
        z-index: 1;
      }
    }
  }
}
</style>