<template>
	<!--pages/editAddress/editAddress.wxml-->
	<view>


		<view class="list">
			<view class="li">
				<text>收货人</text>
				<view class="item">
					<input name="name" v-model="form.name" placeholder="请填写收货人姓名"></input>
				</view>
			</view>
			<view class="li">
				<text>手机号</text>
				<view class="item">
					<input maxlength="11" name="phone" v-model="form.phone" placeholder="请填写手机号"></input>
				</view>
			</view>
			<view style="justify-content: space-between;" class="li">
				<text>所在地区</text>
				<view @click="open">{{ str ? str : '请选择所在地区' }}</view>
				<!-- <picker class="item" mode="region" @change="bindRegionChange" v-model="region"
					:custom-item="customItem">
					<view class="picker">
						{{ region.length > 0 ? region[0] + region[1] + region[2] : '请选择所在地区' }}
					</view>
				</picker> -->
				<!-- <image src="/static/pages/images/more.png" class="rico"></image> -->
				<!-- <image src="../static/mine/images/fbico2.png" class="loca" @click="chooseLocation()"></image> -->
			</view>
			<view class="li">
				<text>详细信息</text>
				<view class="item">
					<input name="house" v-model="form.house" placeholder="如街道、门牌号"></input>
				</view>
			</view>
		</view>
		<view class="list moren">
			<view class="li">
				<text>设为默认</text>
				<view class="item she">
					<image
						:src="isDefault ? 'https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241021/876ae6c1206a4eee97be21a45d4a1c74.png' : 'https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241021/4a776c2e1e7c42b1857c37f80f01719a.png'"
						class="check" @click="setDefault"></image>
				</view>
			</view>
		</view>
		<!-- <view v-if="id" class="btn dflex">
			<view class="del" @tap="deleteAddress">删除</view>
			<button @click="formSubmit" class="submit" form-type="submit">确定</button>
		</view> -->
		<view @click="formSubmit" class="addBtn">确定</view>
		<cityPicker :column="column" :default-value="defaultValue" :mask-close-able="maskCloseAble" @confirm="confirm"
			@cancel="cancel" :visible="visible" />
	</view>
</template>

<script>
const req = require("../../utils/request.js");
let QQMapWX = require("../../utils/qqmap.js");
import cityPicker from '@/components/cityPicker/cityPicker.vue'
export default {
	components: { cityPicker },
	data() {
		return {
			id: "",
			userId: "",
			maskCloseAble: true,
			visible: false,
			defaultValue: ['广东省', '广州市', '荔湾区'],
			str: "",
			column: 3,
			region: [],
			isDefault: 0,
			isChoose: false,
			form: {},
			once: false,
			address: ""
		};
	},

	components: {},
	props: {},
	onLoad(options) {
		QQMapWX.initMap()
		this.id = options.id;
		this.userId = options.userId
		if (this.id) this.chooseAddress();
	},
	methods: {
		open() {
			this.visible = true
		},
		confirm(val) {
			this.str = val.name
			this.form.region = `${val.provinceName},${val.cityName},${val.areaName}`
			this.form.address = val.name
			this.visible = false
		},
		cancel() {
			this.visible = false
		},
		bindRegionChange(event) {
			this.setData({
				region: event.detail.value
			});
		},
		setDefault() {
			this.isDefault = this.isDefault ? 0 : 1
		},
		chooseAddress() {
			req.showLoading()
			req.getRequest('/shopApi/mp/address/get', {
				id: this.id
			}, res => {
				uni.hideLoading()
				console.log(res);
				this.form = res.data
				this.isDefault = res.data.isDefault
				this.str = res.data.address
			});
		},

		formSubmit() {
			if (!this.form.name) return req.msg("请输入姓名")
			if (!this.form.phone) return req.msg("请输入手机号")
			if (!this.str) return req.msg("请选择地区")
			if (!this.form.house) return req.msg("请输入详细地址")
			this.form.isDefault = this.isDefault
			this.form.staffId = req.getStorage("uid")
			this.form.uid = this.userId
			req.showLoading()
			QQMapWX.geocoder(this.form.address + this.form.house, res => {
				console.log(res, "获取地址");
				this.form.lng = res.location.lng;
				this.form.lat = res.location.lat;
				req.postRequest("/shopApi/mp/address/save", this.form, res => {
					uni.hideLoading();
					uni.navigateBack()
				})
			})




		},


	}
};
</script>
<style>
@import "./addressDetail.css";
</style>