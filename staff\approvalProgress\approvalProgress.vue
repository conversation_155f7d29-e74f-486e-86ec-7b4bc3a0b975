<template>
	<view class="approvalProgress">
		<view class="exitTime">
			<uni-section title="申请信息" type="line">
				<view style="padding: 20rpx 40rpx;">
					<view class="form-item">
						<text class="required">*</text>
						<text class="label">申请人：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.starter }}</view>
					</view>
					<view class="form-item">
						<text class="required">*</text>
						<text class="label">申请日期：</text>
						<view class="date-input" style="background: #F2F3F5;">
							<view
								style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
								<text class="iconfont"
									style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
							</view>
							<text>{{ info.startTime }}</text>
						</view>
					</view>
					<view class="form-item" v-if="info.startDate">
						<text class="required">*</text>
						<text class="label">开始时间：</text>
						<view class="date-input" style="background: #F2F3F5;">
							<view
								style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
								<text class="iconfont"
									style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
							</view>
							<text>{{ info.startDate }}</text>
						</view>
					</view>
					<view class="form-item" v-if="info.endDate">
						<text class="required">*</text>
						<text class="label">结束时间：</text>
						<view class="date-input" style="background: #F2F3F5;">
							<view
								style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
								<text class="iconfont"
									style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
							</view>
							<text>{{ info.endDate }}</text>
						</view>
					</view>
					<view class="form-item" v-if="info.outType">
						<text class="required">*</text>
						<text class="label">外出类型：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.outType == 0 ? '外出'
							: '巡店'
						}}</view>
					</view>
					<view class="form-item" v-if="info.outStore && info.outType == 1">
						<text class="required">*</text>
						<text class="label">巡店地址：</text>
						<view class="input"
							style="line-height: 46rpx;background: #F2F3F5;height: auto;padding: 10rpx 20rpx;">{{
								info.outStore }}</view>
					</view>
					<view class="form-item" v-if="info.details && info.details.length > 0">
						<text class="required">*</text>
						<text class="label">打卡记录：</text>
						<view style="color: #EA1306;text-decoration: underline;"
							@click="goUrl(`/staff/punchCard/punchCard?id=${info.businessKey}&formIndex=1`)">查看打卡详情
						</view>
					</view>
					<view class="form-item" v-if="info.howTime">
						<text class="required">*</text>
						<text class="label">时长：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.howTime }}</view>
					</view>
					<view class="form-item" v-if="info.turnaroundTime">
						<text class="required">*</text>
						<text class="label">转正时间：</text>
						<view class="date-input" style="background: #F2F3F5;">
							<view
								style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
								<text class="iconfont"
									style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
							</view>
							<text>{{ info.turnaroundTime }}</text>
						</view>
					</view>
					<view class="form-item" v-if="info.fullMoney">
						<text class="required">*</text>
						<text class="label">转正薪资：</text>
						<view class="date-input" style="background: #F2F3F5;">
							<view
								style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
								<text class="iconfont"
									style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
							</view>
							<text>{{ info.fullMoney }}</text>
						</view>
					</view>
					<view class="form-item" v-if="info.transferTime">
						<text class="required">*</text>
						<text class="label">调职时间：</text>
						<view class="date-input" style="background: #F2F3F5;">
							<view
								style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
								<text class="iconfont"
									style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
							</view>
							<text>{{ info.transferTime }}</text>
						</view>
					</view>
					<view class="form-item" v-if="info.promotionTime">
						<text class="required">*</text>
						<text class="label">晋升时间：</text>
						<view class="date-input" style="background: #F2F3F5;">
							<view
								style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
								<text class="iconfont"
									style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
							</view>
							<text>{{ info.promotionTime }}</text>
						</view>
					</view>
					<!-- <view class="form-item" v-if="info.deptName">
						<text class="required">*</text>
						<text class="label">原部门：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.deptName }}</view>
					</view> -->
					<view class="form-item" v-if="info.nowDivName">
						<text class="required">*</text>
						<text class="label">新部门：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.nowDivName }}</view>
					</view>
					<view class="form-item" v-if="info.postName">
						<text class="required">*</text>
						<text class="label">原岗位：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.postName }}</view>
					</view>
					<view class="form-item" v-if="info.nowPostName">
						<text class="required">*</text>
						<text class="label">新岗位：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.nowPostName }}
						</view>
					</view>
					<view class="form-item" v-if="info.oldMoney">
						<text class="required">*</text>
						<text class="label">原工资：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.oldMoney }}</view>
					</view>
					<view class="form-item" v-if="info.promotionMoney">
						<text class="required">*</text>
						<text class="label">晋升工资：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.promotionMoney }}
						</view>
					</view>
					<view class="form-item" v-if="info.resignationTime">
						<text class="required">*</text>
						<text class="label">离职时间：</text>
						<view class="date-input" style="background: #F2F3F5;">
							<view
								style="background: #F9E9E8;border-right: 1px solid red;height: 100%;padding: 0 10px;display: flex;align-items: center;margin-right:20rpx;">
								<text class="iconfont"
									style="color:red;font-size:34rpx;font-weight: 900;">&#xe685;</text>
							</view>
							<text>{{ info.resignationTime }}</text>
						</view>
					</view>
					<view class="form-item" v-if="info.handName">
						<text class="required">*</text>
						<text class="label">交接人：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.handName }}
						</view>
					</view>
					<view class="form-item" v-if="info.leave">
						<text class="required">*</text>
						<text class="label">请假类型：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.leave }}
						</view>
					</view>
					<view class="form-item" v-if="info.checkPeople">
						<text class="required">*</text>
						<text class="label">被考核人：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.checkPeople }}
						</view>
					</view>
					<view class="form-item" v-if="info.deptName">
						<text class="required">*</text>
						<text class="label">原部门：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.deptName }}
						</view>
					</view>
					<view class="form-item" v-if="info.coverDepName">
						<text class="required">*</text>
						<text class="label">跨部门：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.coverDepName }}
						</view>
					</view>
					<view class="form-item" v-if="info.years">
						<text class="required">*</text>
						<text class="label">考核时间：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.years }}
						</view>
					</view>
					<view class="form-item" v-if="info.things">
						<text class="required">*</text>
						<text class="label">考核事项：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.things }}
						</view>
					</view>
					<view class="form-item" v-if="info.baseSco">
						<text class="required">*</text>
						<text class="label">基础分：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.baseSco }}
						</view>
					</view>
					<view class="form-item" v-if="info.sco">
						<text class="required">*</text>
						<text class="label">得分值：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.sco }}
						</view>
					</view>
					<view class="form-item" v-if="info.remark">
						<text class="required">*</text>
						<text class="label">扣分原因：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.remark }}
						</view>
					</view>
					<view class="form-item" v-if="info.isJob !== 1 && info.isClass !== 1 && info.reason">
						<text class="required">*</text>
						<text class="label">申请原因：</text>
						<view class="input"
							style="line-height: 48rpx;background: #F2F3F5;height: auto;padding: 10rpx 20rpx;">{{
								info.reason
							}}</view>
					</view>
					<view class="form-item" v-if="attachment.length > 0">
						<text class="required">*</text>
						<text class="label">附件：</text>
						<view
							style="margin-right: 20rpx;padding: 0 20rpx;line-height: 46rpx;border: 1px solid #F2F3F5; border-radius: 8rpx;color: #333;position: relative;"
							v-for="(item, index) in attachment" @click="chooseImg(item, index)" :key="index">
							图片{{ index + 1 }}
						</view>
					</view>
					<!-- accessory -->
					<view class="form-item" v-if="accessory.length > 0" style="align-items: flex-start;">
						<text class="required">*</text>
						<text class="label">附件：</text>
						<view style="display: flex;flex-wrap: wrap;">
							<view
								style="margin-right: 20rpx;padding: 0 20rpx;line-height: 46rpx;border: 1px solid #F2F3F5; border-radius: 8rpx;color: #333;position: relative;margin-bottom: 10rpx;"
								@click="previewFile(item)" v-for="(item, index) in accessory" :key="index">
								附件{{ index + 1 }}
							</view>
						</view>
					</view>
				</view>
			</uni-section>
		</view>

		<!-- 入职申请 -->
		<view class="exitTime" v-if="info.isJob === 1">
			<uni-section title="详情信息" type="line">
				<view style="padding: 20rpx 40rpx;">
					<view class="form-item">
						<text class="required">*</text>
						<text class="label label_1">姓名：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.starter }}</view>
					</view>
					<view class="form-item">
						<text class="required">*</text>
						<text class="label label_1">面试时间：</text>
						<input style="line-height: 68rpx;background: #F2F3F5;" type="number"
							v-model="jobInfo.interviewDate" disabled placeholder="面试时间" class="input" />
					</view>
					<view class="form-item">
						<text class="required">*</text>
						<text class="label label_1">报道时间：</text>
						<input style="line-height: 68rpx;background: #F2F3F5;" type="number"
							v-model="jobInfo.whenToReach" disabled class="input" placeholder="报道时间" />
					</view>
					<view class="form-item">
						<text class="required">*</text>
						<text class="label label_1">部门：</text>
						<input type="text" v-model="jobInfo.departmentName" placeholder="请输入部门" class="input"
							@input="departmentChange" />
						<!-- 搜索结果列表 -->
						<view class="leader-list" v-if="departmentList.length > 0">
							<view class="leader-item" v-for="(item, index) in departmentList" :key="index"
								@click="selectDepartment(item)">
								{{ item.divName }}
							</view>
						</view>
					</view>
					<view class="form-item">
						<text class="required">*</text>
						<text class="label label_1">试用期：</text>
						<input type="number" v-model="jobInfo.period" placeholder="请输入试用期" class="input" />
					</view>
					<view class="form-item">
						<text class="required">*</text>
						<text class="label label_1">期望薪资：</text>
						<input style="background: #F2F3F5;" type="number" v-model="jobInfo.expSal" disabled
							placeholder="" class="input" />
					</view>
					<view class="form-item">
						<text class="required">*</text>
						<text class="label label_1">试用薪资：</text>
						<input type="number" v-model="jobInfo.periodSalary" placeholder="请输入转正薪资" class="input" />
					</view>
					<view class="form-item">
						<text class="required">*</text>
						<text class="label label_1">转正薪资：</text>
						<input type="number" v-model="jobInfo.regularizationSalary" placeholder="请输入转正薪资"
							class="input" />
					</view>
					<view class="form-item">
						<text class="required"></text>
						<text class="label label_1">拟录用岗位：</text>
						<!-- <input type="text" v-model="jobInfo.prePost" placeholder="请输入拟录用岗位" class="input" /> -->
						<input type="text" v-model="jobInfo.postName" placeholder="请输入拟录用岗位" class="input"
							@focus="prePostChange" />
						<!-- 搜索结果列表 -->
						<view class="leader-list" v-if="prePostList.length > 0">
							<view class="leader-item" v-for="(item, index) in prePostList" :key="index"
								@click="selectPrePost(item)">
								{{ item.postName }}
							</view>
						</view>
					</view>
					<view class="form-item">
						<text class="required"></text>
						<text class="label label_1">劳动合同期限：</text>
						<input type="number" v-model="jobInfo.contractPeriod" placeholder="请输入劳动合同期限" class="input" />
					</view>
					<view class="form-item">
						<text class="required"></text>
						<text class="label label_1">签约公司：</text>
						<input type="text" v-model="jobInfo.signCompany" placeholder="请输入签约公司" class="input" />
					</view>
					<view class="form-item" v-if="jobInfo.personalPic && jobInfo.personalPic.length > 0">
						<text class="label label_1">个人简历照片：</text>
						<view style="display: flex; flex-wrap: wrap;">
							<view v-for="(pic, index) in jobInfo.personalPic" :key="index" style="margin-right: 10rpx;">
								<image :src="pic" style="width: 100px; height: 100px; border-radius: 5px;"
									@click="chooseImg(pic, index)" />
							</view>
						</view>
					</view>
					<view class="form-item" style="align-items: flex-start;">
						<text class="required"></text>
						<text class="label label_1">附件：</text>
						<view style="display: flex;flex-wrap: wrap;">
							<view
								style="margin-right: 20rpx;padding: 0 20rpx;line-height: 46rpx;border: 1px solid #F2F3F5; border-radius: 8rpx;color: #333;position: relative;margin-bottom: 10rpx;"
								v-for="(item, index) in fileUrl" :key="index" @click="previewFile(index)">附件{{ index + 1
								}}<i style="display: inline-block;width: 24rpx;height: 24rpx;border-radius: 50%;position: absolute;top: -10rpx;right: -10rpx;border: 1px solid #EA1306;background-color: #fff;text-align: center;line-height: 20rpx;font-size: 24rpx;color: #EA1306;"
									@click.stop="del_file(index)">x</i>
							</view>
							<view @click="uploadFile"
								style="padding: 0 20rpx;line-height: 46rpx;border: 1px solid #F2F3F5; border-radius: 8rpx;color: #333;">
								上传附件</view>
						</view>
					</view>
					<!-- <view class="form-item">
						<text class="required"></text>
						<text class="label label_1">附件：</text>
						<view
							style="margin-right: 20rpx;padding: 0 20rpx;line-height: 46rpx;border: 1px solid #F2F3F5; border-radius: 8rpx;color: #333;position: relative;"
							@click="previewFile('')" v-if="fileUrl">查看文件 <i
								style="display: inline-block;width: 24rpx;height: 24rpx;border-radius: 50%;position: absolute;top: -10rpx;right: -10rpx;border: 1px solid #EA1306;background-color: #fff;text-align: center;line-height: 20rpx;font-size: 24rpx;color: #EA1306;"
								@click.stop="fileUrl = ''">x</i>
						</view>
						<view @click="uploadFile"
							style="padding: 0 20rpx;line-height: 46rpx;border: 1px solid #F2F3F5; border-radius: 8rpx;color: #333;">
							上传附件</view>
					</view> -->
					<view class="form-item">
						<text class="required"></text>
						<text class="label ">备注：</text>
						<textarea v-model="jobInfo.remark" class="textarea" />
					</view>
				</view>
			</uni-section>
		</view>
		<view v-if="info.outType != 1" class="exitPost">
			<uni-section title="待办名称" type="line">
				<view style="padding: 20rpx 40rpx;">
					<view class="form-item">
						<text class="required">*</text>
						<text class="label">流程名称：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.processName }}
						</view>
					</view>
					<view class="form-item">
						<text class="required">*</text>
						<text class="label">任务名称：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.taskName }}</view>
					</view>
					<view class="form-item" v-if="info.processName && info.processName.includes('员工信息')">
						<text class="required">*</text>
						<text class="label">详情信息：</text>
						<view @click="goUrl(`/staff/fileMaintain/fileMaintain?formIndex=1&staffId=${info.staffId}`)" style="color: #ea1306;text-decoration: underline;">点击查看</view>
					</view>
				</view>
			</uni-section>
		</view>
		<view class="exitPost" v-if="info.isClass === 1">
			<uni-section title="排班管理" type="line">
				<view style="padding: 20rpx 40rpx;">
					<view class="form-item">
						<text class="required">*</text>
						<text class="label">排班类型：</text>
						<view class="input" style="line-height: 68rpx;background: #F2F3F5;">{{ info.classTypeName }}
						</view>
					</view>
					<view class="form-item">
						<text class="required">*</text>
						<text class="label">排班管理：</text>
						<view @click="$refs.detailPopup.open()"
							style="background-color: #ea1306;color: #fff;padding: 10rpx 20rpx;border-radius: 12rpx;">
							查看详情</view>
					</view>
				</view>
			</uni-section>
		</view>
		<!-- 考核工资信息 -->
		<view class="exitTime" v-if="info.processName && info.processName.includes('考核工资')">
			<uni-section title="薪资详情" type="line">
				<view style="padding: 20rpx 40rpx;">
					<view style="display: flex;height: 60rpx;">
						<view class="form-item form-item1" style="width: 100%;">
							<text class="label label1">考核时间：</text>
							<view>{{ info.checkDate || '' }}</view>
						</view>
					</view>
					<view style="display: flex;height: 60rpx;">
						<view class="form-item form-item1" style="width: 100%;">
							<text class="label label1">所属部门：</text>
							<view>{{ info.storeName || '' }}</view>
						</view>
					</view>
					<view style="display: flex;height: 60rpx;">
						<view class="form-item form-item1" style="width: 100%;">
							<text class="label label1">手机号码：</text>
							<view>{{ info.phone || '' }}</view>
						</view>
					</view>
					<view style="display: flex;">
						<view class="form-item form-item1">
							<text class="label label1">应出勤：</text>
							<view>{{ info.requiredAttendance || 0 }}天</view>
						</view>
						<view class="form-item form-item1">
							<text class="label label1">实际出勤：</text>
							<view>{{ info.attendDay || 0 }}天</view>
						</view>
					</view>
					<view style="display: flex;">
						<view class="form-item form-item1">
							<text class="label label1">迟到时间：</text>
							<view>{{ info.lateMinute || 0 }}分钟</view>
						</view>
						<view class="form-item form-item1">
							<text class="label label1">迟到次数：</text>
							<view>{{ info.lateNum || 0 }}次</view>
						</view>
					</view>
					<view style="display: flex;">
						<view class="form-item form-item1">
							<text class="label label1">星级考评：</text>
							<view>{{ info.starLv || 0 }}分</view>
						</view>
						<view class="form-item form-item1">
							<text class="label label1">考核分数：</text>
							<view>{{ info.score || 0 }}分</view>
						</view>
					</view>
					<view style="display: flex;">
						<view class="form-item form-item1">
							<text class="label label1">社保补贴：</text>
							<view>{{ info.medicalAllowance || 0 }}</view>
						</view>
						<view class="form-item form-item1">
							<text class="label label1">岗位补贴：</text>
							<view>{{ info.jobSubsidy || 0 }}</view>
						</view>
					</view>
					<view style="display: flex;">
						<view class="form-item form-item1">
							<text class="label label1">交通补贴：</text>
							<view>{{ info.trafficAllowance || 0 }}</view>
						</view>
						<view class="form-item form-item1">
							<text class="label label1">餐补补贴：</text>
							<view>{{ info.mealAllowance || 0 }}</view>
						</view>
					</view>
					<view style="display: flex;">
						<view class="form-item form-item1">
							<text class="label label1">药师驻点补贴：</text>
							<view>{{ info.practiceDoctorAllowance || 0 }}</view>
						</view>
						<view class="form-item form-item1">
							<text class="label label1">药师证补贴：</text>
							<view>{{ info.cardAllowance || 0 }}</view>
						</view>
					</view>
					<view style="display: flex;">
						<view class="form-item form-item1">
							<text class="label label1">住宿补贴：</text>
							<view>{{ info.stayAllowance || 0 }}</view>
						</view>
						<view class="form-item form-item1">
							<text class="label label1">司龄奖：</text>
							<view>{{ info.ageAllowance || 0 }}</view>
						</view>
					</view>
					<view style="display: flex;">
						<view class="form-item form-item1">
							<text class="label label1">其他薪资补充：</text>
							<view>{{ info.staffWageSupply || 0 }}</view>
						</view>
						<view class="form-item form-item1">
							<text class="label label1">社保：</text>
							<view>{{ info.socialMoney || 0 }}</view>
						</view>
					</view>
					<view style="display: flex;">
						<view class="form-item form-item1">
							<text class="label label1">缺卡扣款：</text>
							<view>{{ info.missingCardWithhold || 0 }}</view>
						</view>
						<view class="form-item form-item1">
							<text class="label label1">迟到扣款：</text>
							<view>{{ Math.abs(info.beLateWithhold) || 0 }}</view>
						</view>
					</view>
					<view style="display: flex;">
						<view class="form-item form-item1">
							<text class="label label1">缺勤次数：</text>
							<view>{{ info.absenceNumber || 0 }}</view>
						</view>
						<view class="form-item form-item1">
							<text class="label label1">缺卡次数：</text>
							<view>{{ info.missingCardNumber || 0 }}</view>
						</view>
					</view>
					<view style="display: flex;">
						<view class="form-item form-item1">
							<text class="label label1">应发红包：</text>
							<view>{{ info.payable || 0 }}</view>
						</view>
						<view class="form-item form-item1">
							<text class="label label1">工资红包：</text>
							<view>{{ info.workPay || 0 }}</view>
						</view>
					</view>
					<view style="display: flex;height: 60rpx;">
						<view class="form-item form-item1">
							<text class="label label1">初始绩效：</text>
							<view>{{ info.initCheckMoney || 0 }}</view>
						</view>
						<view class="form-item form-item1">
							<text class="label label1">实际绩效：</text>
							<view>{{ info.checkMoney || 0 }} </view>
						</view>
					</view>
					<view style="display: flex;height: 60rpx;">
						<view class="form-item form-item1">
							<text class="label label1">底薪：</text>
							<view>{{ info.lowMoney || 0 }}</view>
						</view>
						<view class="form-item form-item1">
							<text class="label label1">提成：</text>
							<view>{{ info.integral || 0 }} </view>
						</view>
					</view>
					<view style="display: flex;">
						<view class="form-item form-item1">
							<text class="label label1">应发工资：</text>
							<view>{{ info.payableSalary || 0 }}</view>
						</view>
						<view class="form-item form-item1">
							<text class="label label1">实发工资：</text>
							<view>{{ info.actualSalary || 0 }}</view>
						</view>
					</view>
				</view>
			</uni-section>
		</view>
		<view class="exitPost">
			<uni-section title="审批" type="line">
				<view style="padding: 20rpx 40rpx;">
					<view class="form-item" v-if="step !== 'ceocheck'">
						<text class="required">*</text>
						<text class="label">审批结果：</text>
						<radio-group style="display: flex;align-items: center;" @change="changeResult">
							<label class="uni-list-cell uni-list-cell-pd" v-for="(item, index) in items"
								:key="item.value" style="display: flex;align-items: center;margin-right: 10rpx;">
								<view>
									<radio :value="item.value" :checked="item.value === result"
										style="transform:scale(0.7);" color="#EA1306" />
								</view>
								<view>{{ item.name }}</view>
							</label>
						</radio-group>
					</view>
					<view class="form-item" v-if="step !== 'ceocheck'">
						<text class="required">*</text>
						<text class="label" style="width: 208rpx;">下一级审批人：</text>
						<uni-data-select v-model="value" :localdata="range" @change="leaderChange"
							class="selectleader"></uni-data-select>
						<!-- <input type="text" v-model="leader.name" placeholder="请输入审批人员" class="input"
							@input="leaderChange" />
						<view class="leader-list" v-if="leaderList.length > 0">
							<view class="leader-item" v-for="(item, index) in leaderList" :key="index"
								@click="selectLeader(item)">
								{{ item.name }}
							</view>
						</view> -->
					</view>
					<view class="form-item" style="align-items: flex-start;">
						<text class="required"> </text>
						<text class="label">审批意见：</text>
						<textarea v-model="comment" placeholder="请输入审批意见" class="textarea" />
					</view>
					<view class="form-item">
						<text class="label">审批进度：</text>
						<view @click="$refs.popup.open()"
							style="background-color: #ea1306;color: #fff;padding: 10rpx 20rpx;border-radius: 12rpx;">
							查看审批进度</view>
					</view>
					<view v-if="info.outType == 1" class="form-item">
						<text class="label">巡店详情：</text>
						<view @click="goUrl(`/staff/shopPlanCard/shopPlanCard?id=${info.businessKey}&type=1`)"
							style="background-color: #ea1306;color: #fff;padding: 10rpx 20rpx;border-radius: 12rpx;">
							查看巡店详情</view>
					</view>
				</view>
			</uni-section>
		</view>
		<view style="display: flex;justify-content: center;">
			<view class="submitbtn" style="color: #fff;background-color: #ea1306;" @click="submit(1)">提交</view>
			<view class="submitbtn" style="color: #fff;background-color: #FBA808;" @click="submit(3)"
				v-if="step !== 'hrcheck' || this.is_headquarters != 1">拒绝</view>
			<!-- <view class="submitbtn" style="color: #fff;background-color: #ea1306;">撤销</view> -->
		</view>
		<uni-popup :is-mask-click="false" ref="popup" type="bottom" border-radius="10px 10px 0 0">
			<view class="popup-content">
				<view class="popup-header">
					<text>审批进度</text>
					<text class="close-icon" @click="$refs.popup.close()">×</text>
				</view>
				<view class="popup-body">
					<view class="step-container">
						<view class="step" v-for="(item, index) in stepList" :key="index">
							<view class="step-number"></view>
							<view class="step-title">
								<view>开始时间:{{ item.startTime }}</view>
								<view>{{ item.assignee }}：{{ item.taskName }}</view>
								<view v-if="item.comment">审批意见：{{ item.comment }}</view>
								<view v-if="item.endTime">结束时间:{{ item.endTime }}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<!-- 详情弹窗 -->
		<uni-popup ref="detailPopup" type="bottom">
			<view class="popup-content detail-popup">
				<view class="popup-header">
					<text>{{ info.classTypeName }}</text>
					<text class="close-icon" @click="closeDetailPopup">×</text>
				</view>
				<view class="detail-order-list">
					<view class="list-content">
						<view class="item">
							<view class="info-grid">
								<view class="info-row">
									<view class="info-item">
										<text class="label">{{ info.classType == 1 ? '门店' : '部门' }}名称</text>
										<text class="value">：{{ scheduleInfo.storeDepartmentName }}</text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item full">
										<text class="label">{{ info.classType == 1 ? '门店' : '部门' }}负责人</text>
										<text class="value">：{{ scheduleInfo.departmentUser }}</text>
									</view>
								</view>
								<view class="info-row" v-if="scheduleInfo.attendanceTime">
									<view class="info-item">
										<text class="label">考勤时间</text>
										<text class="value">：<text style="color: #EA1306;">{{
											scheduleInfo.attendanceTime }}</text></text>
									</view>
								</view>
								<view class="info-row" v-if="scheduleInfo.breakStartTime && scheduleInfo.breakEndTime">
									<view class="info-item">
										<text class="label">午休时间</text>
										<text class="value">：<text style="color:#08BF8B;">{{ scheduleInfo.breakStartTime
										}} - {{ scheduleInfo.breakEndTime }}</text></text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item">
										<text class="label">部门人数</text>
										<text class="value">：<text style="color: #EA1306;">{{ scheduleInfo.peoNum
										}}人</text></text>
									</view>
								</view>
								<view class="info-row">
									<view class="info-item">
										<text class="label">排班日期</text>
										<!-- <text class="value">：{{ scheduleInfo.scheduleStart }} 至 {{
											scheduleInfo.scheduleEnd }}</text> -->
										<text class="value">：{{ scheduleInfo.scheduleDates }}</text>
									</view>
								</view>
								<view class="info-row" v-if="info.classType === 2">
									<view class="info-item" style="align-items: flex-start;">
										<text class="label">部门人员</text>
										<text class="value">：{{ department }}</text>
									</view>
								</view>
								<!-- v-if="info.classType == 1" -->
								<view class="info-row">
									<view class="info-item" style="align-items: flex-start;">
										<text class="label">班次说明</text>
										<text class="value"
											@click="goUrl(`/staff/schedule/schedule?storeDepartmentId=${info.storeDepartmentId}&classType=${info.classType}`)"
											v-if="info.classType === 1">：<text style="color: #EA1306;">查看</text></text>
										<text class="value" v-else>：<text style="color: #0B78FF;">办公室大小次</text></text>
									</view>
								</view>
							</view>
						</view>
						<!-- 加载更多 -->
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
const req = require("../../utils/request");
export default {
	data() {
		return {
			info: {},
			stepList: [],
			divName: req.getStorage('userInfo').divName,
			step: '',
			items: [{
				value: 1,
				name: '同意',
				checked: 'true'
			},
				// {
				// 	value: 0,
				// 	name: '拒绝'
				// }
			],
			timer: null,
			timer1: null,
			timer2: null,
			leaderList: [],
			leader: {},  // 审批人员
			result: 1,  // 审批结果
			comment: "", // 审批意见
			jobInfo: {},
			departmentList: [],
			prePostList: [],
			fileUrl: [],
			attachment: [],
			accessory: [],
			is_headquarters: '',   // 是否总部人
			department: "",
			scheduleInfo: {},
			value: 0,
			range: [],
		};
	},
	onLoad(options) {
		console.log(options);
		this.info = JSON.parse(options.item);
		// console.log(JSON.parse(this.info.attachment));
		if (this.info.attachment && JSON.parse(this.info.attachment).length != 0) {
			this.attachment = JSON.parse(this.info.attachment);
			console.log(this.attachment);
		};
		if (this.info.accessory && JSON.parse(this.info.accessory).length != 0) {
			this.accessory = JSON.parse(this.info.accessory);
			console.log(this.accessory);
		};
		console.log(this.info);
		if (this.info.isJob === 1) {
			this.getJobInfo();
		};
		if (this.info.isClass === 1) {
			this.getschedule();
			if (this.info.classType === 1 || (this.info.classType === 2 && this.info.storeDepartmentId == 116)) {
				this.info.classTypeName = "门店排班";
				this.info.classType = 1;
			} else if (this.info.classType === 2) {
				this.info.classTypeName = "部门排班";
				this.info.classType = 2;
				this.getDUser();
			}
		};
	},
	onShow() {
		this.getInfo();
	},
	methods: {
		goUrl(url) {
			uni.navigateTo({
				url
			});
		},
		// 获取排班信息
		getschedule() {
			req.getRequest('/shopApi/scheduling/getDatail', {
				type: this.info.classType,
				storeDepartmentId: this.info.storeDepartmentId,
			}, res => {
				console.log(res);
				this.scheduleInfo = res.data;
				// // 排班日期 - 取排班表第一个时间跟最后一个时间
				// if (this.scheduleInfo.paramsMap.length != 0) {
				// 	this.scheduleInfo.scheduleStart = this.scheduleInfo.paramsMap[0].scheduleDate;
				// 	this.scheduleInfo.scheduleEnd = this.scheduleInfo.paramsMap[this.scheduleInfo.paramsMap.length - 1].scheduleDate;
				// }
			})
		},
		// 获取部门人员
		getDUser() {
			req.getRequest('/shopApi/scheduling/getDUser', {
				type: this.info.classType,
				storeDepartmentId: this.info.storeDepartmentId,
			}, res => {
				console.log(res);
				if (res.data.length != 0) {
					let DUser = res.data.map(item => {
						return item.name
					});
					this.department = DUser.join('、')
					console.log(DUser, "部门人员");
				}
			})
		},
		//预览
		chooseImg(item, index) {
			console.log('ItmImg', item);
			uni.previewImage({
				current: index,
				// 当前显示图片的http链接  
				urls: this.jobInfo.personalPic // 假设 personalPic 是一个包含所有图片链接的数组
			});
		},
		getJobInfo() {
			req.getRequest(`/shopApi/translate/getJob`, {
				id: this.info.businessKey
			}, res => {
				console.log(res);
				this.jobInfo = res.data;
				if (this.jobInfo.personalPic) {
					this.jobInfo.personalPic = JSON.parse(this.jobInfo.personalPic);
				};
				if (JSON.parse(res.data.annex).length != 0) {
					this.fileUrl = JSON.parse(res.data.annex);
				};
			});
		},
		changeResult(e) {
			this.result = Number(e.detail.value);
		},
		leaderChange(e) {
			this.leader.name = e ? this.range.find(item => item.value === e).text : "";
			// console.log(e, this.leader.name);
			// const value = e.detail.value.trim();
			// if (this.timer) {
			// 	clearTimeout(this.timer);
			// 	this.timer = null;
			// }
			// if (!value) {
			// 	this.leaderList = [];
			// 	return;
			// }
			// this.timer = setTimeout(() => {
			// 	req.getRequest("/shopApi/replacement/getStaff", {
			// 		name: value
			// 	}, res => {
			// 		this.leaderList = res.data.items;
			// 	});
			// }, 500);
		},
		// selectLeader(item) {
		// 	this.leader = item;
		// 	this.leaderList = [];
		// },
		departmentChange(e) {
			const value = e.detail.value.trim();
			if (this.timer1) {
				clearTimeout(this.timer);
				this.timer1 = null;
			}
			if (!value) {
				this.departmentList = [];
				return;
			}
			this.timer1 = setTimeout(() => {
				req.getRequest("/shopApi/translate/getDiv", {
					search: value,
					pageNum: 1,
					pageSize: 10,
				}, res => {
					this.departmentList = res.data.items;
				});
			}, 500);
		},
		selectDepartment(item) {
			// this.jobInfo.job = item;
			this.jobInfo.departmentId = item.id;
			this.jobInfo.departmentName = item.divName
			this.departmentList = [];
		},
		prePostChange(e) {
			if (!this.jobInfo.departmentId) {
				return req.msg('请先输入部门信息')
			};
			const value = e.detail.value.trim();
			if (this.timer2) {
				clearTimeout(this.timer);
				this.timer2 = null;
			}
			if (!value) {
				this.prePostList = [];
				return;
			};
			this.timer2 = setTimeout(() => {
				req.getRequest("/shopApi/translate/getPostListByDepId", {
					divParentId: this.jobInfo.departmentId
				}, res => {
					this.prePostList = res.data;
				});
			}, 500);
		},
		selectPrePost(item) {
			this.jobInfo.prePostId = item.id;
			this.jobInfo.postName = item.postName;
			this.prePostList = [];
		},
		// 上传附件
		uploadFile() {
			console.log("上传附件");
			wx.chooseMessageFile({
				// count: 1, // 选择文件的个数
				type: 'all', // 文件类型，可以是图片、视频、文档等
				success: (res) => {
					req.showLoading('上传中');
					console.log(res);
					const tempFilePaths = res.tempFiles; // 获取文件路径
					tempFilePaths.forEach((item, index) => {
						req.uploadImg('/shopApi/translate/upload', item.path, res => {
							this.fileUrl.push(res);
						});
					});
					uni.hideLoading();
				},
				fail(err) {
					console.log('选择文件失败', err);
				}
			});
		},
		del_file(index) {
			this.fileUrl.splice(index, 1);
		},
		// uploadFile() {
		// 	console.log("上传附件");
		// 	wx.chooseMessageFile({
		// 		count: 9, // 选择文件的个数
		// 		type: 'all', // 文件类型，可以是图片、视频、文档等
		// 		success: (res) => {
		// 			// console.log(res);
		// 			// res
		// 			// return
		// 			req.showLoading('上传中');
		// 			const tempFilePath = res.tempFiles[0].path; // 获取文件路径
		// 			req.uploadImg('/shopApi/translate/upload', tempFilePath, res => {
		// 				// console.log(res, "---------------");
		// 				this.fileUrl = res;
		// 				uni.hideLoading();
		// 			});
		// 		},
		// 		fail(err) {
		// 			console.log('选择文件失败', err);
		// 		}
		// 	});
		// },
		// 预览文件
		// 预览文件
		previewFile(index) {
			const fileExtension = this.fileUrl[index].split('.').pop().toLowerCase();
			console.log(fileExtension);
			if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
				// 预览图片
				uni.previewImage({
					current: this.fileUrl[index], // 当前显示图片的http链接
					urls: [this.fileUrl[index]]    // 需要预览的图片http链接列表
				});
				// fileExtension === 'pdf'
			} else if (['pdf', 'xls', 'ppt', 'docx', 'xlsx', 'pptx'].includes(fileExtension)) {
				uni.downloadFile({
					url: this.fileUrl[index],
					success: function (res) {
						var filePath = res.tempFilePath;
						uni.openDocument({
							filePath: filePath,
							showMenu: true,
							success: function (res) {
								console.log('打开文档成功');
							}
						});
					}
				});
			} else {
				// 其他文件类型可以使用外部查看或下载方式
				wx.showModal({
					title: '文件格式不支持预览',
					content: '您可以下载该文件查看。',
					confirmText: '下载',
					success(res) {
						if (res.confirm) {
							wx.downloadFile({
								url: this.jobInfo.annex,
								success(downloadRes) {
									wx.openDocument({
										filePath: downloadRes.tempFilePath,
										success: function () {
											console.log('文件打开成功');
										}
									});
								}
							});
						}
					}
				});
			}
		},
		// previewFile(file) {
		// 	const fileExtension = file ? file.split('.').pop().toLowerCase() : this.fileUrl.split('.').pop().toLowerCase();
		// 	if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExtension)) {
		// 		// 预览图片
		// 		file ? uni.previewImage({
		// 			current: file, // 当前显示图片的http链接
		// 			urls: [file]   // 需要预览的图片http链接列表
		// 		}) : uni.previewImage({
		// 			current: this.fileUrl, // 当前显示图片的http链接
		// 			urls: [this.fileUrl]    // 需要预览的图片http链接列表
		// 		})
		// 		// fileExtension === 'pdf'
		// 	} else if (['pdf', 'xls', 'ppt', 'docx', 'xlsx', 'pptx'].includes(fileExtension)) {
		// 		uni.downloadFile({
		// 			url: file ? file : this.fileUrl,
		// 			success: function (res) {
		// 				var filePath = res.tempFilePath;
		// 				uni.openDocument({
		// 					filePath: filePath,
		// 					showMenu: true,
		// 					success: function (res) {
		// 						console.log('打开文档成功');
		// 					}
		// 				});
		// 			}
		// 		});
		// 	} else {
		// 		// 其他文件类型可以使用外部查看或下载方式
		// 		wx.showModal({
		// 			title: '文件格式不支持预览',
		// 			content: '您可以下载该文件查看。',
		// 			confirmText: '下载',
		// 			success(res) {
		// 				if (res.confirm) {
		// 					wx.downloadFile({
		// 						url: file ? file : this.jobInfo.annex,
		// 						success(downloadRes) {
		// 							wx.openDocument({
		// 								filePath: downloadRes.tempFilePath,
		// 								success: function () {
		// 									console.log('文件打开成功');
		// 								}
		// 							});
		// 						}
		// 					});
		// 				}
		// 			}
		// 		});
		// 	}
		// },
		getInfo() {
			// is_headquarters 判断申请人是否是总部的
			req.getRequest('/shopApi/goOut/business', {
				name: this.info.starter,
				processName: this.info.processName
			}, res => {
				console.log(res);
				this.is_headquarters = res.data;
				req.getRequest(`/shopApi/translate/history/${this.info.taskId}`, {}, res => {
					console.log(res);
					this.stepList = [...res];
					const taskName = res[res.length - 1].taskName;
					if (this.is_headquarters == 1) {
						// 判断申请人是否是总部的，是的话 走之前的四级流程
						const stepMap = {
							'发起': 'addleave',
							'发放': 'addleave',
							'补贴': 'addleave',
							'人事': 'hrcheck',
							'部门': 'deptleadercheck',
							'副总裁': 'vicepresidentcheck',
							'总裁': 'ceocheck',
							'ceo': 'ceocheck',
							'销假': 'destroyapply'
						};
						this.step = Object.entries(stepMap).find(([key]) =>
							taskName.includes(key))?.[1] || 'deptleadercheck';
						this.getReviewer();
					} else {
						// 判断申请人是否是总部的，不是的话 走六级流程
						// 六个节点的第一节点:manager 第二节点:hr 第三节点:regional  第四节点:director 第五节点:vicepresident 第六节点:ceo
						const stepMap = {
							'发起': 'addleave',
							'发放': 'addleave',
							'补贴': 'addleave',
							'人事': 'hrcheck',
							'店长': 'managercheck',
							'副总裁': 'vicepresidentcheck',
							'总裁': 'ceocheck',
							'ceo': 'ceocheck',
							'销假': 'destroyapply',
							'区域经理': 'regionalcheck',
							'运营总监': 'directorcheck',
						};
						this.step = Object.entries(stepMap).find(([key]) =>
							taskName.includes(key))?.[1] || 'managercheck';
						// console.log(this.step, '--------------');
						this.getReviewer();
					}

				});
			});
		},
		getReviewer() {
			let params = {
				uid: this.info.staffId,
				node: this.step,
				isHeadquarters: this.is_headquarters
			};
			if (this.info.processName.includes('人员入职')) {
				params.isPersonDiv = this.info.deptId;
			};
			req.getRequest('/shopApi/translate/getReviewer', params, res => {
				if (res.data.length != 0) {
					this.range = res.data.map(item => ({
						value: item.id,
						text: item.name
					}))
				}
			});
		},
		submit(state) {
			req.showLoading();
			// 入职审批
			if (state == 1) {
				if (this.info.isJob === 1 && !this.jobInfo.departmentName) return req.msg('请输入部门信息');
				if (this.info.isJob === 1 && !this.jobInfo.period) return req.msg('请输入试用期');
				if (this.info.isJob === 1 && !this.jobInfo.periodSalary) return req.msg('请输入试用薪资');
				if (this.info.isJob === 1 && !this.jobInfo.regularizationSalary) return req.msg('请输入转正薪资');
			};
			// if (state == 1 && !this.comment) return req.msg('请输入审批意见');
			if (state == 1 && this.step !== 'ceocheck' && !this.leader.name) return req.msg('请输入审批人员');
			let params = {
				taskId: this.info.taskId,
				comment: this.comment,
			};
			if (state == 2 || state == 3) {
				let formKey = this.info.formKey.split('/')[0];
				console.log(formKey, '------------');
				// 好tm牛的sid
				switch (formKey) {
					case 'leaveapply': params.sid = this.is_headquarters == 1 ? 'sid-70E3550A-2308-48B7-B253-F037AF31A924' : 'sid-F0A483F1-9701-4595-90D3-455969BD4F70'; break;    // 请假
					case 'applyexamine': params.sid = this.is_headquarters == 1 ? 'sid-5246D954-4091-407F-A04F-2A25EB17E53F' : 'sid-D25E4A04-8930-4B55-B967-2631150C40C9'; break;  // 转正
					case 'applycard': params.sid = this.is_headquarters == 1 ? 'sid-D943B6C7-2A11-455A-8D8B-3978863D6224' : 'sid-74CD16E5-BE05-49F7-81BF-B7A2DEB7BF3B'; break;     // 补卡
					case 'applysub': params.sid = this.is_headquarters == 1 ? 'sid-3A353D68-D9B1-4880-B1E2-899766C1BB4F' : 'sid-C3548E62-7E51-4754-AC5A-F628D9450DEE'; break;      // 补贴
					case 'applybasescore': params.sid = this.is_headquarters == 1 ? 'sid-EDB816BA-D940-4ACC-B487-39431B4CEEFE' : 'sid-A3F2D73B-E502-4070-8C3D-E6E7F01D8BEE'; break;   // 表现基础分
					case 'applystore': params.sid = this.is_headquarters == 1 ? 'sid-0A18A432-6D11-4A54-ADEC-659C2915F86A' : 'sid-651BA804-CD39-420E-97ED-900FA86894D0'; break;       // 门店运营规范
					case 'applyWage': params.sid = this.is_headquarters == 1 ? ' sid-2409D119-EF60-48BD-94CD-78347AA0925B' : 'sid-FAA28B0E-880C-42FC-AC4C-BDE264DE1D22'; break;        // 工资发放
					case 'overtimeApp': params.sid = this.is_headquarters == 1 ? 'sid-8427184B-DB94-47EE-AD4B-F683F6E685BE' : 'sid-2AA58994-264E-4860-957D-3ED0ED96D22A'; break;    // 加班
					case 'resignation': params.sid = this.is_headquarters == 1 ? 'sid-75B0FCF5-E02C-40CC-986A-D4151C943AB0' : 'sid-B954D3D7-5554-41D5-8D9E-26E9FB1150CB'; break;    // 离职
					case 'employee': params.sid = this.is_headquarters == 1 ? 'sid-C7AF5BFD-0B2C-418E-BC78-568A6B83139D' : 'sid-BCC2995C-7D41-4071-82D5-F0C2D15458F5'; break;       // 晋升
					case 'employeegoout': params.sid = this.is_headquarters == 1 ? 'sid-7C534899-9B7D-46C7-839E-F5C3D5954999' : 'sid-CBD65C74-BD36-4BBF-B02B-105F6CABADA1'; break;       // 外出
					case 'typesetting': params.sid = this.is_headquarters == 1 ? 'sid-CE680435-3910-4177-B2FA-6BFE9910A826' : 'sid-7E3125F0-31BA-408F-977A-4FAA00A4BEFD'; break;       // 排班
					case 'toanotherpost': params.sid = 'sid-ACCB4E73-4253-4E1D-9D47-01BA6F6E0BB3'; break;  // 调职
					case 'personon': params.sid = 'sid-F225EBCA-BAAF-4C08-87AD-A84109E709BE'; break;  // 入职
					case 'scopeStaff': params.sid = 'sid-25D19B7D-CAC0-4C43-8E73-D425C4E08814'; break;  // 人员考核
					case 'staffCheckMoney': params.sid = 'sid-B07594A6-7775-44F9-8C41-592B7E8EC07D'; break;  // 薪资审核
				}
			} else {
				if (this.is_headquarters == 1) {
					if ((this.step == 'addleave')) {
						params.hr = this.leader.name;
						params.hrapprove = this.result ? true : false;
					} else if (this.step == 'hrcheck') {
						params.deptleader = this.leader.name;
						params.deptleaderapprove = this.result ? true : false;
					} else if (this.step == 'deptleadercheck') {
						params.vicepresident = this.leader.name;
						params.vicepresidentapprove = this.result ? true : false;
					} else if (this.step == 'vicepresidentcheck') {
						params.ceo = this.leader.name;
						params.ceoapprove = this.result ? true : false;
					};
				} else {
					// 判断申请人是否是总部的，不是的话 走六级流程
					// 六个节点的第一节点 店长:manager 第二节点 人事:hr 第三节点 区域经理:regional  
					// 第四节点 运营总监:director 第五节点 副总裁:vicepresident 第六节点 总裁班:ceo
					if (this.step == 'addleave') {
						params.manager = this.leader.name;
						params.managerapprove = this.result ? true : false;
					} else if (this.step == 'managercheck') {
						params.hr = this.leader.name;
						params.hrapprove = this.result ? true : false;
					} else if (this.step == 'hrcheck') {
						params.regional = this.leader.name;
						params.regionalapprove = this.result ? true : false;
					} else if (this.step == 'regionalcheck') {
						params.director = this.leader.name;
						params.directorapprove = this.result ? true : false;
					} else if (this.step == 'directorcheck') {
						params.vicepresident = this.leader.name;
						params.vicepresidentapprove = this.result ? true : false;
					} else if (this.step == 'vicepresidentcheck') {
						params.ceo = this.leader.name;
						params.ceoapprove = this.result ? true : false;
					}
				}
			}
			if (this.step == 'ceocheck' || state == 2 || state == 3) {
				params.applyId = this.info.businessKey;
				params.applyType = this.info.processName;
				params.state = state;
				if (params.applyType.includes('离职')) {
					params.applyTime = this.info.startTime;
					params.name = this.info.starter;
				}
			};
			if (this.info.isJob === 1) {
				req.postRequest(`/shopApi/translate/updateJob`, {
					id: this.info.businessKey,
					departmentId: this.jobInfo.departmentId, // 部门id
					name: this.info.starter,// 姓名
					period: this.jobInfo.period,// 试用期 例: 5 -----> 代表5个月
					periodSalary: this.jobInfo.periodSalary,// 试用期工资
					regularizationSalary: this.jobInfo.regularizationSalary, // 转正薪资
					prePost: this.jobInfo.prePostId,//拟录用岗位
					contractPeriod: this.jobInfo.contractPeriod,//劳动合同期限
					signCompany: this.jobInfo.signCompany,//签约公司
					annex: JSON.stringify(this.fileUrl),//附件
					remark: this.jobInfo.remark //备注
				}, res => {
					console.log(res, '修改入职申请');
					if (this.step == 'ceocheck' && state === 1) {
						req.postRequest(`/shopApi/translate/insertStaff`, this.jobInfo, res => {
							console.log(res, "新增成功");
							this.completeTask(params, state);
						});
					} else {
						this.completeTask(params, state);
					}
				});
			} else {
				this.completeTask(params, state);
			}
		},
		completeTask(params, state, callback) {
			req.postRequest(`/shopApi/translate/completeTask/${this.info.taskId}`, params, res => {
				console.log(res, '-----');
				if (state === 2 || state === 3) {
					req.getRequest('/shopApi/translate/forceEnd', {
						taskId: this.info.taskId,
						// sid: params.sid
					}, res => {
						console.log(res);
						req.msg('已拒绝');
						uni.hideLoading();
						setTimeout(() => {
							uni.navigateBack({});
						}, 1500);
					});
				} else {
					// // ceo则调用新增员工 接口
					// if (this.step == 'ceocheck' || state == 1) {
					// }
					uni.hideLoading();
					req.msg('已审核');
					setTimeout(() => {
						uni.navigateBack({});
					}, 1500);

				}
				callback && callback(res);
			});
		},
		// 关闭详情弹窗
		closeDetailPopup() {
			this.$refs.detailPopup.close();
		}
	},
}
</script>

<style src="./approvalProgress.scss" lang="scss" scoped></style>
