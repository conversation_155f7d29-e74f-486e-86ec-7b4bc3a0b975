.schedule-detail {
  padding: 20rpx;

  .header {
    margin-bottom: 30rpx;

    .staff-info {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .name {
        font-size: 32rpx;
        font-weight: bold;
      }

      .department {
        font-size: 28rpx;
        color: #666;
      }
    }
  }

  .calendar {
    background: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 30rpx;

    .month-switcher {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .current-month {
        font-size: 32rpx;
        font-weight: bold;
      }

      .prev,
      .next {
        color: #2196f3;
        padding: 10rpx 20rpx;
      }
    }

    .week-header {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      text-align: center;
      margin-bottom: 10rpx;

      text {
        padding: 10rpx;
        color: #666;
      }
    }

    .calendar-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 2rpx;
      background: #f5f5f5;

      .calendar-cell {
        background: #fff;
        aspect-ratio: 1;
        padding: 10rpx;
        display: flex;
        flex-direction: column;
        align-items: center;

        &.other-month {
          opacity: 0.5;
        }

        .date {
          font-size: 24rpx;
          margin-bottom: 10rpx;
        }

        .schedule-type {
          font-size: 20rpx;
          padding: 4rpx 8rpx;
          border-radius: 4rpx;
          background: #e0e0e0;
        }
      }
    }
  }

  .legend {
    display: flex;
    justify-content: center;
    gap: 30rpx;
    margin-bottom: 30rpx;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 10rpx;

      .color-block {
        width: 20rpx;
        height: 20rpx;
        border-radius: 4rpx;
      }

      text {
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 20rpx;
    padding: 20rpx;

    .btn {
      flex: 1;
      padding: 20rpx;
      text-align: center;
      border-radius: 8rpx;
      font-size: 28rpx;

      &.primary {
        background: #2196f3;
        color: #fff;
      }

      &.secondary {
        background: #f5f5f5;
        color: #333;
      }
    }
  }
}
