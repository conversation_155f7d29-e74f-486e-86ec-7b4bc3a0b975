{"appid": "wxa453effc4de53ac2", "compileType": "miniprogram", "libVersion": "3.6.5", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "ignoreDevUnusedFiles": false, "ignoreUploadUnusedFiles": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}