.inventory-check {
  min-height: 100vh;
  background-color: #f5f5f5;
  
  .header {
    background: linear-gradient(to bottom, #ff3333, #ff6666);
    padding: 20rpx 30rpx;
    color: #fff;
    position: relative;
    
    .back-icon {
      position: absolute;
      left: 30rpx;
      top: 50%;
      transform: translateY(-50%);
      font-size: 40rpx;
    }
    
    .title {
      text-align: center;
      font-size: 36rpx;
      font-weight: 500;
    }
    
    .action-btns {
      position: absolute;
      right: 30rpx;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      
      .btn {
        margin-left: 20rpx;
        font-size: 40rpx;
      }
    }
  }
  
  .search-container {
    margin: 20rpx;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 10;
  }
  
  .search-box {
    flex: 1;
    background-color: #fff;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    padding: 15rpx 30rpx;
    margin-right: 20rpx;
    
    .search-icon {
      color: #999;
      font-size: 40rpx;
      margin-right: 10rpx;
    }
    
    .search-input {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }
    
    .scan-icon {
      font-size: 40rpx;
      color: #999;
    }
  }
  
  .search-btn {
    width: 120rpx;
    height: 70rpx;
    background-color: #ff3333;
    color: #fff;
    font-size: 28rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  // 搜索下拉框样式
  .search-dropdown {
    position: absolute;
    left: 20rpx;
    right: 140rpx;
    top: 280rpx;
    background-color: #fff;
    border-radius: 0 0 20rpx 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    z-index: 9;
    
    .dropdown-scroll {
      width: 100%;
    }
    
    .dropdown-item {
      padding: 20rpx 30rpx;
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:active {
        background-color: #f9f9f9;
      }
      
      .item-name {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 10rpx;
        font-weight: 500;
      }
      
      .item-spec {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
  
  .user-info {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    display: flex;
    align-items: center;
    
    .avatar {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    
    .info {
      flex: 1;
      
      .name {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 10rpx;
      }
      
      .store {
        font-size: 26rpx;
        color: #666;
      }
    }
  }
  
  .section {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 20rpx;
    
    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      
      .section-title {
        font-size: 30rpx;
        font-weight: bold;
        position: relative;
        padding-left: 20rpx;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 8rpx;
          height: 30rpx;
          background-color: #ff3333;
          border-radius: 4rpx;
        }
      }
      
      .edit-btn {
        margin-left: auto;
        background-color: #2b85e4;
        color: #fff;
        font-size: 30rpx;
        padding: 6rpx 20rpx;
        border-radius: 30rpx;
      }
    }
    
    .product-info {
      display: flex;
      flex-direction: column;
      
      .info-row {
        display: flex;
        margin-bottom: 15rpx;
        font-size: 28rpx;
        
        .label {
          width: 180rpx;
          color: #666;
        }
        
        .value {
          flex: 1;
          color: #333;
        }
      }
    }
    
    .batch-info {
      .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15rpx;
        
        .label {
          color: #666;
          font-size: 28rpx;
        }
        
        .value {
          color: #333;
          font-size: 28rpx;
          
          &.highlight {
            color: #ff3333;
            font-weight: bold;
          }
        }
      }
    }
  }
  
  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    
    .action-btn {
      flex: 1;
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      font-size: 32rpx;
      
      &.submit {
        background-color: #fff;
        color: #333;
      }
      
      &.add {
        background-color: #ff3333;
        color: #fff;
      }
    }
  }
} 