<template>
  <view style="padding-bottom:150rpx;">
    <view v-if="!productPointOpen">
      <view v-if="signInfo.refuseReason && signInfo.isRatify == 4" class='content'>
        <view class="title" style="border:none">
          <view class="df" style="margin-bottom:10px;">
            <img class="shu"
              src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240805/fec7b98eb8714ecc8b58ddccadd78fc8.png" alt="">
            <text class="Copay">医助拒绝原因</text>
          </view>
          <view>
            {{ signInfo.refuseReason }}
          </view>
        </view>
      </view>
      <view class="content">
        <view class="title">
          <view style="display: flex;justify-content: space-between;">
            <view class="df" style="margin-bottom:10px;">
              <img class="shu"
                src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240805/fec7b98eb8714ecc8b58ddccadd78fc8.png"
                alt="">
              <text class="Copay">患者信息</text>
            </view>
            <view v-if="signInfo.id" @click="fixedPoint" style="color:#0173ff;font-size:24rpx;">{{
              signInfo.isSettleDown == 1 ?
                '已定点' : '未定点' }}</view>
          </view>
          <view class="infoDet df">
            <view class="leftInfo">问诊类型:</view>
            <view>复诊</view>
            <!-- <uni-data-select v-if="staffStoreId == 9" :clear="false" :disabled="state == 2 || state == 8 || state == 9"
              v-model="signInfo.isFirst" :localdata="isFirstList"></uni-data-select> -->
          </view>
          <view class="infoDet df">
            <view class="leftInfo">接诊医生:</view>
            <view style='position: relative;flex:1;'>
              <uni-data-select v-if="!isDisabled" style="width:100%" v-model="doctor.doctorId"
                :localdata="doctorList" />
              <view v-else>{{ doctor.doctorName }}</view>
              <!-- <uni-easyinput :disabled="isDisabled" @input="getDoctor" v-model="doctor.doctorName"
                placeholder="请输入内容"></uni-easyinput>
              <view v-if="doctorList.length > 0"
                style="position: absolute;top:37px;left:0;z-index:10;background: #fff;width: 100%;max-height:200px;overflow: auto;border-radius: 0 0 8px 8px;box-shadow: 0px 5px 5px 0px #ccc;">
                <view v-for="(item, index) in doctorList" @click="activeDoctor(item)" style="padding:8px;" :key="index">
                  {{ item.doctorName }}
                </view>
              </view> -->
            </view>
          </view>
          <view v-if="signInfo.isFirst == 0" class="infoDet df">
            <view class="leftInfo">首诊机构:</view>
            <view style='position: relative;flex:1;'>
              <uni-easyinput @input="getInstitution" :disabled="isDisabled" v-model="signInfo.institution"
                placeholder="请输入内容"></uni-easyinput>
              <view v-if="institutionList.length > 0"
                style="position: absolute;top:37px;left:0;z-index:10;background: #fff;width: 100%;max-height:200px;overflow: auto;border-radius: 0 0 8px 8px;box-shadow: 0px 5px 5px 0px #ccc;">
                <view @click="activeInstitution(item)" style="padding:8px;" v-for="item in institutionList"
                  :key="item.id">
                  {{ item.name }}
                </view>
              </view>
            </view>
          </view>
          <view v-if="signInfo.isFirst == 0" class="infoDet df">
            <view class="leftInfo">首诊时间:</view>
            <uni-datetime-picker :disabled="isDisabled" v-model="signInfo.firstTime" type="date" :clearIcon="false"
              @change="dateChange" :end="new Date().toISOString().split('T')[0]">
            </uni-datetime-picker>
          </view>
          <view v-if="signInfo.isFirst == 0" class="nav">
            <view class="case_left" style="width:80px;">上传处方单:</view>
            <uni-file-picker :value="signInfo.imgs" limit="5" @select="(e) => handleUpload(e, 'imgs')"
              @delete="(e) => clearFiles(e, 'imgs')"></uni-file-picker>
          </view>
          <view class="infoDet df">
            <view class="leftInfo">姓名:</view>
            <uni-easyinput :disabled="isDisabled" v-model="signInfo.name" placeholder="请输入内容"></uni-easyinput>
          </view>
          <view class="infoDet df">
            <view class="leftInfo">性别:</view>
            <uni-data-checkbox :disabled="isDisabled" selectedTextColor="#004ed6" selectedColor="#004ed6"
              v-model="signInfo.sex" :localdata="sexList"></uni-data-checkbox>
          </view>
          <view class="infoDet df">
            <view class="leftInfo">年龄:</view>
            <uni-easyinput :disabled="isDisabled" v-model="signInfo.age" placeholder="请输入内容"></uni-easyinput>
          </view>
          <view class="infoDet df">
            <view class="leftInfo">身份证号:</view>
            <uni-easyinput :disabled="isDisabled" v-model="signInfo.idCard" placeholder="请输入内容"></uni-easyinput>
          </view>
          <view class="infoDet df">
            <view class="leftInfo">联系方式:</view>
            <uni-easyinput :disabled="isDisabled" v-model="signInfo.tel" placeholder="请输入内容"></uni-easyinput>
          </view>
          <!-- <view class="infoDet df">
            <view class="leftInfo">统筹累计消费:</view>
            <view class="rightInfo c9">
              {{ signInfo.hisInsuranceFee }}元
            </view>
          </view> -->
          <!-- <view class="infoDet df">
            <view class="leftInfo">推荐人:</view>
            <view class="rightInfo c9">
              {{ signInfo.pname }}
            </view>
          </view> -->
          <view style="display: flex;align-items: center;">
            <view class='leftInfo' style="margin-right:30rpx;">配送方式:</view>
            <uni-data-checkbox v-model="mode" selectedTextColor="#004ed6" selectedColor="#004ed6" :localdata="range"
              @change="onChangeCheck"></uni-data-checkbox>
          </view>
          <view @click="goUrl(`/staff/addressList/addressList?userId=${userId}&choice=1`)" v-if="mode != 1"
            class="infoDet df">
            <view class="leftInfo">地址:</view>
            <view v-if="address.id" style="flex:1;display: flex;align-items: center;">
              <view style="width:220px;">{{ address.address }}{{ address.house || "" }}</view>
              <image mode="widthFix" style="width:20rpx;margin-left:20rpx;"
                src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241022/e1a1a5f8e10741799448da64bafd00fc.png">
              </image>
            </view>
            <view v-else>请选择地址</view>
          </view>
          <view class="infoDet df">
            <view class="leftInfo">导诊门店:</view>
            <uni-easyinput v-if="mode == 2 && isChinese == 0" disabled
              v-model="defaultMerchant.storeName"></uni-easyinput>
            <uni-easyinput v-else-if="mode == 2 && isChinese == 1" disabled
              v-model="chineseMerchant.storeName"></uni-easyinput>
            <view v-else style='position: relative;flex:1;'>
              <uni-easyinput disabled @input="getMerInfo" v-model="merchant.storeName"
                placeholder="请输入内容"></uni-easyinput>
              <!-- <view v-if="merchantList.length > 0"
                style="position: absolute;top:37px;left:0;z-index:10;background: #fff;width: 100%;max-height:200px;overflow: auto;border-radius: 0 0 8px 8px;box-shadow: 0px 5px 5px 0px #ccc;">
                <view @click="activeMerchant(item)" style="padding:8px;" v-for="item in merchantList" :key="item.id">
                  {{ item.storeName }}
                </view>
              </view> -->
            </view>
          </view>
        </view>
      </view>
      <view class="content">
        <view class="title">
          <view class="df" style="margin-bottom:10px;">
            <view style="display: flex;align-items: center;"><img class="shu"
                src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240805/fec7b98eb8714ecc8b58ddccadd78fc8.png"
                alt="">
              <text class="Copay">确诊用药</text>
            </view>
            <view class="createBox">
              <view v-if="state == 0 || state == 1" class="createImg" @click="openProduct">添加医嘱</view>
            </view>
          </view>

          <view>
            <!-- 药品搜索 -->
            <view class="drugSearchs" style="margin-top:20rpx;position:relative;">
              <image src="/static/ssico.png" class="ssico"></image>
              <input :disabled="isDisabled" class="searchInp" placeholder="请输入商品名称" @input='ZYSearch'
                @confirm="ZYSearch">
              <!-- 药品搜索列表显示 -->
              <view v-if="ZYSearchList.length != 0 && showZYSearchBox"
                style="position:absolute;top:100%;left:0;z-index:999;background:#fff;width:100%;max-height:400rpx;overflow-y:auto;box-shadow:0 4px 12px rgba(0,0,0,0.15);border-radius:0 0 12rpx 12rpx;">
                <view v-for="(item, index2) in ZYSearchList" @click="clickZYSearch(item, index2)" :key="index2"
                  style="padding:20rpx;border-bottom:1px solid #f5f5f5;font-size:26rpx;transition:all 0.2s ease;cursor:pointer;"
                  hover-class="search-item-hover">
                  <rich-text v-if="ZYSearchList[0].name" :nodes="item.title"></rich-text>
                  <rich-text v-else :nodes="item.title"></rich-text>
                </view>
              </view>
            </view>

            <view @click="handleOutsideClick">
              <!-- 清单列表 -->
              <view class="detailed_list padding">
                <view style="display: flex;align-items: center;justify-content: space-between;">
                  <view>清单：</view>
                  <view style="font-weight:normal;color:#0173ff" @click="AllClear"
                    v-if="chooseIdsString.length > 0 && state != 2 && state != 9">
                    删除商品</view>
                </view>
                <view class="list2">
                  <block v-for="(item3, idx) in pageLists" :key="idx">
                    <view style="border-bottom: 1px solid #eef0f5;">
                      <view class="li">
                        <view class="checkbox" @click="goodsRadio(item3)">
                          <image class="checkImg" :src="item3.isActive ? '/static/gou_h.png' : '/static/gou.png'">
                          </image>
                        </view>
                        <view class="info dflex flex">
                          <view class="cimgs">
                            <image :src="item3.productPic + '?x-oss-process=style/w160'" class="cimg"></image>
                          </view>
                          <view class="zhinfo">
                            <view :class="'zhtit ' + (item3.store <= 0 || item3.isInvalid ? 'color999' : '')">
                              {{ item3.productName }}
                            </view>

                            <view class="jiage" style="margin-top: 14rpx;">
                              <view class="zhjia">
                                <text>￥</text>
                                <text>{{ item3.insuranceFee ? item3.insuranceFee : item3.overplainMoney }}</text>
                              </view>
                              <!-- <view class="zhjia"
                                  v-if="item3.kickback ">
                              <text>和币：</text>
                              <text>{{ item3.kickback }}</text>
                            </view> -->
                              <view class="num">
                                <view class="jian" :class="isInputDisabled ? 'inputDisabled' : ''"
                                  @click="jianQuantity(item3)" :data-idx="idx">-
                                </view>
                                <input class="nums" :value="item3.quantity" type="number" @input="getNum(item3, $event)"
                                  :data-idx="idx" :disabled="isDisabled" />
                                <view class="jian" :class="isInputDisabled ? 'inputDisabled' : ''"
                                  @click="jiaQuantity(item3)" :data-idx="idx">+
                                </view>
                              </view>
                            </view>
                            <view v-if="item3.purchaseNo != 3" class="usage-container">
                              <view style="display:flex;align-items:center;font-size:22rpx;">
                                <text style="margin-right:8rpx;">用法:</text>
                                <input
                                  style="flex:1;height:50rpx;background:#f5f7fa;border:1px solid #eef0f5;border-radius:8rpx;padding:0 8rpx;max-width:150rpx;"
                                  type="text" @blur="updateUsageAndFrequent(item3, idx)" v-model="item3.usages" />
                                <text style="margin:0 8rpx 0 16rpx;">一天</text>
                                <input
                                  style="width:30rpx;height:50rpx;background:#f5f7fa;border:1px solid #eef0f5;border-radius:8rpx;padding:0 4rpx;text-align:center;"
                                  type="text" v-model="item3.frequent" @blur="updateUsageAndFrequent(item3, idx)" />
                                <text style="margin:0 16rpx 0 8rpx;">次</text>
                                <text style="margin-right:8rpx;">一次</text>
                                <input
                                  style="width:30rpx;height:50rpx;background:#f5f7fa;border:1px solid #eef0f5;border-radius:8rpx;padding:0 4rpx;text-align:center;"
                                  type="text" v-model="item3.singleDose" @blur="updateUsageAndFrequent(item3, idx)" />
                                <text style="margin-left:8rpx;">{{ item3.unit }}</text>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                      <view style="display: flex;margin-bottom: 20rpx;align-items: flex-start;"
                        v-if="item3.purchaseNo != 3">
                        <view style="margin-top: 10rpx;">追溯码：</view>
                        <view style="flex: 1;">
                          <view style="display: flex;align-items: center;margin-bottom: 6rpx;"
                            v-for="(val, key) in item3.traceableCode" :key="key">
                            <input
                              style="height: 60rpx; border: 1px solid #eef0f5;border-radius: 8rpx;margin-right: 10rpx;flex: 1;background: #f5f7fa;"
                              :value="val" @blur="changeVal($event, idx, key)">
                            <view
                              style="background-color: #F5F5F5;border-radius: 50%;width: 50rpx;height: 50rpx;line-height: 50rpx;text-align: center;margin-right: 10rpx;"
                              @click="scanCode(idx, key)">
                              <i class="iconfont" style="color: #4E5969;font-size: 40rpx;">&#xe697;</i>
                            </view>
                            <view
                              style="background-color: #F5F5F5;border-radius: 50%;width: 50rpx;height: 50rpx;line-height: 50rpx;text-align: center;"
                              @click="deleteCode(idx, key)" v-if="key != 0">
                              <i class="iconfont" style="color: #4E5969;font-size: 40rpx;">&#xe60a;</i>
                            </view>
                            <view
                              style="background-color: #E91306;border-radius: 50%;width: 50rpx;height: 50rpx;line-height: 50rpx;text-align: center;"
                              @click="addCode(idx)" v-if="key == 0">
                              <i class="iconfont" style="color: #FFFFFF;font-size: 24rpx;">&#xe634;</i>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </block>
                </view>
              </view>
              <!-- 药剂数 -->
              <view class="YaoJiNum" v-if="isChinese == 1">
                <text>药剂数量：</text>
                <view class="dflex" style="color:red;">
                  <view class="num">
                    <view class="jian" :class="isInputDisabled ? 'inputDisabled' : ''" @click="decrement"
                      :disabled="isDisabled">-
                    </view>
                    <input class="nums" v-model="doseQuantity" type="number" :disabled="isDisabled"
                      @input="validateInput" />
                    <view class="jian" :class="isInputDisabled ? 'inputDisabled' : ''" @click="increment"
                      :disabled="isDisabled">+
                    </view>
                  </view>
                </view>
              </view>

              <!-- 用法用量 -->
              <view class="usages" v-if="isChinese == 1">
                <view style="min-width:80px;margin-bottom: 20rpx;">内服:</view>
                <view class="nav_right">
                  <view class="diagnoseInp">
                    <input style="width:100%" :disabled="isDisabled" v-model="usages" placeholder="请输入用法用量">
                  </view>
                </view>
              </view>
              <view class="usages" v-if="isChinese == 1">
                <view style="min-width:80px">外用</view>
                <view class="nav_right">
                  <view class="diagnoseInp">
                    <input style="width:100%" :disabled="isDisabled" v-model="fesco" placeholder="请输入外用方法">
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="title">
          <view class="df" style="margin-bottom:10px;">
            <img class="shu"
              src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240805/fec7b98eb8714ecc8b58ddccadd78fc8.png" alt="">
            <text class="Copay">病历</text>
          </view>
          <view class="card_body">
            <view class="nav">
              <view class="case_left">疾病类型:</view>
              <view class="nav_right">
                <uni-data-checkbox v-model="signInfo.diseaseType" selectedTextColor="#004ed6" selectedColor="#004ed6"
                  :localdata="diseaseTypes" @change="diseaseTypeChange" :disabled="isDisabled" />
              </view>
            </view>

            <!-- 疾病名称输入/选择 -->
            <view class="nav" v-if="signInfo.diseaseType">
              <view class="case_left">疾病名称:</view>
              <view class="nav_right">
                <view class="diagnoseInp" v-if="signInfo.diseaseType === 1" @click="showPopup(7)">
                  <view style="padding-right:30rpx;">{{ signInfo.diseaseName }}</view>
                  <text @click.stop="deleteText('diseaseName')" v-if="signInfo.diseaseName && !isDisabled"
                    class="iconfont delete_icon">&#xe6d7;</text>
                  <!-- <uni-easyinput @input="handleCommonDiseaseInput" :disabled="isDisabled" v-model="signInfo.diseaseName"
                    placeholder="请输入疾病名称"></uni-easyinput> -->
                </view>
                <uni-data-select style="width:100%" v-if="signInfo.diseaseType === 2" v-model="signInfo.diseaseNumber"
                  :localdata="showdiseaseList" :clear="false" @change="handleChronicDiseaseSelect"
                  :disabled="isDisabled" placeholder="请选择慢性病" />
              </view>
            </view>
            <view class="nav">
              <view class="case_left">主诉:</view>
              <view class="nav_right">
                <view @click="showPopup(1)" class="diagnoseInp">
                  <view style="padding-right:30rpx;">{{ signInfo.chiefComplaint }}</view>
                  <text @click.stop="deleteText('chiefComplaint')" v-if="signInfo.chiefComplaint && !isDisabled"
                    class="iconfont delete_icon">&#xe6d7;</text>
                </view>
              </view>
            </view>
            <view class="nav">
              <view class="case_left">流行病史:</view>
              <view class="nav_right">
                <view @click="showPopup(2)" class="diagnoseInp">
                  <view style="padding-right:30rpx;">{{ signInfo.epidemicHistory }}</view>
                  <text @click.stop="deleteText('epidemicHistory')" v-if="signInfo.epidemicHistory && !isDisabled"
                    class="iconfont delete_icon">&#xe6d7;</text>
                </view>
                <!-- <view class="diagnoseInp">
                  <uni-easyinput :disabled="isDisabled" v-model="signInfo.epidemicHistory"></uni-easyinput>
                </view> -->
              </view>
            </view>
            <view class="nav">
              <view class="case_left">现病史:</view>
              <view class="nav_right">
                <view @click="showPopup(3)" class="diagnoseInp">
                  <view style="padding-right:30rpx;">{{ signInfo.historyOfPresentIllness }}</view>
                  <text @click.stop="deleteText('historyOfPresentIllness')"
                    v-if="signInfo.historyOfPresentIllness && !isDisabled" class="iconfont delete_icon">&#xe6d7;</text>
                </view>
                <!-- <view class="diagnoseInp">
                  <uni-easyinput :disabled="isDisabled" v-model="signInfo.historyOfPresentIllness"></uni-easyinput>
                </view> -->
              </view>
            </view>
            <view class="nav">
              <view class="case_left">既往史:</view>
              <view class="nav_right">
                <view @click="showPopup(4)" class="diagnoseInp">
                  <view style="padding-right:30rpx;">{{ signInfo.pastHistory }}</view>
                  <text @click.stop="deleteText('pastHistory')" v-if="signInfo.pastHistory && !isDisabled"
                    class="iconfont delete_icon">&#xe6d7;</text>
                </view>
                <!-- <view class="diagnoseInp">
                  <uni-easyinput :disabled="isDisabled" v-model="signInfo.pastHistory"></uni-easyinput>
                </view> -->
              </view>
            </view>
            <view class="nav">
              <view class="case_left">体格检查:</view>
              <view class="nav_right"></view>
            </view>
            <view class="vital-signs-row" style="display: flex;justify-content: space-around">
              <view class="vital-sign-item" style="display: flex;align-items: center;font-size:28rpx;">
                <view class="vital-sign-label">体温：</view>
                <view class="vital-sign-input-container" style="width:100px;display: flex;align-items: center;">
                  <view @click="showPopup(8)" class="diagnoseInp vital-sign-input" style="width:50%;">
                    <view style="padding-right:30rpx;">{{ signInfo.temperature }}</view>
                  </view>
                  <text class="vital-sign-unit" style="margin-left:4rpx;">℃</text>
                </view>
              </view>
              <view class="vital-sign-item" style="display: flex;align-items: center;font-size:28rpx;">
                <view class="vital-sign-label">脉搏：</view>
                <view class="vital-sign-input-container" style="width:100px;display: flex;align-items: center;">
                  <view @click="showPopup(9)" class="diagnoseInp vital-sign-input" style="width:50%;">
                    <view style="padding-right:30rpx;">{{ signInfo.pulse }}</view>
                  </view>
                  <text class="vital-sign-unit" style="margin-left:4rpx;">次/分</text>
                </view>
              </view>
            </view>
            <view class="vital-signs-row" style="display: flex;justify-content: space-around">
              <view class="vital-sign-item" style="display: flex;align-items: center;font-size:28rpx;">
                <view class="vital-sign-label">呼吸：</view>
                <view class="vital-sign-input-container" style="width:80px;display: flex;align-items: center;">
                  <view @click="showPopup(10)" class="diagnoseInp vital-sign-input" style="width:50%;">
                    <view style="padding-right:30rpx;">{{ signInfo.breathing }}</view>
                  </view>
                  <text class="vital-sign-unit" style="margin-left:4rpx;">次/分</text>
                </view>
              </view>
              <view class="vital-sign-item" style="display: flex;align-items: center;font-size:28rpx;">
                <view class="vital-sign-label">血压：</view>
                <view class="vital-sign-input-container" style="width:130px;display: flex;align-items: center;">
                  <view @click="showPopup(11)" class="diagnoseInp vital-sign-input" style="width:50%;">
                    <view style="padding-right:30rpx;">{{ signInfo.bloodPressure }}</view>
                  </view>
                  <text class="vital-sign-unit" style="margin-left:4rpx;">mmHg</text>
                </view>
              </view>
            </view>
            <view class="nav">
              <view class="case_left">过敏史:</view>
              <view class="nav_right">
                <view class="diagnoseInp" style="border:none">
                  <uni-easyinput :disabled="isDisabled" v-model="signInfo.allergyHistory"></uni-easyinput>
                </view>
              </view>
            </view>
            <view class="nav">
              <view class="case_left">肝功能:</view>
              <view class="nav_right">
                <view class="diagnoseInp" style="border:none">
                  <uni-easyinput :disabled="isDisabled" v-model="signInfo.liverFunction"></uni-easyinput>
                </view>
              </view>
            </view>
            <view class="nav">
              <view class="case_left">肾功能:</view>
              <view class="nav_right">
                <view class="diagnoseInp" style="border:none">
                  <uni-easyinput :disabled="isDisabled" v-model="signInfo.renalFunction"></uni-easyinput>
                </view>
              </view>
            </view>
            <view v-if="signInfo.isFirst == 0" class="nav">
              <view class="case_left" style="width:80px;">重症佐证:</view>
              <uni-file-picker :value="signInfo.severeImgs" limit="5" @select="(e) => handleUpload(e, 'severe')"
                @delete="(e) => clearFiles(e, 'severe')"></uni-file-picker>
            </view>
            <view class="nav" style="align-items: center;margin-bottom:20rpx;flex-direction: column;">
              <view style="display:flex;width:100%;align-items: center;">
                <text style="width:160rpx;">初步诊断:</text>
                <view class="liste" v-if="TabList.length > 0">
                  <view v-for="(item2, index) in TabList" :key="index" @click="switchTab(index)"
                    :class="['tabItem', isSelected(item2) ? 'itemActive' : '']" :data-index="index" :data-name="item2">
                    {{ item2 }}</view>
                </view>
              </view>
              <view class="searchs">
                <image src="/static/ssico.png" class="ssico"></image>
                <input placeholder="搜索病症" :disabled="isInputDisabled || isDisabled" @input='confirmSearch'
                  @confirm="confirmSearch">
              </view>
            </view>

            <view @click="handleOutsideClick">
              <view v-if="showSearchBox && filterList.length >= 1" class="search_box">
                <view @click="clickSearch(item, index)" v-for="(item, index) in filterList" :key="index"
                  class="search_item">
                  <text>{{ index + 1 }}、</text>
                  <rich-text v-if="filterList[0].name" :nodes="item.name"></rich-text>
                  <rich-text v-else :nodes="item"></rich-text>
                </view>
              </view>
            </view>

            <view class="nav">
              <view class="case_left">使用过的药物:</view>
              <view class="nav_right">
                {{ signInfo.userDrugLog }}
              </view>
            </view>

            <view class="nav">
              <view class="case_left">慢病种类:</view>
              <view class="nav_right">
                {{ signInfo.slowDisease }}
              </view>
            </view>

            <view class="nav">
              <view class="case_left">历史用药:</view>
              <view class="nav_right">
                <view @click="unfoldOpen" style="display: flex;align-items: center;margin-left:30rpx;">
                  <view>展开</view>
                  <view style="font-size: 20rpx;transform: rotate(180deg);margin-left: 10rpx;">▲</view>
                </view>
              </view>
            </view>
            <view v-if="foldOpen">
              <view class="lumpSum_section">
                <!-- <view class="time_year">{{ year }}</view> -->
                <picker mode="selector" :range="yearList" :value="currentYearIndex" @change="yearChange">
                  <view class="time_year">{{ currentYear }}年
                    <view style="font-size: 20rpx;transform: rotate(180deg);margin-left: 10rpx;">▲</view>
                  </view>
                </picker>
                <view style="font-size: 32rpx;font-weight: 900;" class="lumpSum">
                  <view>消费总额</view>
                  <view>{{ lumpSum.totalFee }}</view>
                </view>
                <view class="lumpSum">
                  <view>医保统筹基金支付总额</view>
                  <view>{{ lumpSum.hisInsuranceFee }}</view>
                </view>
                <view class="lumpSum">
                  <view>医保个人账户支付总额</view>
                  <view>{{ lumpSum.hisPersonalMedicalCardFee }}</view>
                </view>
                <view class="lumpSum">
                  <view>门慢统筹支付金额</view>
                  <view>{{ lumpSum.hisInsuranceChronicDiseaseFee }}</view>
                </view>
                <view class="lumpSum">
                  <view>现金和其他支付总额</view>
                  <view>{{ lumpSum.hisWeChatFee }}</view>
                </view>
              </view>

              <view class="details_section" v-for="(item, index) in unfoldList" :key="index">
                <view class="details_time">
                  <view style="border-left:4px solid skyblue;padding-left:20rpx;font-weight: 900;">
                    {{ item[0].years + "年" + item[0].moenths + "月" }}
                  </view>
                  <view style="font-size:26rpx;color:#999;">
                    {{ "共" + item.length + "笔" + "，" + "合计消费" }}
                    {{ item[0].allMoney }}
                  </view>
                </view>
                <view @click="goDetail(item2)" class="details_item" v-for="(item2, index2) in item" :key="index2">
                  <view class="details_top">
                    <view style="font-weight:500;font-size:30rpx;">
                      <view>广东集和堂健康产业管理有限公司</view>
                      <view>伟诚中医门诊部</view>
                    </view>
                    <view style="display: flex;align-items: center;">
                      {{ item2.totalFee.toFixed(2) }}
                      <image src="/static/more.png" mode="widthFix" style="width:14rpx;margin-left:20rpx;">
                      </image>
                    </view>
                  </view>
                  <view class="details_bottom">
                    <view style="border-right:1px solid #ccc;padding-right:15rpx;margin-right:15rpx;">
                      {{ item2.order_state_name }}
                    </view>
                    <view>{{ item2.month }}</view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="finishSubmit" v-if="state == 0 || state == 1" @click=" sumitHanlder()">完诊</view>
      </view>
    </view>
    <uni-popup ref="testPopup" type="bottom" @maskClick="closePopup">
      <view class="popup_content">
        <view>
          <view>新增</view>
          <view style="display: flex;align-items: center;margin-bottom: 20rpx;">
            <view>系统名称：</view>
            <view style="flex:1;">
              <uni-data-select v-model="category" :localdata="categoryList" :clear="false"></uni-data-select>
            </view>
          </view>
          <view style="display: flex;align-items: center;">
            <view>描　　述：</view>
            <uni-easyinput v-model="content" placeholder="输入新增描述"></uni-easyinput>
          </view>
          <view @click="addContent" class="add_btn">新增</view>
        </view>
        <view>选择</view>
        <view style="margin-bottom:10rpx;">
          <uni-easyinput v-model="searchContentValue" @input="searchContent" placeholder="搜索"></uni-easyinput>
        </view>
        <view class="table">
          <view class="table-header">
            <view class="table-cell">系统名称</view>
            <view class="table-cell">描述</view>
          </view>
          <view class="table-group" v-for="(group, category) in groupedData" :key="category">
            <view class="table-cell category-cell">{{ category }}</view>
            <view class="table-cell-group">
              <view @click="searchGroup(item)" class="table-cell content-cell" v-for="(item, index) in group"
                :key="index">
                {{ item.content }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
const req = require('../../utils/request.js')
export default {
  data() {
    return {
      // 基础数据
      mode: 1,
      state: 0, //订单状态
      staffStoreId: req.getStorage("loginInfo").storeId,
      // 年份相关
      yearList: [],
      currentYear: new Date().getUTCFullYear(),
      currentYearIndex: 0,

      // 商品相关
      productPointOpen: false,
      lumpSum: {},
      unfoldList: [],
      categoryList: [],
      contentList: [],
      categoryType: null,
      searchContentValue: "",
      category: "",
      content: "",
      foldOpen: false,

      // 定时器
      timer: null,

      // 配送方式
      range: [
        { "value": 1, "text": "自提", "disable": false },
        { "value": 2, "text": "快递", "disable": false },
        // { "value": 3, "text": "及时达", "disable": false }
      ],

      // 疾病类型
      diseaseTypes: [{
        text: '常见疾病',
        value: 1
      }, {
        text: '慢性病',
        value: 2
      }],

      // 疾病列表
      showdiseaseList: [
        { value: 1, text: '高血压病' },
        { value: 2, text: '高脂血症' },
        { value: 3, text: '糖尿病' },
        { value: 4, text: '骨关节炎' },
        { value: 5, text: '冠心病' },
        { value: 6, text: '类风湿关节炎' },
        { value: 7, text: '慢性阻塞性肺疾病' },
        { value: 8, text: '脑血管病后遗症' },
        { value: 9, text: '肝硬化' },
        { value: 10, text: '克罗恩病' },
        { value: 11, text: '溃疡性结肠炎' },
        { value: 12, text: '慢性肾功能不全' },
        { value: 13, text: '慢性肾小球肾炎' },
        { value: 14, text: '强直性脊柱炎' }
      ],
      sexList: [{ value: 1, text: '男' }, { value: 2, text: '女' }],
      isFirstList: [{ value: 0, text: '复诊' }, { value: 1, text: '初诊' }],
      // 地址相关
      address: {},
      addressList: [],

      // 用户相关
      userId: '', //患者id

      // 病症相关
      arr: [],
      TabList: [],
      filterList: [], //搜索的病症列表
      showSearchBox: false, //搜列表显示

      // 商户相关
      merchant: {
        storeName: "伟诚中医门诊",
        id: "68"
      },
      chineseMerchant: {
        storeName: "伟诚中医门诊",
        id: "68"
      },
      defaultMerchant: {
        storeName: "广州仓",
        id: "60"
      },
      merchantList: [],

      // 医生相关
      doctorList: [],
      doctor: {
        doctorName: '',
        doctorId: null
      },

      // 病历信息
      signInfo: {
        isFirst: 0,
        chiefComplaint: '', //主述
        historyOfPresentIllness: '无', //现病史
        pastHistory: '', //既往史
        allergyHistory: '无', //过敏史
        liverFunction: '正常', //肝功能
        renalFunction: '正常', //肾功能
        epidemicHistory: '', //流病史
        temperature: '', //体温
        pulse: '', //脉搏
        breathing: '', //呼吸
        bloodPressure: '', //血压
        diagnosis: '', //初步诊断
        firstTime: '', // 首诊时间
        diseaseType: 1, // 疾病类型
        diseaseName: '', // 疾病名称
        diseaseNumber: '', // 慢性病编号
        diseaseNameList: [],
      },


      // 药品相关
      pageLists: [], //清单列表
      doseQuantity: 1, //药剂数量
      ZYSearchList: [],
      usages: '', //内服
      fesco: '', //外服
      showZYSearchBox: false, //中药搜索列表显示
      isInputDisabled: false,
      chooseIdsString: [], //选中删除的商品ID数组
      isChinese: 0, //是否中药 0否1是
      cardIds: [],
      registrationId: '', //挂号id
      usePharmacy: false, //医嘱用药模块显示

      // 组合相关
      institutionList: [],
    }
  },
  onLoad(options) {
    console.log('options', options);
    if (options != '' && options != null) {
      this.registrationId = options.id //挂号id
      this.patientId = Number(options.patientId)
      this.state = options.state
      this.userId = options.userId
    }
    if (this.state == 2 || this.state == 8 || this.state == 9) {
      this.range.forEach(item => {
        item.disable = true
      })
    }
    this.getPatientMessage().then(res => {
      this.getOutpatientInfo()
    })
    this.getYearList()
    this.getDoctor()
    if (this.state == 0 || this.state == 1) this.getAddress()
  },
  computed: {
    groupedData() {
      const groups = {};
      if (this.contentList && this.contentList.length) {
        this.contentList.forEach(item => {
          if (!groups[item.category]) {
            groups[item.category] = [];
          }
          groups[item.category].push(item);
        });
      }
      return groups;
    },
    isSelected() {
      return (item2) => {
        return this.arr.includes(item2);
      };
    },
    isDisabled() {
      return this.state == 2 || this.state == 8 || this.state == 9;
    }
  },
  watch: {
    // 监听内服值变化
    usages(newVal) {
      if (newVal && this.fesco) {
        this.fesco = '' // 如果内服有值,清空外用
      }
    },
    // 监听外用值变化
    fesco(newVal) {
      if (newVal && this.usages) {
        this.usages = '' // 如果外用有值,清空内服
      }
    }
  },
  methods: {
    addContent() {
      if (!this.category) return req.msg("请选择系统名称");
      if (!this.content) return req.msg("请输入新增描述");
      const isExist = this.categoryList.filter(item => item.value == this.category);
      req.postRequest("/shopApi/mp/his/addContent", {
        category: isExist[0].category,
        content: this.content,
        type: this.categoryType
      }, res => {
        this.content = "";
        this.category = "";
        req.msg("新增成功");
        this.showPopup(this.categoryType);
      })
    },
    deleteText(type) {
      this.signInfo[type] = ''
      if (type == 'diseaseName') {
        this.signInfo.chiefComplaint = ''
        this.signInfo.epidemicHistory = ''
        this.signInfo.historyOfPresentIllness = ''
        this.signInfo.diseaseNameList = []
      }
    },
    searchGroup(item) {
      if (this.categoryType == 1) {
        this.signInfo.chiefComplaint = item.content;//主诉
      } else if (this.categoryType == 2) {
        this.signInfo.epidemicHistory = item.content;//流行病史
      } else if (this.categoryType == 3) {
        this.signInfo.historyOfPresentIllness = item.content;//现病史
      } else if (this.categoryType == 4) {
        this.signInfo.pastHistory = item.content;//既往史
      } else if (this.categoryType == 7) {
        // 检查是否已选择3个病症
        let currentDiseases = this.signInfo.diseaseName ? this.signInfo.diseaseName.split(',') : [];
        if (currentDiseases.length >= 3) {
          req.msg('最多只能选择3个病症');
          return;
        }
        req.postRequest("/shopApi/mp/his/sortAdd", { id: item.id }, res => { })
        this.signInfo.diseaseNameList.push(item.category)
        this.signInfo.chiefComplaint = ''
        this.signInfo.historyOfPresentIllness = ''
        this.signInfo.epidemicHistory = ''
        this.signInfo.diseaseName = this.signInfo.diseaseName ? this.signInfo.diseaseName + ',' + item.content : item.content;//病症
      } else if (this.categoryType == 8) {
        this.signInfo.temperature = item.content//体温
      } else if (this.categoryType == 9) {
        this.signInfo.pulse = item.content//脉搏
      } else if (this.categoryType == 10) {
        this.signInfo.breathing = item.content//呼吸
      } else if (this.categoryType == 11) {
        this.signInfo.bloodPressure = item.content//血压
      }
      this.contentList = []
      this.searchContentValue = ""
      this.$refs.testPopup.close();
    },
    closePopup() {
      this.contentList = []
      this.searchContentValue = ""
    },
    searchContent(e) {
      this.searchContentValue = e
      // 使用模糊搜索过滤描述内容
      req.getRequest("/shopApi/mp/his/getContentList", {
        type: this.categoryType,
        search: this.searchContentValue,
        category: this.categoryType >= 1 && this.categoryType <= 3 ? this.signInfo.diseaseNameList.join() : ""
      }, res => {
        this.contentList = res.data;
      });
    },
    showPopup(type) {
      if (this.isDisabled) return;
      if ((type == 1 || type == 2 || type == 3) && this.signInfo.diseaseNameList.length == 0 && this.signInfo.diseaseType == 1) {
        return req.msg("请先选择疾病")
      }
      this.categoryType = type;
      req.getRequest("/shopApi/mp/his/getContentCategory", {
        type,
        category: type == 1 || type == 2 || type == 3 ? this.signInfo.diseaseNameList.join() : ""
      }, res => {
        console.log(111, res);
        res.data.forEach((item, index) => {
          item.text = item.category
          item.value = index + 1
        })
        this.categoryList = res.data
        // 默认选择第一个选项
        if (this.categoryList.length > 0) {
          this.category = this.categoryList[0].value;
        }
      })
      req.getRequest("/shopApi/mp/his/getContentList", {
        type,
        category: type == 1 || type == 2 || type == 3 ? this.signInfo.diseaseNameList.join() : ""
      }, res => {
        this.contentList = res.data
      })
      this.$refs.testPopup.open()
    },
    changeVal(e, index, codeIndex) {
      this.$set(this.pageLists[index].traceableCode, codeIndex, e.detail.value);
    },
    // 
    scanCode(index, codeIndex) {
      console.log(index, '1111');
      // this.pageLists[index].traceableCode.push(res.result);
      // 允许从相机和相册扫码
      uni.scanCode({
        success: res => {
          // this.pageLists[index].traceableCode[codeIndex] = res.result;
          this.$set(this.pageLists[index].traceableCode, codeIndex, res.result);
          console.log(this.pageLists[index]);
        }
      });
    },
    addCode(index) {
      this.pageLists[index].traceableCode.push("");
    },
    deleteCode(index, codeIndex) {
      // this.pageLists[index].traceableCode[codeIndex];
      this.pageLists[index].traceableCode.splice(codeIndex, 1);
    },
    getInstitution(e) {
      clearTimeout(this.timer);
      if (!e) {
        this.institutionList = []
        return
      }
      this.timer = setTimeout(() => {
        req.getRequest("/shopApi/shopEmployee/searchRecord", {
          name: e,
          type: 0,
          uid: this.userId
        }, res => {
          console.log(res);
          this.institutionList = res.data
          console.log(this.institutionList);
        })
      }, 1000);
    },
    // 更新用法用量
    updateUsageAndFrequent(val) {
      console.log('val----0', val)
      let params = {
        usages: val.usages ? val.usages : '',
        frequent: val.frequent ? val.frequent : '',
        purchaseId: val.id,
        singleDose: val.singleDose ? val.singleDose : '',
        unit: val.unit ? val.unit : '',
      }
      req.postRequest('/shopApi/shopEmployee/updateUsageAndFrequent', params, res => {
        console.log('修改用药', res);
      })
    },
    goDetail(item) {
      uni.navigateTo({
        url: '/staff/consumptionDetails/consumptionDetails?orderId=' + item.orderId + '&uid=' + this.userId + "&isNew=" + item.isNew
      });
    },
    unfoldOpen() {
      this.foldOpen = !this.foldOpen
      if (this.foldOpen) this.getConsumptionDetails()
    },
    yearChange(e) {
      this.currentYearIndex = e.detail.value
      this.currentYear = this.yearList[e.detail.value]
      this.getConsumptionDetails()
    },
    getYearList() {
      const currentYear = new Date().getFullYear();
      for (let year = currentYear; year >= 2000; year--) {
        this.yearList.push(year);
      }
    },
    getConsumptionDetails() {
      let form = {}
      form.userName = this.signInfo.name
      form.idCard = this.signInfo.idCard
      form.uid = this.userId
      form.year = this.currentYear
      form.phone = this.signInfo.tel
      req.showLoading()
      const request1 = new Promise((resolve, reject) => {
        req.getRequest('/shopApi/shopEmployee/consumptionDetails', form, res => {
          console.log('历史', res);
          res.data.totalFee = res.data.totalFee.toFixed(2)
          res.data.hisInsuranceChronicDiseaseFee = res.data.hisInsuranceChronicDiseaseFee.toFixed(2)
          res.data.hisInsuranceFee = res.data.hisInsuranceFee.toFixed(2)
          res.data.hisPersonalMedicalCardFee = res.data.hisPersonalMedicalCardFee.toFixed(2)
          res.data.hisWeChatFee = res.data.hisWeChatFee.toFixed(2)
          resolve(res.data)
        })
      });
      const request2 = new Promise((resolve, reject) => {
        req.getRequest("/shopApi/shopEmployee/orderConsumptionDetails", form, res => {
          res.data.forEach(item => {
            let tempArr = []
            item.forEach(item2 => {
              tempArr.push(item2.totalFee)
            })
            let totalMoney = tempArr.reduce((total, currentValue) => total + currentValue, 0);
            item[0].allMoney = totalMoney.toFixed(2)
          });
          resolve(res.data)
        })
      });
      Promise.all([request1, request2])
        .then((results) => {
          uni.hideLoading();
          this.lumpSum = results[0]
          this.unfoldList = results[1]
        })
        .catch((error) => {
          uni.hideLoading();
          req.msg("报错了，请稍后再试")
        });
    },

    onChangeCheck(e) {
      // if (e.detail.value == 2) {
      //   this.merchant.storeName = '广州仓'
      //   this.merchant.id = '60'
      // } 
      // else {
      //   this.merchant.storeName = '广州天河伟诚分店'
      //   this.merchant.id = '9'
      // }
    },
    goUrl(url) {
      if (this.isDisabled) return
      uni.navigateTo({ url })
    },
    getAddress() {
      req.getRequest("/shopApi/shopEmployee/get/default", {
        userId: this.userId,
      }, res => {
        console.log(res);
        this.address = res.data
      })
    },
    fixedPoint() {
      req.showLoading()
      req.postRequest("/shopApi/shopEmployee/fixedPoint", {
        uid: this.signInfo.uid,
        userDPId: this.signInfo.id,
      }, res => {
        uni.hideLoading()
      })
    },
    handleUpload(e, type) {
      req.showLoading()
      var tempFilePaths = e.tempFilePaths;
      let uploadPromises = tempFilePaths.map((item) => {
        return new Promise((resolve, reject) => {
          req.uploadFile('/shopApi/home/<USER>', item, data => {
            if (type === 'imgs') {
              this.signInfo.imgs.push({ url: data });
            } else if (type === 'severe') {
              this.signInfo.severeImgs.push({ url: data });
            }
            resolve();
          }, err => {
            reject(err);
          });
        });
      });

      Promise.all(uploadPromises)
        .then(() => {
          uni.hideLoading()
        })
        .catch((error) => {
          uni.hideLoading()
          req.msg(error.msg)
        });
    },
    clearFiles(e, type) {
      if (type === 'imgs') {
        this.signInfo.imgs = this.signInfo.imgs.filter((item, index) => index != e.index)
      } else if (type === 'severe') {
        this.signInfo.severeImgs = this.signInfo.severeImgs.filter((item, index) => index != e.index)
      }
    },
    //打开产品积分
    openProduct() {
      // if (!this.merchant.id) return req.msg("请选择门店")
      // this.productPointOpen = true
      uni.navigateTo({
        url: '/staff/productPointInquiry/productPointInquiry?UserID=' + this.userId + '&regID=' + this.registrationId + '&mode=' + this.mode + '&merchantId=' + this.merchant.id
      })
    },
    handleChildEvent(children) {
      this.productPointOpen = false
      this.getOutpatientInfo()
    },
    activeInstitution(item) {
      this.signInfo.institution = item.name
      this.institutionList = []
      req.getRequest("/shopApi/shopEmployee/searchFirstTime?institution", {
        institution: this.signInfo.institution,
        uid: this.userId
      }, res => {
        console.log(res);
        this.signInfo.firstTime = res.data[0].firstTime
        this.signInfo.imgs = res.data[0].imgs ? JSON.parse(res.data[0].imgs).map(item => ({ url: item })) : []
      })
    },
    activeDoctor(item) {
      console.log(1111, item);
      this.doctor = item
      this.doctorList = []
    },
    activeMerchant(item) {
      this.merchant = item
      this.merchantList = []
    },
    getDoctor(e) {
      // this.doctor = {}
      // clearTimeout(this.timer);
      // if (!e) {
      //   this.doctorList = []
      //   return
      // }
      // setTimeout(() => {
      req.getRequest("/shopApi/shopEmployee/queryDoctorList", {
        // search: e
      }, (res) => {
        console.log(res.data);
        this.doctorList = res.data
        this.doctorList.forEach((item) => {
          item.text = item.doctorName
          item.value = item.doctorId
        })
      });
      // }, 1000);
    },
    //获取门店
    getMerInfo(e) {
      this.merchant = {}
      clearTimeout(this.timer);
      if (!e) {
        this.merchantList = []
        return
      }
      this.timer = setTimeout(() => {
        req.getRequest("/shopApi/shopEmployee/getMerInfo", {
          search: e
        }, (res) => {
          this.merchantList = res.data
        });
      }, 1000);
    },
    switchTab(index) {
      console.log('index', index);
      let name = this.TabList[index];
      if (this.arr.find(item => item == name)) {
        this.arr.splice(this.arr.indexOf(name), 1);
      } else if (name && this.arr.length < 3) {
        this.arr.push(name);
      }
      if (this.arr.length > 3) {
        return req.msg('选择不能超过三个');
      }
      this.signInfo.diagnosis = this.arr
    },
    // 搜索
    confirmSearch(e) {
      clearTimeout(this.timer)
      let name = e.detail.value;
      console.log(name);
      this.showSearchBox = true
      if (!name) {
        this.filterList = []
      } else {
        this.timer = setTimeout(() => {
          req.getRequest('/shopApi/shopEmployee/queryIcdCodes', {
            name,
          }, res => {
            console.log('搜索', res);
            if (res.length == 0) {
              this.filterList = []
              return req.msg('没找到相关的病症')
            }
            this.filterList = res.data
          })
        }, 1000)
      }
    },

    // 隐藏 search_box 区域的内容
    handleOutsideClick() {
      this.showSearchBox = false;
      this.showZYSearchBox = false;
    },
    //搜索选择
    clickSearch(item) {
      console.log('tab', this.signInfo.diagnosis, this.arr);
      let selectedDiseases = this.arr
      let name = item.name
      if (selectedDiseases.length >= 3) {
        return req.msg('选择不能超过三个');
      }

      const isAlreadySelected = this.TabList.includes(item.name);
      if (isAlreadySelected) {
        return req.msg('已经存在该病症');
      }

      this.arr.push(name);
      this.TabList.push(name)
      this.signInfo.diagnosis = this.arr
    },

    // 药品搜索
    ZYSearch(e) {
      if (!this.merchant.id) {
        req.msg("请先选择门店")
        return
      }

      clearTimeout(this.timer)
      this.showZYSearchBox = true
      if (!e.detail.value) {
        this.ZYSearchList = []
      } else {
        this.timer = setTimeout(() => {
          req.getRequest('/shopApi/shopEmployee/searchProductEm', {
            searchTitle: e.detail.value,
            page: 1,
            limit: 10,
            isOverallPlanning: 1,
            carryGoodsMode: this.mode,
            merchantId: this.mode == 2 ? this.defaultMerchant.id : this.merchant.id
          }, res => {
            if (res.length == 0) {
              this.ZYSearchList = []
              return req.msg('没找到相关的药品')
            }
            this.ZYSearchList = res.data.list
          })
        }, 1000)
      }
    },
    getRandomNumber(min, max, keepDecimal = true) {
      const random = Math.random() * (max - min) + min;
      if (keepDecimal) {
        return Math.round(random * 10) / 10;
      } else {
        return Math.round(random);
      }
    },
    clickZYSearch(index) {
      this.showZYSearchBox = false; // 关闭下拉框
      req.postRequest('/shopApi/shopEmployee/addCartUser', {
        registrationId: this.registrationId, //订单id
        quantity: 1, //数量
        uid: this.patientId, //患者id
        productId: index.id, //商品id
        merchantId: this.mode == 2 && this.isChinese == 0 ? this.defaultMerchant.id : this.mode == 2 && this.isChinese == 1 ? this.chineseMerchant.id : this.merchant.id, //根据就诊模式和药品类型选择不同的商户ID
        state: 3,
        purchaseNo: index.purchaseNo
      }, res => {
        console.log('中药购物车', res);
        this.getOutpatientInfo()
      })
    },

    // 获取患者信息
    getPatientMessage() {
      return new Promise((resolve, reject) => {
        req.showLoading()
        req.getRequest('/shopApi/shopEmployee/getPatientMessage', {
          registrationId: this.registrationId,
          uid: this.userId
        }, res => {
          console.log('患者信息', res);
          if (res) {
            uni.hideLoading()
            this.signInfo = res.data
            this.signInfo.imgs = this.signInfo.imgs ? JSON.parse(this.signInfo.imgs).map(item => ({ url: item })) : []
            this.signInfo.severeImgs = this.signInfo.severeImgs ? JSON.parse(this.signInfo.severeImgs).map(item => ({ url: item })) : []
            this.state = res.data.state
            if (res.data.dose) {
              this.doseQuantity = res.data.dose
            }
            this.usages = res.data.usages
            this.fesco = res.data.fesco
            if (!this.signInfo.diseaseName) this.signInfo.diseaseName = ""
            this.signInfo.allergyHistory = this.signInfo.allergyHistory || '无'
            this.signInfo.liverFunction = this.signInfo.liverFunction || '正常'
            this.signInfo.renalFunction = this.signInfo.renalFunction || '正常'
            if (this.isDisabled) {
              this.address.id = res.data.addressId
              this.address.address = res.data.addressName
              this.merchant.storeName = res.data.merchantName
              this.merchant.id = res.data.merchantId
              this.doctor.doctorName = res.data.doctorName
              this.doctor.id = res.data.id
              this.mode = res.data.mode
              this.signInfo = res.data
              // if (!res.data.pulse) {
              //   this.getIcdCodes()
              // }
            }
            if (res.data.mode) {
              this.mode = res.data.mode
            } else {
              this.merchant = this.chineseMerchant
            }
          }
          resolve()
        })
      })
    },

    // 获取用药列表
    getOutpatientInfo(type) {

      let registrationId = this.registrationId

      req.getRequest('/shopApi/shopEmployee/getCart', {
        registrationId
      }, res => {
        console.log('用药列表', res);
        this.chooseIdsString = []
        if (Array.isArray(res.data) && res.data.length) {
          this.pageLists = res.data
          this.cardIds = res.data.map(it => it.id)
          // 根据第一个药品的类型判断是中药还是西药
          this.isChinese = res.data[0].purchaseNo === 3 ? 1 : 0
        } else {
          this.pageLists = []
          this.cardIds = ''
          this.isChinese = 0 // 清空时默认为西药
        }
      })
      // if (this.state !== 2 && type !== 1) {
      //   this.getIcdCodes()
      // }
    },

    // 获取主诉现病史
    getIcdCodes() {
      let registrationId = this.registrationId
      req.getRequest('/shopApi/shopEmployee/getIcdCodes', {
        registrationId,
      }, res => {
        // if (res.data.chiefComplaint && res.data.chiefComplaint.includes("发热")) {
        //   this.signInfo.temperature == '' ? this.signInfo.temperature = this.getRandomNumber(37.6, 39.2) : this.signInfo.temperature = this.signInfo.temperature
        // } else {
        //   this.signInfo.temperature == '' ? this.signInfo.temperature = this.getRandomNumber(36.8, 37.2) : this.signInfo.temperature = this.signInfo.temperature
        // }
        if (res.data && res.data.icdCode) {
          this.TabList = res.data.icdCode.split(',')
          this.arr = res.data.icdCode.split(',')
          this.signInfo.diagnosis = this.arr
        } else {
          this.TabList = []
          this.arr = []
          this.signInfo.diagnosis = this.arr
        }
        this.signInfo.chiefComplaint = res.data.chiefComplaint
        this.signInfo.historyOfPresentIllness = res.data.presentIllnessHistory
      })
    },

    // 商品加减
    // 商品勾选
    goodsRadio(chooseItem) {
      this.pageLists.forEach(it => {
        if (it.isActive === undefined) {
          this.$set(it, 'isActive', false);
        }
      });

      if (chooseItem.isActive) {
        const index = this.chooseIdsString.indexOf(chooseItem.id);
        if (index > -1) {
          this.chooseIdsString.splice(index, 1);
        }
      } else {
        this.chooseIdsString.push(chooseItem.id);
      }

      chooseItem.isActive = !chooseItem.isActive;
    },
    // 全部删除
    AllClear() {
      if (this.chooseIdsString.length > 0) {
        req.msgConfirm('确定删除该商品', () => {
          req.postRequest('/shopApi/shopEmployee/deleteCartUser/' + this.chooseIdsString.join(','), {
          }, () => {
            this.getOutpatientInfo()
          });
        });
      }
    },
    //增加按钮
    jiaQuantity(item3) {
      if (this.isDisabled) return
      let num = item3.quantity
      num++
      item3.quantity = num
      if (num > 0) {
        req.postRequest('/shopApi/shopEmployee/updateCartUser', {
          id: item3.id,
          quantity: num,
          merchantId: this.mode == 2 && this.isChinese == 0 ? this.defaultMerchant.id : this.mode == 2 && this.isChinese == 1 ? this.chineseMerchant.id : this.merchant.id,
          productId: item3.productId,
          uid: this.patientId
        }, res => {
          console.log('修改购物车', res);
        })
        setTimeout(() => {
          this.getOutpatientInfo()
        }, 1000)
      }
    },
    // 减少按钮
    jianQuantity(item3) {
      if (this.isDisabled) return
      let num = item3.quantity
      num--
      if (num <= 0) {
        req.msgConfirm('确定删除该商品', () => {
          req.postRequest('/shopApi/shopEmployee/deleteCartUser/' + item3.id, {
          }, () => {
            this.getOutpatientInfo()
          });
        });
      } else {
        item3.quantity = num
        req.postRequest('/shopApi/shopEmployee/updateCartUser', {
          id: item3.id,
          quantity: num,
          merchantId: this.mode == 2 && this.isChinese == 0 ? this.defaultMerchant.id : this.mode == 2 && this.isChinese == 1 ? this.chineseMerchant.id : this.merchant.id,
          productId: item3.productId,
          uid: this.patientId
        }, res => {
          console.log('修改购物车', res);
        })
      }
      if (num > 0) {
        req.postRequest('/shopApi/shopEmployee/updateCartUser', {
          id: item3.id,
          quantity: num,
          merchantId: this.mode == 2 && this.isChinese == 0 ? this.defaultMerchant.id : this.mode == 2 && this.isChinese == 1 ? this.chineseMerchant.id : this.merchant.id,
          productId: item3.productId,
          uid: this.patientId
        }, res => {
          console.log('修改购物车', res);
        })
        setTimeout(() => {
          this.getOutpatientInfo()
        }, 1000)
      }
    },
    // 获取输入的值
    getNum(item3, event) {
      let num = event.detail.value
      if (num > 0) {
        item3.quantity = num
        req.postRequest('/shopApi/shopEmployee/updateCartUser', {
          id: item3.id,
          quantity: num,
          merchantId: this.mode == 2 && this.isChinese == 0 ? this.defaultMerchant.id : this.mode == 2 && this.isChinese == 1 ? this.chineseMerchant.id : this.merchant.id,
          productId: item3.productId,
          uid: this.patientId
        }, res => {
          console.log('res111', res);
        })
        setTimeout(() => {
          this.getOutpatientInfo()
        }, 1000)
      }
      if (num <= 0 && num != '') {
        return num = 1
      }
    },
    sumitHanlder() {
      // 表单验证
      const validations = [
        { condition: this.mode != 1 && !this.address.id, message: "请选择地址" },
        { condition: this.mode != 2 && !this.merchant.id, message: "请选择门店" },
        { condition: !this.signInfo.id && !this.signInfo.name, message: "请输入姓名" },
        { condition: !this.signInfo.id && !this.signInfo.sex, message: "请选择性别" },
        { condition: !this.signInfo.id && !this.signInfo.age, message: "请输入年龄" },
        { condition: !this.signInfo.id && !this.signInfo.idCard, message: "请输入身份证" },
        { condition: !this.signInfo.id && !this.signInfo.tel, message: "请输入电话" },
        { condition: !this.signInfo.chiefComplaint, message: "请输入主述" },
        { condition: !this.signInfo.epidemicHistory, message: "请输入流行病史" },
        { condition: this.signInfo.isFirst == 0 && !this.signInfo.institution, message: "请输入首诊机构" },
        { condition: this.signInfo.isFirst == 0 && this.signInfo.imgs.length == 0, message: "上传病历照片" },
        { condition: !this.signInfo.historyOfPresentIllness, message: "请输入现病史" },
        { condition: !this.signInfo.pastHistory, message: "请输入既往史" },
        { condition: !this.arr?.length, message: "请输入病症" },
        { condition: !this.cardIds?.length, message: "请选择药品" },
        { condition: this.havePrescriptionFormat == 0 && (!this.prescriptionFormat || !this.prescriptionFormat.length), message: "请上传处方单模版图片" },
        { condition: !this.signInfo.diseaseType, message: "请选择疾病类型" },
        { condition: this.signInfo.diseaseType === 1 && !this.signInfo.diseaseName, message: "请输入疾病名称" },
        { condition: this.signInfo.diseaseType === 2 && !this.signInfo.diseaseNumber, message: "请选择慢性病" }
      ]
      for (const validation of validations) {
        if (validation.condition) {
          return req.msg(validation.message)
        }
      }
      let traceableCodeList = [];
      console.log(this.pageLists, 'hhhhhhhhhhhhhhhhhhhhhhhhhh');
      // 提取 traceableCode 并转化为对象数组的形式 `{${item.join(',')}}`
      traceableCodeList = this.pageLists.map(item =>
        item.traceableCode
      ).map(val => `{${val.join(', ')}}`);
      console.log(traceableCodeList);
      // 判断数组是否包含空值，包括空字符串、空对象、null、undefined等
      // const containsEmptyValue = traceableCodeList.some(item => item === "{}");
      // if (containsEmptyValue && this.isChinese != 1) {
      //   return req.msg('药品追溯码不能为空');
      // };
      // 构建提交表单数据对象
      let form = {
        traceableCodeList: traceableCodeList,
        isOnline: 3,
        isRatify: this.signInfo.isRatify, // 审核状态
        isFirst: this.signInfo.isFirst, // 是否首诊 
        institution: this.signInfo.isFirst == 0 ? this.signInfo.institution : "", // 首诊机构
        imgs: this.signInfo.isFirst == 0 ? JSON.stringify(this.signInfo.imgs.map(item => item.url)) : "", // 病历照片
        severeImgs: this.signInfo.isFirst == 0 ? JSON.stringify(this.signInfo.severeImgs.map(item => item.url)) : "", // 重症片
        mode: this.mode, // 就诊模式
        addressId: this.mode != 1 ? this.address.id : "", // 地址ID
        id: this.registrationId,// 挂号ID
        uid: this.patientId ? this.patientId : "", // 用户ID
        patientId: this.signInfo.id ? this.signInfo.id : '', // 患者ID
        name: this.signInfo.name, // 姓名
        sex: this.signInfo.sex == 2 ? 0 : this.signInfo.sex, // 性别
        idCard: this.signInfo.idCard, // 身份证
        age: this.signInfo.age, // 年龄
        tel: this.signInfo.tel, // 电话
        // 根据就诊模式和药品类型选择不同的商户ID
        merchantId: this.mode == 2 && this.isChinese == 0 ? this.defaultMerchant.id : this.mode == 2 && this.isChinese == 1 ? this.chineseMerchant.id : this.merchant.id,
        doctorId: this.doctor.doctorId ? this.doctor.doctorId : "", // 医生ID
        chiefComplaint: this.signInfo.chiefComplaint,// 主述
        historyOfPresentIllness: this.signInfo.historyOfPresentIllness,// 现病史
        pastHistory: this.signInfo.pastHistory, // 既往史
        allergyHistory: this.signInfo.allergyHistory ? this.signInfo.allergyHistory : "无",// 过敏史
        liverFunction: this.signInfo.liverFunction ? this.signInfo.liverFunction : "正常",// 肝功能
        renalFunction: this.signInfo.renalFunction ? this.signInfo.renalFunction : "正常",// 肾功能
        epidemicHistory: this.signInfo.epidemicHistory,// 流行病史
        temperature: this.signInfo.temperature,   // 体温
        pulse: this.signInfo.pulse,   // 脉搏
        breathing: this.signInfo.breathing,   // 呼吸
        bloodPressure: this.signInfo.bloodPressure, // 血压 
        diagnosis: this.signInfo.diagnosis.join(','),  // 初步诊断
        ids: this.cardIds, // 物车ID列表
        uploadPrescription: '', // 处方单上传
        state: 9, // 状态
        firstTime: this.signInfo.isFirst == 0 ? this.signInfo.firstTime : null,
        diseaseType: this.signInfo.diseaseType, // 疾病类型
        diseaseName: this.signInfo.diseaseType === 1 ? this.signInfo.diseaseName :
          (this.signInfo.diseaseType === 2 ? this.showdiseaseList.find(item => item.value === this.signInfo.diseaseNumber)?.text : ''), // 疾病名称
        diseaseNumber: this.signInfo.diseaseType === 2 ? this.signInfo.diseaseNumber : '', // 慢性病编号
      }
      if (this.havePrescriptionFormat == 0) {
        form.uploadPrescription = this.prescriptionFormat[0].path
      }
      req.showLoading()
      if (this.isChinese == 1) {
        // 中药处方校验
        const { doseQuantity, usages, fesco } = this
        // 校验用法用量
        if (!usages && !fesco) {
          return req.msg('请填写用法用量')
        }
        // 提交中药处方
        Object.assign(form, {
          dose: doseQuantity,
          usages,
          fesco,
        })
      } else {
        let ifSubmit = true
        this.pageLists.map((item) => {
          if (!item.frequent || !item.singleDose || !item.usages) {
            ifSubmit = false
          }
        })
        console.log(111, this.pageLists);
        if (ifSubmit == false) return req.msg('请输入用法用量')
      }
      // 提交处方
      req.postRequest('/shopApi/shopEmployee/saveOutpatient', form, res => {
        if (res) {
          uni.hideLoading()
          uni.navigateBack()
        }
      })
    },
    dateChange(value) {
      this.signInfo.firstTime = value;
    },
    // 疾病类型变更处理
    diseaseTypeChange(e) {
      // 清空疾病名称和编号
      this.signInfo.diseaseName = '';
      this.signInfo.diseaseNumber = '';
      this.signInfo.diseaseNameList = []
    },

    // 处理常见疾病输入
    handleCommonDiseaseInput(e) {
      this.signInfo.diseaseName = e.detail.value;
    },

    // 处理慢性病选择
    handleChronicDiseaseSelect(e) {
      const selectedDisease = this.showdiseaseList.find(item => item.value === e);
      if (selectedDisease) {
        this.signInfo.diseaseNumber = selectedDisease.value;
        this.signInfo.diseaseName = selectedDisease.text;
      }
    },

    // 增加药剂数量
    increment() {
      if (this.isDisabled) return
      if (this.doseQuantity >= 7) {
        req.msg('最多不能超过7剂')
        return
      }
      this.doseQuantity++
    },

    // 减少药剂数量 
    decrement() {
      if (this.isDisabled) return
      if (this.doseQuantity <= 1) {
        req.msg('最少不能低于1剂')
        return
      }
      this.doseQuantity--
    },

    // 验证输入的药剂数量
    validateInput(e) {
      let value = parseInt(e.detail.value)
      if (isNaN(value) || value < 1) {
        this.doseQuantity = 1
        req.msg('最少不能低于1剂')
      } else if (value > 7) {
        this.doseQuantity = 7
        req.msg('最多不能超过7剂')
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import './patientInfo.scss';
</style>
