<template>
	<view class="personalData">
		<view
			style="margin:0 20rpx 20rpx;box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.04);background: #fff;border-radius: 10rpx;padding:20rpx 40rpx">
			<view class="real_item" style="margin-top:20rpx;justify-content: space-between;">
				<view style="margin-right:20rpx;display: flex;align-items: center;">
					<i class="iconfont" style="color: #EA1306;font-size: 28rpx;margin-right: 10rpx;">&#xe62b;</i>
					<view style="width: 150rpx;">
						头像
					</view>
				</view>
				<view
					style="width: 96rpx;height: 96rpx;border-radius: 50%;background: rgba(0, 122, 252, 0.1);margin: 15rpx;overflow: hidden;"
					@click="uploadImg">
					<image mode="aspectFit" style="width: 96rpx;height: 96rpx;" :src="Info.avatar"></image>
				</view>
			</view>
			<view class="real_item" style="margin-top:20rpx;">
				<view style="margin-right:20rpx;display: flex;align-items: center;">
					<i class="iconfont" style="color: #EA1306;font-size: 32rpx;margin-right: 10rpx;">&#xe62a;</i>
					<view style="width: 150rpx;">
						昵称
					</view>
				</view>
				<uni-easyinput v-model="Info.memName" placeholder="请输入" :clearable="false"></uni-easyinput>
			</view>
			<view class="real_item" style="margin-top:20rpx;">
				<view style="margin-right:20rpx;display: flex;align-items: center;">
					<i class="iconfont" style="color: #EA1306;font-size: 32rpx;margin-right: 10rpx;">&#xe62e;</i>
					<view style="width: 150rpx;">
						真实姓名
					</view>
				</view>
				<uni-easyinput v-model="Info.phone" placeholder="请输入" :clearable="false"></uni-easyinput>
			</view>
			<view class="real_item" style="margin-top:20rpx;">
				<view style="margin-right:20rpx;display: flex;align-items: center;">
					<i class="iconfont" style="color: #EA1306;font-size: 30rpx;margin-right: 10rpx;">&#xe62c;</i>
					<view style="width: 150rpx;">
						性别
					</view>
				</view>
				<uni-easyinput v-model="Info.phone" placeholder="请输入" :clearable="false"></uni-easyinput>
			</view>
			<view class="real_item" style="margin-top:20rpx;">
				<view style="margin-right:20rpx;display: flex;align-items: center;">
					<i class="iconfont" style="color: #EA1306;font-size: 26rpx;margin-right: 10rpx;">&#xe62f;</i>
					<view style="width: 150rpx;">
						身份证号
					</view>
				</view>
				<uni-easyinput v-model="Info.phone" placeholder="请输入" :clearable="false"></uni-easyinput>
			</view>
			<view class="real_item" style="margin-top:20rpx;">
				<view style="margin-right:20rpx;display: flex;align-items: center;">
					<i class="iconfont" style="color: #EA1306;font-size: 28rpx;margin-right: 10rpx;">&#xe62d;</i>
					<view style="width: 150rpx;">
						出生年月
					</view>
				</view>
				<uni-easyinput v-model="Info.phone" placeholder="请输入" :clearable="false"></uni-easyinput>
			</view>
			<view class="real_item" style="margin-top:20rpx;">
				<view style="margin-right:20rpx;display: flex;align-items: center;">
					<i class="iconfont" style="color: #EA1306;font-size: 28rpx;margin-right: 10rpx;">&#xe631;</i>
					<view style="width: 150rpx;">
						手机号码
					</view>
				</view>
				<uni-easyinput v-model="Info.phone" placeholder="请输入" :clearable="false"></uni-easyinput>
			</view>
			<view class="real_item" style="margin-top:20rpx;">
				<view style="margin-right:20rpx;display: flex;align-items: center;">
					<i class="iconfont" style="color: #EA1306;font-size: 28rpx;margin-right: 10rpx;">&#xe632;</i>
					<view style="width: 150rpx;">
						地区
					</view>
				</view>
				<uni-easyinput v-model="Info.phone" placeholder="请输入" :clearable="false"></uni-easyinput>
			</view>
			<view class="real_item" style="margin-top:20rpx;">
				<view style="margin-right:20rpx;display: flex;align-items: center;">
					<i class="iconfont" style="color: #EA1306;font-size: 28rpx;margin-right: 10rpx;">&#xe632;</i>
					<view style="width: 150rpx;">
						详细地址
					</view>
				</view>
				<uni-easyinput v-model="Info.phone" placeholder="请输入" :clearable="false"></uni-easyinput>
			</view>
			<view class="real_item" style="margin-top:20rpx;">
				<view style="margin-right:20rpx;display: flex;align-items: center;">
					<i class="iconfont" style="color: #EA1306;font-size: 28rpx;margin-right: 10rpx;">&#xe630;</i>
					<view style="width: 150rpx;">
						推荐人
					</view>
				</view>
				<uni-easyinput v-model="Info.phone" placeholder="请输入" :clearable="false"></uni-easyinput>
			</view>
			<view class="real_item" style="margin-top:20rpx;">
				<view style="margin-right:20rpx;display: flex;align-items: center;">
					<i class="iconfont" style="color: #EA1306;font-size: 28rpx;margin-right: 10rpx;">&#xe631;</i>
					<view style="width: 200rpx;">
						推荐人手机号
					</view>
				</view>
				<uni-easyinput v-model="Info.phone" placeholder="请输入" :clearable="false"></uni-easyinput>
			</view>
		</view>
		<view style="display: flex;align-items: center;justify-content: center;margin: 50rpx 50rpx 10rpx 50rpx;">
			<view
				style="width: 500rpx;line-height: 96rpx;text-align: center;line-height: 96rpx;border-radius: 50rpx;background-color: #EA1306;color: #fff;font-size: 36rpx;"
				@click="save">
				保存</view>
		</view>
		<view style="display: flex;align-items: center;justify-content: center;" v-if="false">
			<view style="margin:20rpx;padding:0 40rpx">
				<view style="display:flex;align-items:center;">
					<checkbox-group @change="checkboxChange">
						<checkbox value="1" :checked="isAgree" style="transform:scale(0.7)" />
					</checkbox-group>
					<text style="font-size:26rpx;color:#666;">我已阅读并同意</text>
					<text style="font-size:26rpx;color:#0b78ff;" @click="showPrivacyPolicy">《隐私政策》</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
const req = require("../../utils/request.js")
export default {
	data() {
		return {
			statusState: "",
			Info: {},
			isAgree: false
		};
	},
	onLoad(options) {
		// console.log(options.statusState);
		// this.statusState = Number(options.statusState);
		// if (this.statusState == 3) {
		// 	this.getPersonalData();
		// } else {
		// 	this.getPersonalData_ad();
		// }
	},
	onShow() {

	},
	methods: {
		checkboxChange(e) {
			this.isAgree = e.detail.value.length > 0;
		},
		// 示例
		chooseImg() {
			let src = []
			src.push("https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241206/516c464886474b7a9a5a1e4627e9c67c.png")
			uni.previewImage({
				current: 1,
				// 当前显示图片的http链接  
				urls: src
			});
		},
		// 选择图片上传 - 显示图片
		uploadImg(type) {
			uni.chooseImage({
				count: 1,
				success: (chooseImageRes) => {
					console.log(chooseImageRes);
					const tempFilePath = chooseImageRes.tempFilePaths[0];
					console.log(tempFilePath, "tempFilePaths");
					req.uploadImg('/hisApi/common/upload', tempFilePath, res => {
						console.log(res, '...');
						if (type == 1) {
							this.Info.signImage = res;
						} else {
							this.Info.avatar = res;
						}
					});
				}
			});
		},
		// 我的医生-个人资料-查询
		getPersonalData() {
			req.getRequest("/hisApi/memberships/d/getPersonalData", {}, res => {
				console.log(res, "个人资料");
				this.Info = res.data;

			})
		},
		// 我的医助/我的患者-个人资料-查询
		getPersonalData_ad() {
			req.getRequest("/hisApi/memberships/ad/getPersonalData", {}, res => {
				console.log(res, "个人资料");
				this.Info = res.data;
			})
		},
		// 保存更改
		save() {
			if (!this.Info.avatar) return req.msg("请选择你的头像");
			if (!this.Info.memName) return req.msg("请输入真实名字");
			if (!this.Info.phone || !req.isPhone(this.Info.phone)) return req.msg("请输入正确的手机号码");
			req.msgConfirm('是否保存当前更改', () => {
				if (this.statusState == 3) {
					req.postRequest("/hisApi/memberships/d/updatePersonalData", {
						memName: this.Info.memName,
						phone: this.Info.phone,
						avatar: this.Info.avatar,
						department: this.Info.department,
						technicalTitle: this.Info.technicalTitle,
						introductory: this.Info.introductory,
						doctorId: this.Info.doctorId,
						signImage: this.Info.signImage
					}, res => {
						console.log(res, "保存成功");
						req.msg("保存成功");
					})
				} else {
					req.postRequest("/hisApi/memberships/ad/updatePersonalData", {
						memName: this.Info.memName,
						phone: this.Info.phone,
						avatar: this.Info.avatar,
						staffId: this.Info.staffId ? this.Info.staffId : "",
					}, res => {
						console.log(res, "保存成功");
						req.msg("保存成功");
					})
				}
			})
		},
		showPrivacyPolicy() {
			// 修改跳转路径到 other 分包中的隐私政策页面
			// uni.navigateTo({
			// 	url: '/other/privacyPolicy/privacyPolicy'
			// });
		},
	},
}
</script>

<style lang="scss" src="./personalData.scss" scoped></style>
