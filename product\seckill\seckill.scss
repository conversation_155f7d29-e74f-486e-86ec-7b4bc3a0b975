.seckill-page {
  min-height: 100vh;
  background: #f6f7fb;
  padding-bottom: env(safe-area-inset-bottom);

  .countdown-section {
    background: #ea1306;
    padding-top: 40rpx;
    color: #fff;
    position: relative;
    box-shadow: 0 4rpx 20rpx rgba(234, 19, 6, 0.2);

    &::after {
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      bottom: -40rpx;
      height: 40rpx;
      background: linear-gradient(to bottom, rgba(0, 0, 0, 0.05), transparent);
    }

    .time-block {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 40rpx;
      padding-bottom: 100rpx;

      .time-label {
        font-size: 32rpx;
        margin-right: 24rpx;
        font-weight: 500;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
      }

      .time-numbers {
        display: flex;
        align-items: center;

        .time-unit {
          background: rgba(255, 255, 255, 0.15);
          backdrop-filter: blur(10px);
          padding: 12rpx 24rpx;
          border-radius: 12rpx;
          font-size: 36rpx;
          font-weight: bold;
          font-family: DIN;
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
        }

        .colon {
          margin: 0 12rpx;
          font-weight: bold;
          font-size: 36rpx;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
        }

        .time-label {
          font-size: 32rpx;
          margin: 0 12rpx;
          font-weight: 500;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
        }
      }
    }

    .session-tabs {
      .scroll-view {
        white-space: nowrap;
        width: 100%;

        .session-wrapper {
          display: inline-flex;
          padding: 0 20rpx;

          .session-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-width: 140rpx;
            height: 100rpx;
            margin-right: 20rpx;
            border-radius: 20rpx 20rpx 0 0;
            padding: 0 20rpx;
            color: #fff;

            &.active {
              background: #fff0f0;
              color: #ea1306;
              .time {
                font-weight: bold;
              }
            }

            .time {
              font-size: 28rpx;
              margin-bottom: 8rpx;
            }

            .status {
              font-size: 24rpx;
            }

            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
    }
  }

  .product-list {
    padding: 60rpx 30rpx 30rpx;

    .product-item {
      margin-bottom: 30rpx;
    }

    .product-card {
      background: #fff;
      border-radius: 24rpx;
      overflow: hidden;
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
      transition: all 0.3s;
      display: flex;
      padding: 24rpx;

      &:active {
        transform: scale(0.98);
      }

      .image-wrapper {
        position: relative;

        .yibao-tongchou {
          position: absolute;
          top: 0;
          left: 0;
          background: #ea1306;
          padding: 4rpx 12rpx;
          border-radius: 20rpx 0 20rpx 0;
          color: #fff;
          font-size: 24rpx;
        }

        .product-image {
          width: 240rpx;
          height: 240rpx;
          border-radius: 20rpx;
          flex-shrink: 0;
          border: 4rpx solid #ea1306;
        }

        .date-wrapper {
          width: 100%;
          height: 60rpx;
          background: #ea1306;
          color: #fff;
          position: absolute;
          bottom: 0;
          left: 0;
          border-radius: 0 0 20rpx 20rpx;
          display: flex;
          align-items: center;
          justify-content: space-around;
        }
      }

      .product-info {
        flex: 1;
        padding-left: 24rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .title {
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
          line-height: 1.4;
          margin-bottom: 16rpx;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }

        .stock-info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin: 12rpx 0;
          
          .stock-item {
            display: flex;
            flex-direction: column;
            gap: 6rpx;
            
            .stock {
              font-size: 24rpx;
              color: #999;
            }
          }
          
          .limit {
            font-size: 24rpx;
            padding: 4rpx 12rpx;
            background-color: #FFF0F0;
            color: #ea1306;
            border-radius: 4rpx;
          }
        }

        .price-row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: auto;

          .price {
            display: flex;
            align-items: baseline;

            .symbol {
              font-size: 26rpx;
              color: #ea1306;
              margin-right: 2rpx;
            }

            .number {
              font-size: 44rpx;
              font-weight: bold;
              color: #ea1306;
              font-family: DIN;
            }

            .original {
              font-size: 24rpx;
              color: #999;
              text-decoration: line-through;
              margin-left: 16rpx;
            }
          }

          .buy-btn {
            padding: 12rpx 32rpx;
            background: linear-gradient(135deg, #ff6b6b 0%, #ea1306 100%);
            color: #fff;
            border-radius: 30rpx;
            font-size: 26rpx;
            font-weight: 500;
            box-shadow: 0 4rpx 12rpx rgba(234, 19, 6, 0.2);
            transition: all 0.3s;

            &:active {
              transform: scale(0.95);
            }

            &.disabled {
              background: #ccc;
              box-shadow: none;
            }
          }
        }
      }
    }
  }

  .empty-tip {
    padding: 120rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #999;
    font-size: 28rpx;

    image {
      width: 240rpx;
      height: 240rpx;
      margin-bottom: 30rpx;
      opacity: 0.8;
    }
  }

  .category-title {
    width: 100%;
    height: 100rpx;
    position: sticky;
    top: 0;
    background: #f6f7fb;
    z-index: 1;

    .scroll-view {
      width: 100%;
      height: 100%;
      white-space: nowrap;
    }

    .category-list {
      padding: 20rpx;
      display: flex;
      align-items: center;
      height: 100%;

      .category-item {
        padding: 8rpx 24rpx;
        font-size: 26rpx;
        color: #666;
        position: relative;
        margin-right: 20rpx;
        background: #f5f5f5;
        border-radius: 32rpx;
        transition: all 0.3s ease;

        &.active {
          color: #fff;
          font-weight: 500;
          background: #ea1306;
          box-shadow: 0 4rpx 8rpx rgba(234, 19, 6, 0.2);
        }

        &:last-child {
          margin-right: 20rpx;
        }
      }
    }
  }
}
