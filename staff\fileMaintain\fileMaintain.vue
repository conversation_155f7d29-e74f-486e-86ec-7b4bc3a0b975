<template>
  <view class="fileMaintain">
    <view class="content">
      <view class="user-info">
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>姓名</text>
          </view>
          <view class="right">
            <input type="text" v-model="userInfo.name" placeholder="请输入" :disabled="formIndex === 1" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>性别</text>
          </view>
          <view style="width: 70%">
            <uni-data-select v-model="userInfo.sex" :clear="false" :disabled="formIndex === 1"
              :localdata="sexList"></uni-data-select>
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>出生日期</text>
          </view>
          <view style="width: 70%">
            <uni-datetime-picker type="date" v-model="userInfo.birth" :disabled="formIndex === 1" />
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>年龄</text>
          </view>
          <view class="right">
            <input type="number" v-model="userInfo.age" placeholder="请输入" :disabled="formIndex === 1" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>手机号</text>
          </view>
          <view class="right">
            <input type="number" v-model="userInfo.phone" maxlength="11" placeholder="请输入"
              :disabled="formIndex === 1" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>身份证号</text>
          </view>
          <view class="right">
            <input type="number" v-model="userInfo.idNum" maxlength="18" placeholder="请输入"
              :disabled="formIndex === 1" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>部门</text>
          </view>
          <!-- <view class="right">
            <input
              disabled
              :value="userInfo.divName ? userInfo.divName : '无'"
              maxlength="18"
              placeholder="请输入"
            />
            <text class="iconfont">&#xe667;</text>
          </view> -->
          <view style="width: 70%">
            <next-data-select @change="getPostList()" :filterable="true" :options="divNameList" v-model="userInfo.divId"
              :disabled="formIndex === 1" />
            <!-- <uni-data-select
              v-model="userInfo.divId"
              :clear="true"
              :localdata="divNameList"
            ></uni-data-select> -->
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>岗位</text>
          </view>
          <!-- <view class="right">
            <input
              disabled
              :value="userInfo.postName ? userInfo.postName : '无'"
              maxlength="18"
              placeholder="请输入"
            />
            <text class="iconfont">&#xe667;</text>
          </view> -->
          <view style="width: 70%">
            <uni-data-select v-model="userInfo.post" :clear="true" :localdata="postList"
              :disabled="formIndex === 1"></uni-data-select>
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>门店</text>
          </view>
          <!-- <view class="right">
            <input
              disabled
              :value="userInfo.storeName ? userInfo.storeName : '无'"
              maxlength="18"
              placeholder="请输入"
            />
            <text class="iconfont">&#xe667;</text>
          </view> -->
          <view style="width: 70%">
            <next-data-select :filterable="true" :options="MerList" v-model="userInfo.storeId"
              :disabled="formIndex === 1" />
            <!-- <uni-data-select
              v-model="userInfo.storeId"
              :clear="true"
              :localdata="leaveTypeList"
            ></uni-data-select> -->
          </view>
        </view>
        <view class="info-item" v-if="userInfo.isTest == 0">
          <text class="text-right">试用期时长(月)</text>
          <view class="right">
            <input type="number" v-model="userInfo.trialTime" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">入职时间</text>
          <view style="width: 70%">
            <uni-datetime-picker type="date" v-model="userInfo.entryTime" disabled="true" />
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">购买社保地</text>
          <view class="right">
            <input disabled :value="userInfo.insuranceType ? userInfo.insuranceType : '无'" maxlength="18"
              placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
          <!-- <view style="width: 62%">
            <uni-data-select
              v-model="userInfo.insuranceType"
              :clear="true"
              :localdata="insuranceTypeList"
            ></uni-data-select>
          </view> -->
        </view>
        <view class="info-item">
          <!-- <text class="required">*</text> -->
          <text class="text-right">底薪金额</text>
          <view class="right">
            <input disabled type="number" v-model="userInfo.money" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">考核金额</text>
          <view class="right">
            <input type="number" v-model="userInfo.checkMoney" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">考核</text>
          <view style="width: 62%">
            <uni-data-select v-model="userInfo.isCheck" :clear="true" :localdata="isTrueList"
              disabled="true"></uni-data-select>
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">岗位补贴</text>
          <view class="right">
            <input type="number" v-model="userInfo.jobSubmoney" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">餐补补贴</text>
          <view class="right">
            <input type="number" v-model="userInfo.mealAllowance" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">交通补贴</text>
          <view class="right">
            <input type="number" v-model="userInfo.trafficAllowance" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">药师证补贴</text>
          <view class="right">
            <input type="number" v-model="userInfo.cardAllowance" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">药师驻店补贴</text>
          <view class="right">
            <input type="number" v-model="userInfo.practiceDoctorAllowance" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">住宿补贴</text>
          <view class="right">
            <input type="number" v-model="userInfo.stayAllowance" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>是否试用期</text>
          </view>
          <view style="width: 62%">
            <uni-data-select v-model="userInfo.isTest" :clear="true" :localdata="isTestList"
              :disabled="formIndex === 1"></uni-data-select>
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>机构名称</text>
          </view>
          <!-- <view class="right">
            <input
              disabled
              v-model="userInfo.insName"
              placeholder="请输入"
              disabled="true"
            />
            <text class="iconfont">&#xe667;</text>
          </view> -->
          <view style="width: 62%">
            <uni-data-select v-model="userInfo.insId" :clear="true" :localdata="insIdsList"
              :disabled="formIndex === 1"></uni-data-select>
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">员工状态</text>
          <view class="right">
            <input disabled v-model="userInfo.positionStatusName" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
          <!-- <view style="width: 62%">
            <uni-data-select
              v-model="userInfo.positionStatus"
              :clear="true"
              :localdata="positionStatusList"
            ></uni-data-select>
          </view> -->
        </view>
        <view class="info-item">
          <text>实际转正日期</text>
          <view style="width: 62%">
            <uni-datetime-picker type="date" v-model="userInfo.confirmationDate" disabled="true" />
          </view>
        </view>
        <view class="info-item">
          <text>计划转正日期</text>
          <view style="width: 62%">
            <uni-datetime-picker type="date" v-model="userInfo.plannedConfirmationDate" disabled="true" />
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">转正工资</text>
          <view class="right">
            <input type="number" v-model="userInfo.formalWage" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item" v-if="userInfo.isTest == 0">
          <text class="text-right">试用期工资</text>
          <view class="right">
            <input type="number" v-model="userInfo.probationarySalary" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <text>是否购买社保</text>
          <view style="width: 62%">
            <uni-data-select v-model="userInfo.isSocialSecurity" :clear="true" :localdata="isTrueList"
              disabled="true"></uni-data-select>
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">购买社保主体</text>
          <view class="right">
            <input v-model="userInfo.socialSecuritySubject" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">是否住宿</text>
          <view style="width: 62%">
            <uni-data-select v-model="userInfo.isStay" :clear="true" :localdata="isTrueList"
              disabled="true"></uni-data-select>
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">合同类型</text>
          <view style="width: 62%">
            <uni-data-select v-model="userInfo.contractType" :clear="true" :localdata="contractTypeList"
              disabled="true"></uni-data-select>
          </view>
        </view>
        <!-- <view class="info-item">
          <text>首次合同起始日</text>
          <view style="width: 62%">
            <uni-datetime-picker
              type="date"
              v-model="userInfo.firstContract"
              disabled="true"
            />
          </view>
        </view>
        <view class="info-item">
          <text>首次合同到期日</text>
          <view style="width: 62%">
            <uni-datetime-picker
              type="date"
              v-model="userInfo.contractExpiration"
              disabled="true"
            />
          </view>
        </view> -->
        <view class="info-item">
          <text class="text-right">现合同起始日</text>
          <view style="width: 62%">
            <uni-datetime-picker type="date" v-model="userInfo.nowFirstContract" disabled="true" />
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">现合同到期日</text>
          <view style="width: 62%">
            <uni-datetime-picker type="date" v-model="userInfo.nowContractExpiration" disabled="true" />
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">社保基数</text>
          <view class="right">
            <input type="number" v-model="userInfo.socialSecurityBase" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <text class="text-right">合同公司</text>
          <view class="right">
            <input v-model="userInfo.contractCompany" placeholder="请输入" disabled="true" />
            <text class="iconfont">&#xe667;</text>
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>绑定会员</text>
          </view>
          <!-- <view class="right">
            <input
              disabled
              type="number"
              v-model="userInfo.memName"
              placeholder="请输入"
            />
            <text class="iconfont">&#xe667;</text>
          </view> -->
          <view style="width: 70%">
            <next-data-select @change="membershipchange" :filterable="true" :options="membershipList"
              v-model="userInfo.membershipsId" :disabled="formIndex === 1" />
            <!-- <uni-data-select
              v-model="userInfo.membershipsId"
              :clear="true"
              :localdata="membershipList"
              disabled="true"
            ></uni-data-select> -->
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>是否总部</text>
          </view>
          <view style="width: 62%">
            <uni-data-select v-model="userInfo.isHeadquarters" :localdata="isTrueList" @change="isHeadquartersChange"
              disabled="true"></uni-data-select>
          </view>
        </view>
        <view class="info-item" v-if="formIndex != 1">
          <view class="text-right">
            <text class="required">*</text>
            <text>审批人</text>
          </view>
          <view style="width: 70%">
            <uni-data-select v-model="userInfo.deptleader" :clear="true" :localdata="ReviewerList"
              :disabled="formIndex === 1"></uni-data-select>
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>身份证证件</text>
          </view>
          <view style="width: 70%">
            <uni-file-picker v-model="idCardList" @select="(e) => handleSelect(e, 'idCard')"
              @delete="(e) => handleDelete(e, 'idCard')" :del-icon="formIndex === 1 ? false : true"
              :disabled="formIndex === 1"></uni-file-picker>
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>身份证到期时间</text>
          </view>
          <view style="width: 62%">
            <uni-datetime-picker type="date" v-model="userInfo.idCardEndTime" :disabled="formIndex === 1" />
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>健康证证件</text>
          </view>
          <view style="width: 70%">
            <uni-file-picker v-model="healthPicList" @select="(e) => handleSelect(e, 'healthPic')"
              @delete="(e) => handleDelete(e, 'healthPic')" :del-icon="formIndex === 1 ? false : true"
              :disabled="formIndex === 1"></uni-file-picker>
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>健康证到期时间</text>
          </view>
          <view style="width: 62%">
            <uni-datetime-picker type="date" v-model="userInfo.healthCertificateDate" :disabled="formIndex === 1" />
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>药师证有效期</text>
          </view>
          <view style="width: 62%">
            <uni-datetime-picker type="date" v-model="userInfo.pharmacistEndTime" :disabled="formIndex === 1" />
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>毕业证证件</text>
          </view>
          <view style="width: 70%">
            <uni-file-picker v-model="diplomaPicList" @select="(e) => handleSelect(e, 'diplomaPic')"
              @delete="(e) => handleDelete(e, 'diplomaPic')" :del-icon="formIndex === 1 ? false : true"
              :disabled="formIndex === 1"></uni-file-picker>
          </view>
        </view>
        <view class="info-item">
          <view class="text-right">
            <text class="required">*</text>
            <text>简历</text>
          </view>
          <view style="width: 70%">
            <uni-file-picker v-model="resumePicList" @select="(e) => handleSelect(e, 'resumePic')"
              @delete="(e) => handleDelete(e, 'resumePic')" :del-icon="formIndex === 1 ? false : true"
              :disabled="formIndex === 1"></uni-file-picker>
          </view>
        </view>
      </view>

      <!-- 合同部分 -->
      <view class="contract_container" v-if="!contractInfo">
        <view class="container_info" @click="getContractInfo(1)" v-if="userInfo.isTest == 0">
          <img class="info_image"
            src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241016/925ccd876019453f991f71ff6a3367f7.png" alt="" />
          <span class="info_title">试用期合同:</span>
          <span class="info_name">试用期合同.docx</span>
        </view>
        <view class="container_info" @click="getContractInfo(3)">
          <img class="info_image"
            src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241016/770aff2fcc8d4caabe942ed63496ae33.png" alt="" />
          <span class="info_title">保密协议:</span>
          <span class="info_name">保密协议.docx</span>
        </view>
        <view class="container_info" v-if="userInfo.isTest !== 0" @click="getContractInfo(2)">
          <img class="info_image"
            src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241016/4e30b021272f43d4bb03e722d081a155.png" alt="" />
          <span class="info_title">劳动合同:</span>
          <span class="info_name">劳动合同.docx</span>
        </view>
      </view>

      <button class="save-btn" @click="saveAudit" :disabled="isdisabled == 5" v-if="formIndex !== 1">
        提交审核
      </button>
    </view>
  </view>
</template>

<script>
const req = require("../../utils/request");
export default {
  data() {
    return {
      isBecome: 0,
      contractInfo: false,
      content: "",
      uid: req.getStorage("uid"),
      isHavaSign: false,
      showCanvas: false,
      isDrawing: false,
      lastX: 0,
      lastY: 0,
      canvas: null,
      ctx: {},

      userInfo: {},
      sexList: [
        { value: 0, text: "女" },
        { value: 1, text: "男" },
      ],
      isTrueList: [
        { value: 0, text: "否" },
        { value: 1, text: "是" },
      ],
      isTestList: [
        { value: 0, text: "是" },
        { value: 1, text: "否" },
      ],
      insuranceTypeList: [
        { value: "", text: "无" },
        { value: "广州", text: "广州" },
        { value: "深圳", text: "深圳" },
      ],
      positionStatusList: [
        { value: "0", text: "试岗" },
        { value: "1", text: "实习期" },
        { value: "2", text: "试用期" },
        { value: "3", text: "入职" },
        { value: "4", text: "离职" },
        { value: "5", text: "兼职" },
        { value: "6", text: "挂证" },
      ],
      contractTypeList: [
        { text: "劳动合同", value: 1 },
        { text: "实习协议", value: 2 },
        { text: "兼职协议(劳务合同)", value: 3 },
      ],
      ReviewerList: [],
      membershipList: [],
      idCardList: [],
      healthPicList: [],
      diplomaPicList: [],
      resumePicList: [],
      insIdsList: [],
      divNameList: [],
      postList: [],
      MerList: [],
      isdisabled: 0,
      formIndex: 0,
      staffId: "",
    };
  },

  onLoad(options) {
    this.formIndex = options.formIndex ? 1 : 0;
    this.staffId = options.staffId ? options.staffId : 0;
    if (this.staffId) {
      this.getData();
    } else {
      this.init();
    }
  },
  methods: {
    getData() {
      uni.showLoading({ title: "加载中..." });
      req.getRequest(
        `/shopApi/translate/system/staff/${this.staffId}`,
        {},
        (res) => {
          this.userInfo = res.data.length > 0 ? res.data[0] : {};
          // 转换身份证证件图片格式
          this.idCardList = this.userInfo.idCardPic
            ? this.userInfo.idCardPic.split(",").map((url) => ({
              url: url,
            }))
            : [];

          // 转换健康证图片格式
          this.healthPicList = this.userInfo.healthPic
            ? this.userInfo.healthPic.split(",").map((url) => ({
              url: url,
            }))
            : [];

          // 转换毕业证图片格式
          this.diplomaPicList = this.userInfo.diplomaPic
            ? this.userInfo.diplomaPic.split(",").map((url) => ({
              url: url,
            }))
            : [];

          // 转换简历图片格式
          this.resumePicList = this.userInfo.notesPicture
            ? this.userInfo.notesPicture.split(",").map((url) => ({
              url: url,
            }))
            : [];
          uni.hideLoading();
          this.getMember(); // 绑定会员列表
          this.getIsTest(); //获取合同
          this.getInsName(); //获取机构
          this.getDivName(); //获取部门
          this.getPostList(); //获取岗位
          this.getMerList(); //获取门店
        }
      );
    },
    init() {
      // 资料数据
      let id = req.getStorage("userInfo").introductionId;
      req.getRequest(`/shopApi/jobapply/staff/${id}`, {}, (res) => {
        console.log("资料详情", res);
        this.userInfo = res.data || {};
        this.isdisabled = res.data.staffState;
        // 转换身份证证件图片格式
        this.idCardList = (res.data.idCardPics || []).map((url) => ({
          url: url,
        }));

        // 转换健康证图片格式
        this.healthPicList = (res.data.healthPics || []).map((url) => ({
          url: url,
        }));

        // 转换毕业证图片格式
        this.diplomaPicList = (res.data.diplomaPics || []).map((url) => ({
          url: url,
        }));

        // 转换简历图片格式
        this.resumePicList = (res.data.notesPictureList || []).map((url) => ({
          url: url,
        }));

        this.getMember(); // 绑定会员列表
        this.getIsTest(); //获取合同
        this.getInsName(); //获取机构
        this.getDivName(); //获取部门
        this.getPostList(); //获取岗位
        this.getMerList(); //获取门店
      });
    },
    // 绑定会员列表
    getMember() {
      let search = "";
      req.getRequest(
        `/shopApi/jobapply/getAllUser?search=${search}`,
        {},
        (res) => {
          this.membershipList = (res.data.data || []).map((item) => {
            return {
              value: item.id, // 用 id 作为 value
              text: item.memName, // 用 memName 作为显示文本
            };
          });
        }
      );
      this.getReviewer(); //获取审批人
    },
    // 绑定会员切换
    membershipchange() {
      console.log("切换", this.userInfo.membershipsId);
    },
    //获取审批人
    getReviewer() {
      let _ts = this;
      let param = {
        uid: req.getStorage("userInfo").introductionId, // 申请人id
        node: "addleave", // 审批节点
        isHeadquarters: _ts.userInfo.isHeadquarters, // 是否总部
      };
      req.getRequest("/shopApi/translate/getReviewer", param, (res) => {
        if (res.data.length != 0) {
          this.ReviewerList = res.data.map((item) => ({
            value: item.name,
            text: item.name,
          }));
        }
      });
    },

    // 获取机构
    getInsName() {
      req.getRequest("/shopApi/jobapply/getInsListByStaff", {}, (res) => {
        this.insIdsList =
          res.data.data.map((item) => ({
            value: item.id,
            text: item.insName,
          })) || [];
      });
    },

    // 获取部门
    getDivName() {
      let param = {
        staffId: req.getStorage("userInfo").introductionId,
      };
      req.getRequest(
        `/shopApi/jobapply/getDicsionListsByshop`,
        param,
        (res) => {
          this.divNameList = res.data.map((item) => ({
            value: item.id,
            text: item.divName,
          }));
        }
      );
    },
    // 获取岗位
    getPostList() {
      let param = {
        divParentId: this.userInfo.divId,
      };
      req.getRequest(
        `/shopApi/jobapply/getPostListByDepIdByshop`,
        param,
        (res) => {
          this.postList =
            res.data.data.map((item) => ({
              value: item.id,
              text: item.postName,
            })) || [];
        }
      );
    },

    // 获取门店
    getMerList() {
      req.getRequest(
        `/shopApi/jobapply/getStoreListAllByInsIdByshop`,
        {},
        (res) => {
          this.MerList =
            res.data.map((item) => ({
              value: item.id,
              text: item.storeName,
            })) || [];
        }
      );
    },

    // 是否总部切换
    isHeadquartersChange(e) {
      this.getReviewer();
    },
    // 统一选择处理方法
    async handleSelect(e, type) {
      uni.showLoading({ title: "上传中..." });

      try {
        // 获取对应类型的列表引用
        const targetList = this[`${type}List`];

        // 创建上传任务数组
        const uploadTasks = e.tempFilePaths.map((path) =>
          this.uploadSingleFile(path).then((url) => {
            targetList.push({ url });
          })
        );

        // 等待所有上传完成
        await Promise.all(uploadTasks);

        // 自动排序最新上传
        this[`${type}List`] = [...targetList];
      } catch (error) {
        this.handleUploadError(error, type);
      } finally {
        uni.hideLoading();
      }
    },

    // 统一删除处理方法
    handleDelete(e, type) {
      if (this.formIndex === 1) {
        return;
      }
      const targetList = this[`${type}List`];
      this[`${type}List`] = targetList.filter((_, i) => i !== e.index);
    },

    // 封装单个文件上传
    uploadSingleFile(filePath) {
      return new Promise((resolve, reject) => {
        req.uploadFile(
          "/shopApi/home/<USER>",
          filePath,
          (data) => resolve(data),
          (err) => reject(err)
        );
      });
    },

    // 统一错误处理
    handleUploadError(error, type) {
      const typeName = {
        idCard: "身份证",
        healthPic: "健康证",
        diplomaPic: "毕业证",
        resumePic: "简历",
      }[type];

      uni.showToast({
        title: `${typeName}上传失败}`,
        icon: "none",
      });
    },

    // 性别选择
    // bindPickerChange(e) {
    //   const index = e.detail.value; // 获取选中下标
    //   this.userInfo.sex = this.genderArray[index]; // 根据下标更新性别值
    // },
    // 提交审核
    saveAudit() {
      // 批量处理图片字段映射
      const picFields = {
        idCardList: "idCardPics",
        healthPicList: "healthPics",
        diplomaPicList: "diplomaPics",
        resumePicList: "notesPicture",
      };

      // 统一处理 URL 映射
      // Object.entries(picFields).forEach(([listKey, userInfoKey]) => {
      //   this.userInfo[userInfoKey] = this[listKey].map(({ url }) => url);
      // });

      // 统一处理 URL 为逗号分隔的字符串
      Object.entries(picFields).forEach(([listKey, userInfoKey]) => {
        this.userInfo[userInfoKey] = this[listKey]
          .map(({ url }) => url)
          .join(",");
      });
      this.userInfo.pharmacistPics = "";

      if (!this.userInfo.name) {
        req.msg("请输入姓名");
        return;
      }
      if (!this.userInfo.phone) {
        req.msg("请选择手机号");
        return;
      }
      if (!this.userInfo.account) {
        req.msg("请输入账号");
        return;
      }
      if (!this.userInfo.divId) {
        req.msg("请选择部门");
        return;
      }
      // if (!this.userInfo.insuranceType) {
      //   req.msg("请选择购买社保地");
      //   return;
      // }
      // if (!this.userInfo.money) {
      //   req.msg("请输入底薪金额");
      //   return;
      // }
      if (!this.userInfo.isTest && this.userInfo.isTest != 0) {
        req.msg("请选择是否试用期");
        return;
      }
      if (!this.userInfo.insIds) {
        req.msg("请选择机构名称");
        return;
      }
      if (!this.userInfo.isHeadquarters && this.userInfo.isHeadquarters != 0) {
        req.msg("请选择是否总部");
        return;
      }
      if (!this.userInfo.deptleader) {
        req.msg("请选择审批人");
        return;
      }
      this.userInfo.staffState = 5;
      let param = this.userInfo;
      req.putRequestJson("/shopApi/jobapply/staffUpdate", param, (res) => {
        if (res.code == 200) {
          req.msg("提交成功");
          this.init();
        }
      });
    },

    // 合同部分start
    getIsTest() {
      req.getRequest(
        "/mall-system/staff/isTest",
        {
          name: req.getStorage("userInfo").staffName,
        },
        (res) => {
          this.isBecome = res.data.isTest;
          if (res.data.isHavaSign) {
            this.isHavaSign = false;
          }
        }
      );
    },
    // 获取合同详细内容
    getContractInfo(type) {
      const userInfo = req.getStorage("userInfo");
      const obj = {
        url: `https://staffnew.gzjihetang.com/pages/contractDetail/contractDetail`,
        type: type,
        name: userInfo.staffName,
      };
      uni.navigateTo({
        url: `/staff/contractDetail/contractDetail?obj=${JSON.stringify(obj)}`,
      });
    },
    // 合同部分end
  },
};
</script>

<style lang="scss" scoped>
.user-info {
  background: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    background: #fff;
    margin-bottom: 2rpx;
    border-bottom: 0.2px solid #ebeef5;

    .required {
      color: #f23030;
      margin-right: 5px;
    }

    .right {
      display: flex;
      align-items: center;
      color: #999;

      image {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
        margin-right: 10rpx;
      }

      input {
        text-align: right;
        margin-right: 10rpx;
      }

      .iconfont {
        font-size: 24rpx;
        margin-left: 10rpx;
      }
    }
  }
}

.fileMaintain {
  // min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 80rpx;
  // padding: 16px;
}

.contract_container {
  margin-top: 20rpx;
  padding: 20px;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.container_info {
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 6px;
  background: #fff;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  border: 1px solid #ebeef5;
}

.container_info:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #e6e6e6;
}

.container_info:active {
  transform: translateY(0);
}

.container_info:last-child {
  margin-bottom: 0;
}

.info_image {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.info_title {
  margin-left: 12px;
  font-weight: 500;
  color: #303133;
  font-size: 15px;
}

.info_name {
  margin-left: 8px;
  color: #606266;
  font-size: 14px;
  border-bottom: 1px dashed #909399;
  padding-bottom: 2px;
  transition: all 0.3s ease;
}

.info_name:hover {
  color: #409eff;
  border-bottom-color: #409eff;
}

.header {
  padding: 12px 16px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  background: rgba(248, 248, 248, 0.95);
  backdrop-filter: blur(10px);
  z-index: 999;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.page_head__title {
  display: inline-block;
  width: 80vw;
  font-weight: 600;
  line-height: 32px;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #303133;
  font-size: 16px;
}

.save-btn {
  // position: fixed;
  // bottom: 40rpx;
  // left: 20rpx;
  // right: 20rpx;
  margin-top: 20rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #ea1306;
  color: #fff;
  border-radius: 40rpx;
  font-size: 32rpx;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  width: 560rpx;
  margin: 80rpx auto;

  &:active {
    opacity: 0.8;
  }
}

.text-right {
  width: 26%;
  text-align: right;
  font-size: 28rpx;
}

/deep/ .uni-select__input-box {
  width: 100%;
}

/deep/ .input-placeholder {
  color: #999999;
  font-size: 28rpx;
}

/deep/ .uni-select__input-placeholder {
  color: #999999 !important;
  font-size: 28rpx !important;
}
</style>