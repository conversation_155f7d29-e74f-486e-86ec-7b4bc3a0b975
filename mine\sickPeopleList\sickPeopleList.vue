<template>
    <view style="padding-bottom:200rpx;">
        <view class="content">
            <view @click="upIsDefault(item)" v-for="(item, index) in patientList" :key="index" class="title">
                <view class="df">
                    <img class="shu"
                        src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240829/75327e7fe6e7454b84903e036ae475a1.png"
                        alt="">
                    <text class="Copay">患者信息</text>
                </view>
                <view class="sickPeople">
                    <view>
                        <view style="display: flex;margin-bottom:20rpx;">
                            <view>
                                {{ item.name }}
                            </view>
                            <view v-if="item.isDefault == 1" style="color:#0b78ff;margin-left:20rpx;">默认</view>
                        </view>
                        <text style="font-size:12px;color:#999999;">
                            {{ item.sex == 1 ? '男' : '女' }}-{{ item.age }}岁-{{ item.tel }}
                        </text>
                    </view>
                    <view @click.stop="modifications(item)"
                        style="width:100rpx;height:100%;display: flex;align-items: center;justify-content: flex-end;">
                        <!-- <image src="/static/pages/images/rico.png" mode="widthFix" style="width:25rpx;"></image> -->
                        <uni-icons type="right" size="18"></uni-icons>
                    </view>
                </view>
            </view>
        </view>
        <view class="submit" @click="addSubmit">添加就诊人</view>
    </view>
</template>

<script>
const req = require("../../utils/request.js");
export default {
    data() {
        return {
            patientList: [],
            choice: null,
        }
    },
    onLoad(options) {
        if (options.choice) this.choice = options.choice
    },
    onShow() {
        this.getPatientList()
    },
    methods: {
        upIsDefault(item) {
            if (this.choice) {
                let pages = getCurrentPages();
                var prevPage = pages[pages.length - 2];
                prevPage.$vm.patient = item
                prevPage.$vm.choice = true
                uni.navigateBack();
            } // } 
        },
        getPatientList() {
            req.showLoading()
            req.getRequest("/shopApi/userDrugPeople/list", {
                // uid: 2,
                pageNum: 1,
                pageSize: 99
            }, res => {
                console.log(res, '用药人列表');
                uni.hideLoading();
                this.patientList = res.data.list
            })
        },
        modifications(item) {
            uni.navigateTo({
                url: '/mine/sickPeopleDet/sickPeopleDet?id=' + item.id,
            });
        },
        addSubmit() {
            uni.navigateTo({
                url: '/mine/sickPeopleDet/sickPeopleDet',
            });

        }
    }
}
</script>

<style>
@import "./sickPeopleList.css";
</style>