.inventory-check {
    min-height: 100vh;
    background-color: #f5f5f5;
.section {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 20rpx;
    
    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      
      .section-title {
        font-size: 30rpx;
        font-weight: bold;
        position: relative;
        padding-left: 20rpx;
        
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 8rpx;
          height: 30rpx;
          background-color: #ff3333;
          border-radius: 4rpx;
        }
      }
      
      .edit-btn {
        margin-left: auto;
        background-color: #2b85e4;
        color: #fff;
        font-size: 24rpx;
        padding: 6rpx 20rpx;
        border-radius: 30rpx;
      }
    }
    
    .product-info {
      display: flex;
      flex-direction: column;
      
      .info-row {
        display: flex;
        margin-bottom: 15rpx;
        font-size: 28rpx;
        
        .label {
          width: 180rpx;
          color: #666;
        }
        
        .value {
          flex: 1;
          color: #333;
        }
      }
    }
    
    .batch-info {
      .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15rpx;
        
        .label {
          color: #666;
          font-size: 28rpx;
        }
        
        .value {
          color: #333;
          font-size: 28rpx;
          
          &.highlight {
            color: #ff3333;
            font-weight: bold;
          }
        }
      }
    }
  }
}