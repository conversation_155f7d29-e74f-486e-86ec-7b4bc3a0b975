.index_view {
  padding-bottom: 50rpx;
  background: #f8f8f8
    url("https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241121/78a2a0eddbf84af987f08200c51b7045.png");
  background-repeat: no-repeat;
  background-size: 750rpx 494rpx;
}
.store {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 25rpx;
  padding: 0 20rpx;
  color: #fff;
  .store_name {
    display: flex;
    align-items: center;
  }
}
.search {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  border-radius: 40rpx;
  width: 100%;
  height: 70rpx;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  font-size: 25rpx;
  padding: 0 20rpx;
  color: #bfbfbf;
}
.histories {
  padding: 0 40rpx;
  margin-top: 20rpx;
  .histories_item {
    background: #f2f3f5;
    padding: 10rpx 20rpx;
    border-radius: 40rpx;
    z-index: 1;
    display: inline-block;
    font-size: 25rpx;
    color: #666;
    margin-right: 10rpx;
    margin-bottom: 10rpx;
  }
  .iconfont {
    font-size: 32rpx;
    color: #ea1306;
    margin-right: 10rpx;
  }
}
.categorize {
  display: flex;
  flex-wrap: wrap;
  .categorize_item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 20%;
    margin-bottom: 30rpx;
  }
}
.swiper-box {
  margin: 0 20rpx;
  border-radius: 20rpx;
  height: 200rpx;
  margin-top: 20rpx;
}
.swiper-img {
  border-radius: 20rpx;
  width: 100%;
  height: 200rpx;
}
.time_limited {
  margin: 20rpx;
  background: linear-gradient(180deg, #fdf6f6 0%, #ffffff 72%);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.02);
  padding: 10rpx;

  .time_limited_list {
    display: flex;
    justify-content: space-between;
    font-size: 26rpx;
    margin-top: 15rpx;
  }

  .scroll-view {
    width: 100%;
    white-space: nowrap;
    padding-bottom: 20rpx;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .scroll-content {
      display: inline-flex;
      padding-right: 20rpx;
    }
  }
}

.goods_item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
  border-radius: 15rpx;
  padding: 0 10rpx;

  &.list-item {
    width: 260rpx;
    font-size: 20rpx;
  }

  &.scroll-item {
    width: 220rpx;
    font-size: 24rpx;
    display: inline-flex;
    flex-shrink: 0;
  }

  .assembly_button {
    position: absolute;
    top: 0;
    left: 0;
    width: 56rpx;
    height: 36rpx;
    border-radius: 16rpx 0 16rpx 0;
    background: #ea1306;
    font-size: 20rpx;
    color: #fff;
    text-align: center;
    line-height: 32rpx;
  }

  .goods_title {
    width: 220rpx;
    margin-top: 8rpx;
    margin-bottom: 4rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #3d3d3d;
    font-size: 24rpx;
    font-weight: 600;
  }

  .goods_product {
    font-size: 20rpx;
    margin: 0;
    color: #4e5969;
  }

  .goods_price {
    display: flex;
    color: #ea1306;
    align-items: center;

    .original {
      font-size: 24rpx;
      color: #999;
      text-decoration: line-through;
    }
  }

  .goods_btn {
    display: flex;
    align-items: center;
    background: #f9e4e3;
    border-radius: 15rpx 30rpx 30rpx 15rpx;
    color: #ea1306;
    height: 55rpx;
    padding-left: 10rpx;
    font-size: 20rpx;
  }

  .goods_events {
    position: absolute;
    top: 0;
    right: 0;
    color: #ea1306;
    align-items: center;
    border: 1px solid #ea1306;
    border-radius: 4px;
    padding: 2px;
    background-color: #f9c8c8;
  }
}
.line {
  width: 6rpx;
  height: 26rpx;
  border-radius: 4rpx;
  background: #ea1306;
  margin-right: 8rpx;
}
.title {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}
.classify {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx;
  justify-content: space-between;
  .classify_item {
    width: 49%;
    min-height: 100rpx;
    font-size: 28rpx;
    background: #fff;
    border-radius: 15rpx;
    padding: 15rpx;
    box-sizing: border-box;
    &:nth-child(-n + 2) {
      margin-bottom: 13rpx;
    }
  }
}
.recommend {
  margin: 20rpx;

  .goods_title {
    display: flow-root;
    font-size: 24rpx;

    view {
      float: left;
      margin-right: 10rpx;
    }
  }
}
.login-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-container {
  width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
}

.login-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f5f5f5;
}

.login-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.login-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.login-content {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-avatar {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.login-tip {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.login-btn {
  width: 320rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: #0b78ff;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-btn button {
  font-size: 30rpx !important;
  color: #fff !important;
  background: transparent;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
