<template>
    <view class="seckill-page">
        <!-- 倒计时区域 -->
        <view class="countdown-section">
            <view class="time-block">
                <text class="time-label">本场结束还剩</text>
                <view class="time-numbers">
                    <block v-if="days > 0">
                        <text class="time-unit">{{ days }}</text>
                        <text class="time-label">天</text>
                    </block>
                    <text class="time-unit">{{ hours }}</text>
                    <text class="colon">:</text>
                    <text class="time-unit">{{ minutes }}</text>
                    <text class="colon">:</text>
                    <text class="time-unit">{{ seconds }}</text>
                </view>
            </view>
            <!-- <view class="session-tabs">
                <scroll-view scroll-x class="scroll-view" :show-scrollbar="false">
                    <view class="session-wrapper">
                        <view class="session-item" :class="{ active: currentSession === index }"
                            v-for="(item, index) in sessions" :key="index" @click="switchSession(index)">
                            <text class="time">{{ item.time }}</text>
                            <text class="status">{{ item.status }}</text>
                        </view>
                    </view>
                </scroll-view>
            </view> -->
        </view>
        <!-- <view class="category-title">
            <scroll-view class="scroll-view" scroll-x="true" show-scrollbar="false">
                <view class="category-list">
                    <view class="category-item" v-for="(item, index) in categories" :key="index"
                        :class="{ active: currentCategory === index }" @tap="switchCategory(index)">
                        {{ item.name }}
                    </view>
                </view>
            </scroll-view>
        </view> -->
        <!-- 商品列表 -->
        <view class="product-list">
            <view class="product-item" v-for="(item, index) in productList" :key="index" @click="goToDetail(item)">
                <view class="product-card">
                    <view class="image-wrapper">
                        <view class="yibao-tongchou" v-if="item.yibao === 1">医保</view>
                        <view class="yibao-tongchou" v-if="item.tongchou === 1">统筹</view>
                        <image class="product-image" :src="item.img" mode="aspectFill"></image>
                        <view class="date-wrapper">
                            <view style="font-size:35rpx;font-weight: 600;">{{ currentDate }}</view>
                            <image mode="widthFix" style="width:130rpx;"
                                src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241119/5cd2785fdfed412d9d0af37502fe7bbe.png">
                            </image>
                        </view>
                    </view>
                    <view class="product-info">
                        <text class="title">{{ item.productName }}</text>
                        <view class="stock-info">
                            <view class="stock-item">
                                <text class="stock">门店库存: {{ item.sellStock }}件</text>
                                <text class="stock">云仓库存: {{ item.warehouseStock }}件</text>
                            </view>
                            <text class="limit">限购{{ item.maxBuy }}件</text>
                        </view>
                        <view class="price-row">
                            <view class="price">
                                <text class="symbol">¥</text>
                                <text class="number">{{ item.money }}</text>
                                <text class="original">¥{{ item.orPrice }}</text>
                            </view>
                            <view class="buy-btn" :class="{ disabled: item.soldOut }" @click.stop="openBuyPopup(item)">
                                {{ item.soldOut ? '已抢完' : '立即抢购' }}
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 无数据提示 -->
        <view class="empty-tip" v-if="productList.length === 0">
            <!-- <image src="/static/images/empty.png" mode="aspectFit"></image> -->
            <text>暂无抢购商品</text>
        </view>

        <!-- 添加购买弹窗组件 -->
        <buy-popup v-if="selectedProduct != null" ref="buyPopup"
            :product="{ image: selectedProduct.img, price: selectedProduct.money, maxBuy: selectedProduct.maxBuy }"
            :cloud-stock="selectedProduct.warehouseStock" :store-stock="Number(selectedProduct.sellStock) || 0"
            @confirm="confirmBuy" />
    </view>
</template>

<script>
import buyPopup from '@/components/buy-popup/buy-popup.vue'
const req = require('../../utils/request')

export default {
    components: {
        buyPopup
    },
    data() {
        return {
            currentDate: '',
            days: '00',
            hours: '00',
            minutes: '00',
            seconds: '00',
            timer: null,
            currentSession: 0,
            // sessions: [
            //     { time: '08:00', status: '已开抢' },
            //     { time: '10:00', status: '已开抢' },
            //     { time: '12:00', status: '抢购中' },
            //     { time: '14:00', status: '即将开始' },
            //     { time: '16:00', status: '即将开始' },
            //     { time: '18:00', status: '即将开始' },
            //     { time: '20:00', status: '即将开始' },
            //     { time: '22:00', status: '即将开始' }
            // ],
            productList: [],
            selectedProduct: null, // 添加选中的商品数据
            categories: [
                { name: '推荐' },
                { name: '数码' },
                { name: '服装' },
                { name: '美妆' },
                { name: '食品' },
                { name: '家居' },
                { name: '运动' }
            ],
            currentCategory: 0
        }
    },
    onLoad() {
        this.getCurrentDate()
        this.getProductList()
    },
    onPullDownRefresh() {
        this.getProductList()
    },
    onUnload() {
        if (this.timer) {
            clearInterval(this.timer)
        }
    },
    methods: {
        getSeckillList() {

        },
        // 开始倒计时
        startCountdown(endTime) {
            this.timer = req.startCountdown(
                endTime,
                // 更新时间回调
                (timeData) => {
                    this.days = timeData.days
                    this.hours = timeData.hours
                    this.minutes = timeData.minutes
                    this.seconds = timeData.seconds
                }, () => {
                    this.getProductList()
                }
            )
        },

        // 切换场次
        switchSession(index) {
            this.currentSession = index
            // 获取scroll-view组件
            const query = uni.createSelectorQuery().in(this)
            query.select('.scroll-view').boundingClientRect(data => {
                // 计算需要滚动的距离
                const itemWidth = 180 // 每个item的宽度（包含margin）
                const scrollLeft = index * itemWidth - (data.width - itemWidth) / 2

                // 执行滚动
                uni.pageScrollTo({
                    scrollLeft: Math.max(0, scrollLeft),
                    duration: 300
                })
            }).exec()

            this.getProductList()
        },

        // 获取商品列表
        getProductList() {
            req.getRequest("/shopApi/shopApi/activity/list", {
                pageNum: 1,
                pageSize: 99,
                merchantId: req.getStorage('currentStore').id,
                type: 2,
            }, res => {
                this.productList = res.data.items
                if (this.productList.length > 0 && this.productList[0].endTime) {
                    this.startCountdown(this.productList[0].endTime)
                }
            })
        },

        // 跳转商品详情
        goToDetail(item) {
            if (item.soldOut) {
                uni.showToast({
                    title: '商品已抢完',
                    icon: 'none'
                })
                return
            }
            uni.navigateTo({
                url: `/product/detail/detail?id=${item.productId}&type=2`
            })
        },

        // 打开购买弹窗
        openBuyPopup(item) {
            if (item.soldOut) {
                uni.showToast({
                    title: '商品已抢完',
                    icon: 'none'
                })
                return
            }
            this.selectedProduct = item
            this.$nextTick(() => {
                this.$refs.buyPopup.open()
            })
        },

        // 确认购买
        confirmBuy(data) {
            console.log('购买数据：', data)
            // TODO: 调用抢购接口
            uni.showLoading({
                title: '正在抢购...'
            })

            // 模拟抢购请求
            setTimeout(() => {
                uni.hideLoading()
                uni.showToast({
                    title: '抢购成功',
                    icon: 'success'
                })
                this.$refs.buyPopup.close()
                this.getProductList() // 刷新商品列表
            }, 1000)
        },

        switchCategory(index) {
            this.currentCategory = index
            // 这里可以添加切换分类后的数据加载逻辑
        },

        getCurrentDate() {
            const date = new Date()
            const month = date.getMonth() + 1 // 月份从0开始,需要+1
            const day = date.getDate()
            this.currentDate = `${month}.${day}`
        }
    }
}
</script>

<style lang="scss" scoped>
@import './seckill.scss';
</style>