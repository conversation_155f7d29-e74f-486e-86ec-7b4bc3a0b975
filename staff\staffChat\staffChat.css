.nav {
	display: flex;
	align-items: center;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 999;
	box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.chat-container {
	position: absolute;
	display: flex;
	flex-direction: column;
	/* height: calc(100vh - 150rpx); */
	/* 调整为合适的高度 */
	padding: 0 20px;
	box-sizing: border-box;
	overflow-y: scroll;
}

.inquiryBtn {
	/* border: 1px solid #000; */
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	color: #333;
	font-weight: 500;
	font-size: 28rpx;
}

.message {
	display: flex;
	margin-bottom: 10px;
	animation-duration: 0.3s;
	animation-fill-mode: both;
}

.toFinish {
	padding: 8rpx 24rpx;
	border: none;
	border-radius: 20px;
	position: absolute;
	right: 20rpx;
	color: #ffffff;
	background-color: #4A90E2;
	font-size: 26rpx;
	transition: all 0.3s ease;
	z-index: 2;
}

.toFinish:active {
	transform: scale(0.95);
	background-color: #357ABD;
}

.user-message {
	justify-content: flex-end;
	align-items: center;
	animation-name: slideInRight;
}

.server-message {
	justify-content: flex-start;
	align-items: center;
	animation-name: slideInLeft;
}

.message-text {
	max-width: 210px;
	padding: 12px 16px;
	border-radius: 18px;
	box-shadow: 0 2px 6px rgba(0,0,0,0.1);
	word-break: break-all;
	transition: all 0.3s ease;
}

/* 当消息内容是图片时的样式 */
.message-text image {
	background-color: #fff;
	border-radius: 8px;
	padding: 8px;
	max-width: 100%;
	box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.user-message .message-text {
	background-color: #4A90E2;
	margin-right: 30rpx;
	border-bottom-right-radius: 4px;
	color: #fff;
}

/* 发送的图片消息使用白色背景 */
.user-message .message-text:has(image) {
	background-color: #fff;
	padding: 8px;
}

.server-message .message-text {
	background-color: #f5f7fa;
	margin-left: 30rpx;
	border-bottom-left-radius: 4px;
	color: #333;
}

.input-container {
	width: 100%;
	position: fixed;
	bottom: 0;
	box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
}

.input-field {
	flex-grow: 1;
	padding: 12rpx 24rpx;
	border: 1px solid #e5e5e5;
	border-radius: 24px;
	margin-right: 15px;
	background: #f8f8f8;
	transition: all 0.3s ease;
}

.input-field:focus {
	background: #fff;
	border-color: #4A90E2;
	box-shadow: 0 0 0 2px rgba(74,144,226,0.1);
}

.send-button {
	padding: 12rpx 30rpx;
	margin-right: 10rpx;
	border: none;
	border-radius: 20px;
	background: #4A90E2;
	color: #fff;
	font-weight: 500;
	transition: all 0.3s ease;
}

.send-button:active {
	transform: scale(0.95);
	background: #357ABD;
}

.title {
	display: flex;
}

.logoimg {
	width: 35px;
	height: 35px;
}

.logoimg image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.dealIcon {
	width: 12px;
	height: 14px;
	margin: 5px 5px 0 10px;
}

.dealIcon image {
	width: 100%;
	height: 100%;
}

.goodsList {
	background-color: #ccc;
	padding: 10px;
	border-radius: 5px;
	display: flex;
	margin: 10px 0;
}

.goods {
	width: 43px;
	height: 38px;
	background-color: #c9e2fd;
}

.goods image {
	width: 100%;
	height: 100%;
	border-radius: 2px;
}

.useConfirm {
	border-top: 0.5px solid #ccc;
	padding: 10px 0;
}

.useBtn {
	background-color: #ccc;
	padding: 5px;
	border-radius: 5px;
	float: right;
}

.container {
	position: relative;
}

.container image {
	width: 80rpx;
	height: 80rpx;
	overflow: hidden;
	border-radius: 50%;
	margin-right: 20rpx;
	border: 2px solid #fff;
	box-shadow: 0 2px 8px rgba(74,144,226,0.15);
	transition: transform 0.3s ease;
}

.container image:hover {
	transform: scale(1.05);
}