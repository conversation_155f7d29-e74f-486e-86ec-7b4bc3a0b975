.index_view {
  padding-bottom: 50rpx;
  background: #f8f8f8
    url("https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241121/78a2a0eddbf84af987f08200c51b7045.png");
  background-repeat: no-repeat;
  background-size: 750rpx 494rpx;
}
.line {
  width: 6rpx;
  height: 26rpx;
  border-radius: 4rpx;
  background: #ea1306;
  margin-right: 8rpx;
}
.title {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  margin-bottom: 30rpx;
}

.avatar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 30rpx 0 30rpx;
}
.vip-code {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
}
.open-vip {
  width: 90%;
  height: 230rpx;
  background: #ea7369;
  margin-top: 50rpx;
  margin: 30rpx auto 0;
  border-radius: 40rpx 40rpx 0 0;
  padding: 20rpx;
  box-sizing: border-box;
  position: relative;
  .vip-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24rpx;
    color: #fff;
    .vip-btn {
      padding: 10rpx 20rpx;
      border-radius: 40rpx;
      background: linear-gradient(180deg, #f79186 2%, #e74e40 98%);
      color: #fff;
    }
  }
  .vip-content {
    position: absolute;
    bottom: 20rpx;
    left: 0;
    width: 100%;
    padding: 0 20rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    .vip-item {
      display: flex;
      align-items: center;
      flex-direction: column;
      font-size: 22rpx;
      color: #fff;
    }
  }
}
.wallet {
  display: flex;
  align-items: center;
  background: #fff;
  margin: 20rpx 20rpx 0;
  justify-content: space-around;
  color: #333;
  border-radius: 20rpx;
  padding: 20rpx 0;
  font-size: 28rpx;
  .wallet_item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}
.order {
  background: #fff;
  margin: 20rpx;
  color: #333;
  border-radius: 20rpx;
  padding: 20rpx;
  font-size: 28rpx;
  .order_item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}
.service {
  background: #fff;
  margin: 20rpx;
  color: #333;
  border-radius: 20rpx;
  padding: 20rpx;
  font-size: 28rpx;
  .service_item {
    width: 25%;
    margin-bottom: 30rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

.goods_title {
  display: flow-root;
  margin: 10rpx 0;
  font-size: 26rpx;

  view {
    float: left;
    margin-right: 10rpx;
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

.bounce-image {
  animation: bounce 2s ease-in-out infinite;
}

.login-btn {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  font-size: 28rpx;
  padding: 15rpx 50rpx;
  border-radius: 40rpx;
  line-height: 1.5;
  height: auto;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &::after {
    border: none;
  }

  &:active {
    transform: scale(0.98);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  }
}

.avatar-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  background: #fff;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.8);

  image {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;

    &:active {
      transform: scale(1.05);
    }
  }
}
.identification {
  position: relative;
  width: 120rpx;
  height: 32rpx;
  border-radius: 4rpx;
  opacity: 1;
  background: linear-gradient(90deg, #e5e4e4 20%, #cbcaca 100%);
  margin-left: 28rpx;
  color: #333333;
  font-size: 20rpx;
  line-height: 32rpx;
  text-align: right;
  padding-right: 10rpx;
  box-sizing: border-box;
}
.contact-btn {
  background: none;
  padding: 0;
  margin: 0;
  line-height: 1;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #333;
  font-size: 24rpx;

  &::after {
    border: none;
  }
}
