<template>
  <view class="product-detail">
    <!-- 商品轮播图 -->
    <swiper class="swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000">
      <swiper-item v-for="(item, index) in bannerImages" :key="index">
        <image :src="item" mode="aspectFill" :class="{ 'mohu': isPrescriptionDrug }" :lazy-load="true" />
        <image v-if="isPrescriptionDrug" class="prescription-overlay" mode="widthFix"
          src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20240111/a338c782689a421b8e9ab0848c3c5c10.png" />
      </swiper-item>
    </swiper>

    <!-- 商品信息 -->
    <view class="info-section">
      <!-- 活动商品价格展示 -->
      <view v-if="isActivityProduct" class="group">
        <image src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241121/dc9e04c1711c48ae92c842a3ce185da7.png"
          class="ptbg" />
        <view class="groups dflex">
          <view class="left">
            <text class="price">¥{{ productInfo.money }}</text>
            <text class="origin">¥{{ productInfo.orPrice }}</text>
          </view>
          <view class="right">
            <text class="tag-text">{{ activityTagText }}</text>
            <view class="endtime dflex">
              <block v-if="countdownTime.days > 0">
                <text>{{ countdownTime.days }}</text>天
              </block>
              <text>{{ formatTime(countdownTime.hours) }}</text>:
              <text>{{ formatTime(countdownTime.minutes) }}</text>:
              <text>{{ formatTime(countdownTime.seconds) }}</text>
              <view>结束</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 新客特价商品 -->
      <view v-else-if="isNewCustomerProduct" class="price">
        <view class="seckill-price">
          <text class="number">¥{{ productInfo.money }}</text>
          <text class="original">¥{{ productInfo.orPrice }}</text>
        </view>
        <view class="seckill-tag">
          <text class="tag-text">新客特价</text>
        </view>
      </view>

      <!-- 普通商品价格展示 -->
      <view v-else class="price">
        <text class="symbol">¥</text>
        <text class="number">{{ currentPrice }}</text>
        <text v-if="hasOriginalPrice" class="original">
          ¥{{ productInfo.salePrice }}
        </text>

        <!-- 分享按钮 -->
        <view v-if="userInfo.id" class="share-button" @click="sharePoster">
          <text class="iconfont">&#xe626;</text>
          <text>分享</text>
        </view>

        <!-- 金卡价格展示 -->
        <view v-if="showGoldCardPrice" class="gold-card-price">
          <i v-if="!levels" class="iconfont gold-icon">&#xe6b0;</i>
          <image v-else src="@/static/jinka.png" mode="heightFix" class="gold-card-image" />
          <view v-if="!levels" class="gold-card-badge">金卡价</view>
          <view class="gold-card-amount" :class="{ 'with-image': levels }">
            ¥{{ productInfo.level2Price }}
          </view>
        </view>
      </view>

      <view class="title-row">
        <view class="title">{{ productInfo.goodsName }}</view>
      </view>
      <view class="divider"></view>
    </view>

    <!-- 将三个部分放在一个容器内 -->
    <view class="info-blocks" @click="showMedicationGuide">
      <view class="title">
        <view class="line"></view>
        <view>用药指导</view>
      </view>
      <view style="display: flex;">
        <!-- 主治功能 -->
        <view class="info-block">
          <view class="block-title">主治功能</view>
          <view class="block-content">
            {{ productInfo.fi ? productInfo.fi : '暂无信息' }}
          </view>
        </view>

        <!-- 用法用量 -->
        <view class="info-block">
          <view class="block-title">用法用量</view>
          <view class="block-content">
            {{ productInfo.usages ? productInfo.usages : '暂无信息' }}
          </view>
        </view>

        <!-- 禁忌 -->
        <view class="info-block">
          <view class="block-title">禁忌</view>
          <view class="block-content">
            {{ productInfo.taboo ? productInfo.taboo : '暂无信息' }}
          </view>
        </view>
      </view>

    </view>
    <!-- 门店 -->
    <view class="store-info">
      <view class="store-content" @click="goToStoreList">
        <view class="store-left">
          <image class="store-logo"
            src="https://jht-image.oss-cn-shenzhen.aliyuncs.com/20241119/001451741aa0494f98a3c69511a397a3.png"
            mode="widthFix" style="width:100rpx;margin-right:20rpx;">
          </image>
          <view>
            <view class="store-name">{{ store.storeName }}</view>
            <view class="store-address">{{ store.address }}</view>
          </view>
        </view>
        <view @click.stop="goMap" class="store-right">
          <text class="iconfont">&#xe65e;</text>
          <view>导航</view>
        </view>
      </view>
    </view>
    <!-- 用户评价部分 - 放在储存方式后面 -->
    <view v-if="false" class="review-section">
      <view class="section-title">
        用户评价
        <text class="review-count">({{ reviewInfo.total }})</text>
        <!-- <text class="good-rate">好评率 {{ reviewInfo.goodRate }}%</text> -->
      </view>

      <!-- 评分概览 -->
      <view class="rating-overview">
        <view class="score">
          <text class="number">{{ reviewInfo.avgScore }}</text>
          <text class="total">/5分</text>
        </view>
        <view class="stars">
          <text v-for="n in 5" :key="n" class="iconfont" :class="{ 'active': n <= reviewInfo.avgScore }">{{ n <=
            reviewInfo.avgScore ? '&#xe9a1;' : '&#xe9a2;' }}</text>
        </view>
      </view>

      <!-- 评价标签 -->
      <view class="tag-list">
        <view v-for="(tag, index) in reviewInfo.tags" :key="index" class="tag-item" :class="{ active: tag.active }"
          @click="selectTag(index)">
          {{ tag.name }}({{ tag.count }})
        </view>
      </view>

      <!-- 评价列表 -->
      <view class="review-list">
        <view class="review-item" v-for="(item, index) in reviewList" :key="index">
          <view class="user-info">
            <image class="avatar" :src="item.avatar" mode="aspectFill"></image>
            <view class="right">
              <text class="nickname">{{ item.nickname }}</text>
              <view class="stars">
                <text v-for="n in 5" :key="n" class="iconfont" :class="{ 'active': n <= item.score }">{{ n <= item.score
                  ? '&#xe9a1;' : '&#xe9a2;' }}</text>
                    <text class="score-text">{{ item.score }}.0分</text>
              </view>
            </view>
          </view>
          <view class="review-content">{{ item.content }}</view>
          <view class="review-images" v-if="item.images && item.images.length">
            <image v-for="(img, imgIndex) in item.images" :key="imgIndex" :src="img" mode="aspectFill"
              @click="previewImage(item.images, imgIndex)"></image>
          </view>
          <view class="review-time">{{ item.time }}</view>
        </view>
      </view>

      <!-- 查看更多评价 -->
      <view class="more-reviews" @click="goToReviews" v-if="reviewInfo.total > 3">
        查看全部{{ reviewInfo.total }}条评价
        <text class="iconfont">&#xe6a3;</text>
      </view>
    </view>

    <!-- 商品详情富文本 -->
    <view class="detail-content">
      <rich-text :nodes="formatRichText(productInfo.goodsDetail)" class="rich-text"></rich-text>
    </view>
    <!-- 使用购买弹窗组件 -->
    <buy-popup ref="buyPopup" :product="buyPopupProduct" :is-cart-action="isCartAction"
      :cloud-stock="productInfo.warehouseStock" :store-stock="productInfo.saleStock" :delivery-type="deliveryType"
      :quantity="buyQuantity" @confirm="handleBuyConfirm" />

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="left">
        <view class="icon-btn" @click="contactService">
          <text class="iconfont">&#xec2e;</text>
          <text>客服</text>
        </view>
        <view class="icon-btn" @click="goToCart">
          <text class="iconfont">&#xe635;</text>
          <text>购物车</text>
        </view>
      </view>
      <view class="right">
        <!-- 普通商品显示加入购物车和立即购买 -->
        <view class="add-cart" @click="addToCart">加入购物车</view>
        <view class="buy-now" @click="buyNow">立即购买</view>
      </view>
    </view>

    <!-- 添加用药指导弹窗 -->
    <uni-popup ref="medicationGuidePopup" type="bottom">
      <view class="medication-guide-popup">
        <view class="popup-title">
          用药指导
          <text class="close-btn" @click="closeMedicationGuide">×</text>
        </view>
        <scroll-view scroll-y class="popup-content" :style="{ maxHeight: '60vh' }" style="width: calc(100% - 60rpx);">
          <view class="guide-item">
            <view class="item-title">不良反应</view>
            <view class="item-content">{{ productInfo.adr || '暂无信息' }}</view>
          </view>
          <view class="guide-item">
            <view class="item-title">禁忌</view>
            <view class="item-content">{{ productInfo.taboo || '暂无信息' }}</view>
          </view>
          <view class="guide-item">
            <view class="item-title">注意事项</view>
            <view class="item-content">{{ productInfo.mna || '暂无信息' }}</view>
          </view>
          <view class="guide-item">
            <view class="item-title">规格</view>
            <view class="item-content">{{ productInfo.doseSpec || '暂无信息' }}</view>
          </view>
          <view class="guide-item">
            <view class="item-title">批准文号</view>
            <view class="item-content">{{ productInfo.appNum || '暂无信息' }}</view>
          </view>
          <view class="guide-item">
            <view class="item-title">生产厂家</view>
            <view class="item-content">{{ productInfo.factory || '暂无信息' }}</view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
    <!-- 分享海报组件 -->
    <share-poster ref="sharePoster" :product="productInfo" :userInfo="userInfo" :qrcode="shareImg"></share-poster>
  </view>
</template>

<script>
import BuyPopup from '@/components/buy-popup/buy-popup.vue'
import SharePoster from '@/components/share-poster/share-poster.vue'

const req = require("../../utils/request")

export default {
  components: {
    BuyPopup,
    SharePoster
  },

  data() {
    return {
      levels: req.getStorage("userInfo")?.levels || 0,
      id: '',
      currentSpec: 0,
      productInfo: {},
      isCartAction: true, // 是否为加入购物车操作
      countdownTime: {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0
      },
      timer: null,
      actType: null, // actType2-秒杀商品，actType4-新人商品
      endTime: null, // 秒杀结束时间
      store: null,
      shareImg: "",
      scene: "",
      storeId: "",
      pid: "",
      userInfo: {} // 用户信息
    }
  },

  computed: {
    // 轮播图数组
    bannerImages() {
      return this.productInfo.bannerImgs || []
    },

    // 是否为处方药
    isPrescriptionDrug() {
      return this.productInfo.type === 'prescription_drug'
    },

    // 是否为活动商品（秒杀或限时折扣）
    isActivityProduct() {
      return this.productInfo.actType && (this.productInfo.actType === 2 || this.productInfo.actType === 5)
    },

    // 是否为新客特价商品
    isNewCustomerProduct() {
      return this.productInfo.actType === 4
    },

    // 活动标签文本
    activityTagText() {
      if (this.productInfo.actType === 2) return '限时秒杀'
      if (this.productInfo.actType === 5) return '限时折扣'
      return ''
    },

    // 当前显示价格
    currentPrice() {
      return this.productInfo.level1Price || 0
    },

    // 是否显示原价
    hasOriginalPrice() {
      return this.productInfo.salePrice > this.productInfo.level1Price
    },

    // 是否显示金卡价格
    showGoldCardPrice() {
      return this.productInfo.level2Price && this.productInfo.level2Price < this.productInfo.level1Price
    },

    // 购买弹窗商品信息
    buyPopupProduct() {
      // 价格 2025-7-10 修改
      // price: this.getDisplayPrice(),   ==   this.productInfo.salePrice
      return {
        image: this.productInfo.img,
        // price: this.productInfo.salePrice,
        price: this.levels ? this.productInfo.level2Price : this.productInfo.level1Price,
        originalPrice: this.actType ? this.productInfo.orPrice : this.productInfo.salePrice,
        maxBuy: this.productInfo.maxBuy || 999,
        type: this.productInfo.type,
      }
    },

    // 配送类型
    deliveryType() {
      return this.pid && this.id === 27597 ? 2 : 1
    },

    // 购买数量
    buyQuantity() {
      return this.pid && this.id === 27597 ? 10 : 1
    }
  },
  onLoad(options) {
    if (options.scene) {
      this.scene = options.scene
      const [id, storeId, pid] = options.scene.split('_')
      this.id = Number(id)
      this.storeId = Number(storeId)
      this.pid = pid
      req.setStorage("pid", pid)
    }
    if (options.id) {
      this.id = Number(options.id)
      this.actType = Number(options.type)
    }
    // 获取用户信息
    this.getUserInfo()
  },
  onShow() {
    if (this.scene) {
      this.getStoreInfo().then(() => {
        this.getData()
      })
    } else {
      this.store = req.getStorage("currentStore")
      this.getData()
    }

  },
  onUnload() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    getStoreInfo() {
      return new Promise((resolve, reject) => {
        req.getRequest('/shopApi/home/<USER>', {
          pageNum: 1,
          pageSize: 10,
          id: this.storeId
        }, res => {
          console.log(res);
          this.scene = ""
          this.store = res.data.list[0]
          resolve()
        })
      })
    },
    // 获取用户信息
    getUserInfo() {
      const userInfo = req.getStorage("userInfo")
      if (userInfo) {
        this.userInfo = userInfo
      }
    },

    // 生成分享海报
    async sharePoster() {
      try {
        req.showLoading('准备中...')

        if (!this.shareImg) {
          await this.getShareQRCode()
        }

        uni.hideLoading()
        setTimeout(() => {
          this.$refs.sharePoster.open()
        }, 100)
      } catch (error) {
        console.error('生成分享海报失败:', error)
        uni.hideLoading()
        uni.showToast({
          title: '获取小程序码失败',
          icon: 'none'
        })
      }
    },

    // 获取分享二维码
    getShareQRCode() {
      return new Promise((resolve, reject) => {
        req.getRequest("/shopApi/product/programACode", {
          page: "product/detail/detail",
          scene: this.id + '_' + this.store.id,
        }, res => {
          console.log('获取小程序码成功:', res)
          this.shareImg = res
          resolve(res)
        }, false, false)
      })
    },

    // 获取商品详情
    getData() {
      if (this.actType) {
        this.getSeckillDetail()
      } else {
        this.getProductDetail()
      }
    },

    // 获取活动商品详情
    getSeckillDetail() {
      req.getRequest("/shopApi/shopApi/activity/getDetails", {
        id: this.id,
        merchantId: this.store.id,
        type: this.actType
      }, res => {
        this.processProductData(res.data)
        this.startCountdownIfNeeded(res.data)
      })
    },

    // 获取普通商品详情
    getProductDetail() {
      req.getRequest('/shopApi/product/detail', {
        id: this.id,
        merchantId: this.store.id
      }, res => {
        this.processProductData(res.data)
      })
    },

    // 处理商品数据
    processProductData(data) {
      // 处理轮播图数据
      if (data.bannerImgs && typeof data.bannerImgs === 'string') {
        data.bannerImgs = data.bannerImgs.split(',').filter(img => img.trim())
      }
      this.productInfo = data
    },

    // 开始倒计时（如果需要）
    startCountdownIfNeeded(data) {
      if (data.actType === 2 || data.actType === 5) {
        this.clearTimer()
        this.timer = req.startCountdown(data.endTime, (countdown) => {
          this.countdownTime = {
            days: countdown.days,
            hours: countdown.hours,
            minutes: countdown.minutes,
            seconds: countdown.seconds
          }
        })
      }
    },
    // 联系客服
    contactService() {
      uni.showToast({
        title: '正在接入客服...',
        icon: 'none'
      })
    },

    // 跳转购物车
    goToCart() {
      uni.switchTab({
        url: '/pages/cart/cart'
      })
    },

    // 加入购物车
    addToCart() {
      this.openBuyPopup(true)
    },

    // 立即购买
    buyNow() {
      this.openBuyPopup(false)
    },

    // 预览图片
    previewImage(images, current) {
      uni.previewImage({
        urls: images,
        current: images[current]
      })
    },

    // 跳转到评价列表页
    goToReviews() {
      uni.navigateTo({
        url: `/product/review/review?id=${this.id}`
      })
    },

    // 打开购买弹窗
    openBuyPopup(isCart) {
      this.isCartAction = isCart
      this.$refs.buyPopup.open()
    },

    // 处理购买确认
    handleBuyConfirm({ quantity, deliveryType, isCartAction }) {
      const params = this.buildPurchaseParams(quantity, deliveryType, isCartAction)

      req.postRequest("/shopApi/purchase/cart", params, res => {
        this.handlePurchaseSuccess(res, isCartAction, deliveryType, params.merchantId)
      })
    },

    // 构建购买参数
    buildPurchaseParams(quantity, deliveryType, isCartAction) {
      const params = {
        skuId: null,
        merchantId: deliveryType === 1 ? req.getStorage("currentStore").id : 60,
        quantity,
        productId: this.productInfo.id,
        mode: deliveryType,
        state: 0,
        activityType: this.actType || "",
        actId: this.actType ? this.productInfo.actId : ""
      }

      if (!isCartAction) {
        params.isDirect = 1
      }

      return params
    },

    // 处理购买成功
    handlePurchaseSuccess(res, isCartAction, deliveryType, merchantId) {
      if (isCartAction) {
        req.msg('加入购物车成功')
      } else {
        uni.navigateTo({
          url: `/shoppingCart/confirmOrder/confirmOrder?mode=${deliveryType}&goodslist=${res.data}&merchantId=${merchantId}&discountType=1`
        })
      }
    },

    // 格式化富文本内容
    formatRichText(html) {
      if (!html) return ''

      // 优化图片显示样式
      return html.replace(/<img([^>]*)>/gi, (match, group) => {
        const hasStyle = group.indexOf('style=') > -1
        const imageStyle = 'width:100%;height:auto;display:block !important;'

        if (hasStyle) {
          return match.replace(/style="([^"]*)"/i, `style="$1;${imageStyle}"`)
        } else {
          return `<img${group} style="${imageStyle}">`
        }
      })
    },

    // 显示用药指导弹窗
    showMedicationGuide() {
      this.$refs.medicationGuidePopup.open()
    },

    // 关闭用药指导弹窗
    closeMedicationGuide() {
      this.$refs.medicationGuidePopup.close()
    },

    // 跳转门店列表
    goToStoreList() {
      uni.navigateTo({
        url: '/other/storeList/storeList'
      })
    },

    // 跳转地图导航
    goMap() {
      uni.navigateTo({
        url: '/other/map/map'
      })
    },

    // 分享配置
    onShareAppMessage() {
      return {
        title: this.productInfo.goodsName,
        path: `/product/detail/detail?id=${this.id}&type=${this.actType}`,
        imageUrl: this.productInfo.img,
        success() {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          })
        },
        fail() {
          uni.showToast({
            title: '分享失败',
            icon: 'none'
          })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped src="./detail.scss"></style>