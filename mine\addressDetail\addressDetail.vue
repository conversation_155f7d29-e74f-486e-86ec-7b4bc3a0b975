<template>
    <view class="address-detail">
        <view class="form-item">
            <view class="label">联系人</view>
            <input type="text" v-model="form.name" placeholder="请输入姓名" />
        </view>

        <view class="form-item">
            <view class="label">手机号码</view>
            <input type="number" v-model="form.phone" placeholder="请输入手机号码" maxlength="11" />
        </view>

        <view class="form-item">
            <view class="label">所在地区</view>
            <picker mode="region" @change="handleRegionChange" :value="form.region">
                <view class="picker-text" :class="{ 'placeholder': !form.region.length }">
                    {{ form.region.length ? form.region.join(' ') : '请选择所在地区' }}
                </view>
            </picker>
        </view>

        <view class="form-item">
            <view class="label">详细地址</view>
            <input v-model="form.house" placeholder="请输入详细地址" />
        </view>

        <view class="form-item">
            <view class="label">标签</view>
            <view class="tag-group">
                <view v-for="(tag, index) in tags" :key="index" class="tag"
                    :class="{ 'active': form.label === tag.label || form.tag === tag.value }"
                    @click="selectTag(tag.label, tag.value)">
                    {{ tag.label }}
                </view>
            </view>
        </view>

        <view class="form-item">
            <view class="label">设为默认地址</view>
            <switch :checked="form.isDefault" @change="handleDefaultChange" color="#EA1306" />
        </view>

        <view class="btn-group">
            <button class="save-btn" @click="saveAddress">保存收货地址</button>
        </view>
    </view>
</template>

<script>
const req = require("../../utils/request.js");
var QQMapWX = require("../../utils/qqmap.js");
export default {
    data() {
        return {
            id: "",
            form: {
                name: '',
                phone: '',
                region: [],
                address: '',
                tag: 1,
                isDefault: 0
            },
            tags: [
                { label: '家', value: 1 },
                { label: '父母家', value: 2 },
                { label: '朋友家', value: 3 },
                { label: '公司', value: 4 },
                { label: '学校', value: 5 },
            ],
        }
    },
    onLoad(options) {
        QQMapWX.initMap()
        if (options.id) {
            this.id = Number(options.id);
            this.getDetail();
        }
    },
    onShow() {

    },
    methods: {
        handleRegionChange(e) {
            this.form.region = e.detail.value
        },
        selectTag(label, tag) {
            this.form.label = label;
            this.form.tag = tag;
            console.log(this.form.label, tag);
        },
        handleDefaultChange(e) {
            this.form.isDefault = this.form.isDefault ? 0 : 1
        },
        saveAddress() {
            // 表单验证
            if (!this.form.name) {
                uni.showToast({
                    title: '请输入联系人姓名',
                    icon: 'none'
                })
                return
            }
            if (!this.form.phone) {
                uni.showToast({
                    title: '请输入手机号码',
                    icon: 'none'
                })
                return
            }
            if (!/^1[3-9]\d{9}$/.test(this.form.phone)) {
                uni.showToast({
                    title: '手机号码格式不正确',
                    icon: 'none'
                })
                return
            }
            if (!this.form.region.length) {
                uni.showToast({
                    title: '请选择所在地区',
                    icon: 'none'
                })
                return
            }
            if (!this.form.house) {
                uni.showToast({
                    title: '请输入详细地址',
                    icon: 'none'
                })
                return
            }
            // TODO: 调用保存地址接口
            // console.log('保存地址', this.form);
            let parme = {
                id: this.id,
                name: this.form.name,
                phone: this.form.phone,
                address: this.form.region.join(""),
                region: this.form.region.join(","),
                house: this.form.house,
                label: this.form.label,
                isDefault: this.form.isDefault  // 1：默认 0：非默认
            }
            req.showLoading()
            // QQMapWX.geocoder(this.form.region.join("") + this.form.house, res => {
            // console.log(res, "获取地址");
            // parme.lng = res.location.lng;
            // parme.lat = res.location.lat;
            console.log(parme);
            req.postRequest("/shopApi/mp/address/save", parme, res => {
                uni.hideLoading()
                req.msg("保存成功");
                setTimeout(() => {
                    // 保存成功后返回上一页
                    uni.navigateBack();
                }, 1500);
            })
            // })

        },
        getDetail() {
            req.showLoading();
            req.getRequest("/shopApi/mp/address/get", {
                id: this.id
            }, res => {
                console.log(res, "获取地址详情");
                this.form = res.data;
                this.form.region = res.data.region.split(",");
                let index = this.tags.findIndex(item => item.label === res.data.label);
                // console.log(index);
                this.form.tag = index + 1;
                // console.log(this.form.tag);
                uni.hideLoading();
            })
        },
    }
}
</script>

<style lang="scss" scoped>
.address-detail {
    padding: 20rpx;
    background: #fff;

    .form-item {
        display: flex;
        align-items: center;
        padding: 30rpx 0;
        border-bottom: 1rpx solid #eee;

        .label {
            width: 200rpx;
            font-size: 28rpx;
            color: #333;
        }

        input,
        .picker-text {
            flex: 1;
            font-size: 28rpx;
        }

        .placeholder {
            color: #999;
        }

        .tag-group {
            flex: 1;
            display: flex;
            gap: 20rpx;
            flex-wrap: wrap;

            .tag {
                padding: 10rpx 0;
                width: 110rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                border: 1px solid #ddd;
                border-radius: 30rpx;
                font-size: 24rpx;
                color: #666;

                &.active {
                    background: #F7A19B;
                    color: #fff;
                    border: 1px solid #F7A19B;
                }
            }
        }
    }

    .btn-group {
        margin-top: 60rpx;
        padding: 0 40rpx;

        .save-btn {
            background: #EA1306;
            color: #fff;
            border-radius: 45rpx;
            height: 90rpx;
            line-height: 90rpx;
            font-size: 32rpx;
        }
    }
}
</style>